[{"know_id": 226, "type": "choice", "title": "信息系统中“逻辑炸弹”的核心特征是（    ）", "options": [{"key": "A", "name": "触发特定条件后执行破坏操作（如特定日期）"}, {"key": "B", "name": "通过邮件附件传播（类似病毒）"}, {"key": "C", "name": "仅破坏硬件设备（非数据）"}, {"key": "D", "name": "实时监控用户操作（间谍软件）"}], "answer": "A", "parse": "逻辑炸弹是预设触发条件（如特定日期、操作）后执行破坏（如删除数据）的恶意程序，区别于病毒的主动传播特性。", "source": "第4章4.1.1节‘信息系统安全风险’——《信息技术必修二-信息系统与社会》", "difficulty": 0.6}, {"know_id": 226, "type": "multiple", "title": "以下属于“环境安全风险”的是（    ）", "options": [{"key": "A", "name": "机房空调故障导致服务器过热"}, {"key": "B", "name": "地震损坏数据中心基础设施"}, {"key": "C", "name": "员工误操作删除数据库"}, {"key": "D", "name": "黑客攻击导致系统瘫痪"}], "answer": "AB", "parse": "环境风险指物理环境因素（如温度、自然灾害）对系统的威胁；误操作、黑客攻击属于人为风险。", "source": "第4章4.1.1节‘信息系统安全风险’——《信息技术必修二-信息系统与社会》", "difficulty": 0.5}, {"know_id": 226, "type": "judge", "title": "“零日漏洞”是指已被公开且厂商已修复的系统漏洞（    ）", "answer": "否", "parse": "零日漏洞（Zero-day）指未被厂商发现或修复的漏洞，攻击者可利用其发起未被防御的攻击。", "source": "第4章4.1.1节‘信息系统安全风险’——《信息技术必修二-信息系统与社会》", "difficulty": 0.7}, {"know_id": 226, "type": "choice", "title": "以下属于“技术防范措施”的是（    ）", "options": [{"key": "A", "name": "对员工进行安全意识培训"}, {"key": "B", "name": "部署Web应用防火墙（WAF）"}, {"key": "C", "name": "制定《数据访问审批制度》"}, {"key": "D", "name": "与第三方签订保密协议"}], "answer": "B", "parse": "技术防范通过技术手段实现（如WAF过滤恶意请求）；培训、制度、协议属于管理措施。", "source": "第4章4.1.2节‘信息系统安全防范’——《信息技术必修二-信息系统与社会》", "difficulty": 0.4}, {"know_id": 226, "type": "multiple", "title": "以下符合“数据脱敏”要求的是（    ）", "options": [{"key": "A", "name": "将用户手机号显示为“138****1234”"}, {"key": "B", "name": "直接存储用户银行卡号明文"}, {"key": "C", "name": "对测试环境中的用户姓名进行随机替换"}, {"key": "D", "name": "在日志中记录完整的身份证号"}], "answer": "AC", "parse": "数据脱敏通过隐藏部分敏感信息（如手机号打码）或替换（如随机姓名）降低泄露风险；明文存储、记录完整身份证号违反脱敏要求。", "source": "第4章4.1.2节‘信息系统安全防范’——《信息技术必修二-信息系统与社会》", "difficulty": 0.6}, {"know_id": 226, "type": "judge", "title": "“蜜罐技术”通过模拟易受攻击的系统诱捕攻击者（    ）", "answer": "是", "parse": "蜜罐（Honeypot）是一种主动防御技术，通过部署模拟系统吸引攻击者，分析攻击手段以增强防护。", "source": "第4章4.1.2节‘信息系统安全防范’——《信息技术必修二-信息系统与社会》", "difficulty": 0.5}, {"know_id": 226, "type": "choice", "title": "以下不符合“安全使用信息系统”规范的是（    ）", "options": [{"key": "A", "name": "使用“密码管理器”生成12位以上复杂密码"}, {"key": "B", "name": "在手机设置中关闭“允许安装未知来源应用”"}, {"key": "C", "name": "在公共电脑登录邮箱后仅关闭浏览器"}, {"key": "D", "name": "定期检查账号登录记录（如最近登录设备）"}], "answer": "C", "parse": "公共电脑登录后仅关闭浏览器无法清除缓存，应退出登录并清除浏览器数据，否则可能泄露信息。", "source": "第4章4.1.3节‘安全使用信息系统’——《信息技术必修二-信息系统与社会》", "difficulty": 0.4}, {"know_id": 226, "type": "multiple", "title": "以下属于“防范勒索软件”的措施是（    ）", "options": [{"key": "A", "name": "定期备份重要数据到离线存储"}, {"key": "B", "name": "打开未知邮件中的附件"}, {"key": "C", "name": "及时更新系统和软件补丁"}, {"key": "D", "name": "安装杀毒软件并开启实时监控"}], "answer": "ACD", "parse": "备份数据、更新补丁、安装杀毒软件可防范勒索软件；打开未知附件会触发攻击。", "source": "第4章4.1.3节‘安全使用信息系统’——《信息技术必修二-信息系统与社会》", "difficulty": 0.6}, {"know_id": 226, "type": "judge", "title": "“双因素认证（2FA）”仅需输入密码即可完成验证（    ）", "answer": "否", "parse": "双因素认证需两种独立验证方式（如密码+短信验证码/指纹），仅密码不满足双因素要求。", "source": "第4章4.1.3节‘安全使用信息系统’——《信息技术必修二-信息系统与社会》", "difficulty": 0.4}, {"know_id": 226, "type": "choice", "title": "以下属于“社会安全威胁”的是（    ）", "options": [{"key": "A", "name": "某用户手机被盗导致微信账号被盗"}, {"key": "B", "name": "黑客攻击某省电力调度系统导致停电"}, {"key": "C", "name": "企业员工误将内部文档上传至公开网络"}, {"key": "D", "name": "用户因密码简单导致网银账户被盗"}], "answer": "B", "parse": "电力调度系统属于关键信息基础设施，攻击导致停电影响公共利益，属于社会安全威胁；其他选项为个体或企业风险。", "source": "第4章4.2.1节‘社会安全威胁和应对’——《信息技术必修二-信息系统与社会》", "difficulty": 0.5}, {"know_id": 226, "type": "multiple", "title": "以下属于“网络空间安全事件”的是（    ）", "options": [{"key": "A", "name": "某平台用户数据泄露（涉及1000万人）"}, {"key": "B", "name": "某学校官网被植入恶意代码（挂马攻击）"}, {"key": "C", "name": "用户因操作失误删除个人文件"}, {"key": "D", "name": "某企业服务器因过载导致短暂宕机"}], "answer": "AB", "parse": "数据泄露、网站挂马属于安全事件（由攻击或漏洞引发）；操作失误、服务器过载属于意外事件，非安全事件。", "source": "第4章4.2.1节‘社会安全威胁和应对’——《信息技术必修二-信息系统与社会》", "difficulty": 0.6}, {"know_id": 226, "type": "judge", "title": "“网络安全应急响应”仅需技术团队参与（    ）", "answer": "否", "parse": "应急响应需技术团队（修复漏洞）、管理团队（沟通协调）、法务团队（合规处理）等多部门协作。", "source": "第4章4.2.1节‘社会安全威胁和应对’——《信息技术必修二-信息系统与社会》", "difficulty": 0.5}, {"know_id": 226, "type": "choice", "title": "以下违反“个人信息安全行为规范”的是（    ）", "options": [{"key": "A", "name": "在APP首次启动时拒绝授权“通讯录”权限（非必要）"}, {"key": "B", "name": "将旧手机通过专业工具彻底格式化（数据不可恢复）"}, {"key": "C", "name": "在社交平台发布“某酒店入住记录”（含他人信息）"}, {"key": "D", "name": "使用“隐私保护”功能隐藏手机相册中的敏感照片"}], "answer": "C", "parse": "未经允许发布他人入住记录侵犯隐私，违反个人信息安全规范；其他行为均为合理保护措施。", "source": "第4章4.2.2节‘个人信息安全行为规范’——《信息技术必修二-信息系统与社会》", "difficulty": 0.4}, {"know_id": 226, "type": "multiple", "title": "以下属于“个人信息敏感信息”的是（    ）", "options": [{"key": "A", "name": "身份证号码"}, {"key": "B", "name": "电子邮箱地址"}, {"key": "C", "name": "生物识别信息（如指纹）"}, {"key": "D", "name": "社交平台昵称"}], "answer": "AC", "parse": "敏感信息指一旦泄露可能对个人权益造成严重影响的信息（如身份证、指纹）；邮箱、昵称属于一般个人信息。", "source": "第4章4.2.2节‘个人信息安全行为规范’——《信息技术必修二-信息系统与社会》", "difficulty": 0.6}, {"know_id": 226, "type": "judge", "title": "“匿名化处理”后的信息仍需遵守个人信息保护规定（    ）", "answer": "否", "parse": "匿名化处理（无法识别特定个人）后的信息不属于个人信息，无需遵守《个人信息保护法》。", "source": "第4章4.2.2节‘个人信息安全行为规范’——《信息技术必修二-信息系统与社会》", "difficulty": 0.7}, {"know_id": 226, "type": "choice", "title": "以下违反“信息社会道德准则”的是（    ）", "options": [{"key": "A", "name": "在学术论文中引用开源代码并标注来源"}, {"key": "B", "name": "在论坛发布“某明星离婚”不实消息（博关注）"}, {"key": "C", "name": "帮助同学调试程序并指导正确方法"}, {"key": "D", "name": "拒绝参与“网络水军”攻击他人"}], "answer": "B", "parse": "发布不实消息误导公众，违反诚信和尊重他人的道德准则；其他行为符合道德要求。", "source": "第4章4.2.3节‘信息社会道德准则’——《信息技术必修二-信息系统与社会》", "difficulty": 0.4}, {"know_id": 226, "type": "multiple", "title": "以下符合“网络信息传播道德”的是（    ）", "options": [{"key": "A", "name": "转发新闻前核实信息来源（如官方媒体）"}, {"key": "B", "name": "在评论区对不同观点进行人身攻击"}, {"key": "C", "name": "分享科普文章时标注原作者"}, {"key": "D", "name": "传播“某产品致癌”谣言（无证据）"}], "answer": "AC", "parse": "核实信息、标注原作者符合传播道德；人身攻击、传播谣言违反道德准则。", "source": "第4章4.2.3节‘信息社会道德准则’——《信息技术必修二-信息系统与社会》", "difficulty": 0.5}, {"know_id": 226, "type": "judge", "title": "“网络虚拟财产”不受法律保护（    ）", "answer": "否", "parse": "《民法典》明确网络虚拟财产属于民事权利客体，受法律保护（如游戏账号、虚拟货币）。", "source": "第4章4.2.3节‘信息社会道德准则’——《信息技术必修二-信息系统与社会》", "difficulty": 0.6}, {"know_id": 226, "type": "choice", "title": "根据《网络安全法》，以下不属于“关键信息基础设施运营者”义务的是（    ）", "options": [{"key": "A", "name": "对重要系统和数据进行容灾备份"}, {"key": "B", "name": "将用户信息存储在境外服务器"}, {"key": "C", "name": "定期进行安全检测和风险评估"}, {"key": "D", "name": "制定网络安全事件应急预案"}], "answer": "B", "parse": "关键信息基础设施运营者需在境内存储用户信息（确需出境的需安全评估），境外存储违反《网络安全法》。", "source": "第4章4.2.4节‘信息社会法律法规’——《信息技术必修二-信息系统与社会》", "difficulty": 0.7}, {"know_id": 226, "type": "multiple", "title": "以下属于《数据安全法》规定的“数据安全义务”的是（    ）", "options": [{"key": "A", "name": "建立数据安全管理制度"}, {"key": "B", "name": "对数据处理活动进行风险评估"}, {"key": "C", "name": "随意向境外提供重要数据"}, {"key": "D", "name": "采取技术措施保障数据安全"}], "answer": "ABD", "parse": "《数据安全法》要求数据处理者建立制度、评估风险、采取技术措施；向境外提供重要数据需符合安全评估要求，不可随意操作。", "source": "第4章4.2.4节‘信息社会法律法规’——《信息技术必修二-信息系统与社会》", "difficulty": 0.7}, {"know_id": 226, "type": "judge", "title": "根据《个人信息保护法》，个人有权要求删除其个人信息（    ）", "answer": "是", "parse": "《个人信息保护法》规定个人享有“删除权”，在特定情形下（如信息已无必要）可要求处理者删除个人信息。", "source": "第4章4.2.4节‘信息社会法律法规’——《信息技术必修二-信息系统与社会》", "difficulty": 0.5}]