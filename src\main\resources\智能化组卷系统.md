# 智能化组卷系统 

**摘要**  
随着教育信息化的深化，传统人工组卷方式在效率、个性化和多约束满足等方面的局限性日益凸显。本文提出一套基于遗传算法的智能化组卷系统，通过前端灵活的参数配置与后端智能算法协同，实现多目标、多约束的自动化试卷生成。研究聚焦系统全流程技术细节，涵盖前端请求参数设计、后端服务层数据流转、核心遗传算法引擎实现，以及动态规划调整器（DPAdjuster）、多样性过滤器（DiversityFilter）等关键组件的微观代码级分析。通过详细解析遗传算法的染色体设计、适应度函数构造、选择交叉变异算子，动态规划对总分的精确调整机制，以及多样性过滤对题目新鲜度的保障，揭示系统在满足用户定制化需求与优化试卷质量方面的核心技术。本文为教育信息化领域的智能组卷系统设计提供了完整的技术参考。  

**关键词**：智能化组卷；遗传算法；动态规划；多样性过滤；教育信息化  


## 1. 引言  
教育测评是教学反馈与能力评估的核心环节，其质量直接影响教学效果。传统人工组卷依赖教师经验，存在效率低、个性化不足、难以兼顾多维度约束（如知识点覆盖、难度分布、题型配比）等问题。随着人工智能技术的发展，智能化组卷系统通过引入进化计算（如遗传算法）、动态规划等优化方法，能够自动化生成符合用户需求的高质量试卷，成为教育信息化的重要研究方向。  

本文聚焦一套自主设计的智能化组卷系统，系统通过前端界面接收用户定义的复杂组卷需求（包括知识点配置、题型数量、难度分布等），后端通过服务层协调数据流转，最终调用遗传算法引擎搜索最优题目组合，并辅以动态规划调整总分、多样性过滤保证题目新鲜度。本文从前端请求参数设计出发，逐层解析后端服务逻辑与核心算法实现，重点揭示遗传算法引擎的微观机制，为同类系统开发提供技术参考。  


## 2. 系统整体架构  
智能化组卷系统采用分层架构设计，由用户交互层、应用接口层、核心服务层与数据持久层组成，各层职责明确，协同完成从用户需求到试卷生成的全流程。系统架构如图1所示（注：实际论文中需补充架构图，此处以文字描述替代）。  

### 2.1 用户交互层（前端）  
用户交互层提供图形化界面，支持用户定义组卷的核心参数，包括试卷标题、知识点配置、题型数量、题型分值、难度分布、总分目标、最小题目重用间隔及认知层次分布等。前端通过HTTP POST请求将参数传递至后端，触发组卷流程。  

### 2.2 应用接口层（后端控制器）  
应用接口层由`PaperController`实现，作为系统入口接收前端请求（端点为`/api/papers/generate`），使用Spring MVC框架将JSON请求体反序列化为`PaperGenerationRequest` DTO对象，并通过JSR 303/380 Bean Validation注解（如`@NotBlank`、`@Min`）完成参数校验。校验失败时返回HTTP 400错误，成功则调用服务层处理。  

### 2.3 核心服务层  
核心服务层包含业务逻辑编排与智能算法实现，是系统的核心模块。  

#### 2.3.1 试卷生成服务（`PaperGenerationService`）  
`PaperGenerationService`接口（实现类为`PaperGenerationServiceImpl`）负责协调组卷流程：  
- 记录请求日志，追踪组卷过程；  
- 调用`PaperGenerationEngine`执行智能组卷；  
- 对引擎返回的题目列表进行后处理（如题型数量强制校验、分值设置、质量评估）；  
- 构建`Paper`实体并持久化至数据库，生成最终响应。  

#### 2.3.2 核心组卷引擎（`PaperGenerationEngine`）  
`PaperGenerationEngine`封装遗传算法核心逻辑，位于`com.edu.maizi_edu_sys.service.engine`包内。其依赖`GeneticSolver`（遗传算法求解器）、`DPAdjuster`（动态规划调整器）、`DiversityFilter`（多样性过滤器）等组件，通过预处理候选题目池、调用遗传算法搜索最优解、动态规划调整总分，最终输出符合要求的题目列表。  

### 2.4 数据持久层  
数据持久层通过MyBatis Mapper（如`TopicMapper`、`TopicEnhancementDataMapper`）与数据库交互，存储题目信息（知识点、难度、题型）、题目增强数据（历史使用时间、认知层次）及历史试卷数据，为组卷过程提供数据支撑。  


## 3. 前端请求参数设计（`PaperGenerationRequest` DTO）  
前端通过`PaperGenerationRequest` DTO传递组卷需求，其字段设计需覆盖用户对试卷的多维约束与优化目标。以下为关键字段解析（表1为字段总结，实际论文中需补充表格）：  

### 3.1 基础信息  
- **`title`（String）**：试卷标题，不可为空（`@NotBlank`），示例为“2024学年第一学期计算机网络期末考试”。  

### 3.2 知识点配置（`knowledgePointConfigs`）  
`knowledgePointConfigs`为`KnowledgePointConfigRequest`列表，定义各知识点的出题要求：  
- `knowledgeId`（Long）：知识点唯一标识，非空（`@NotNull`）；  
- `questionCount`（Integer）：该知识点需选取的题目数量，非空且≥0（`@Min(0)`）。若为`null`，则题目数量由全局配置或算法动态确定；  
- `includeShortAnswer`（Boolean）：是否包含简答题，非空（`@NotNull`）。  

### 3.3 全局题型约束  
- **`topicTypeCounts`（Map<String, Integer>）**：全局题型数量配置（如`{"singleChoice": 20, "multipleChoice": 10}`），非空（`@NotNull`），允许空Map（表示无全局限制）；  
- **`typeScoreMap`（Map<String, Integer>）**：题型分值配置（如`{"singleChoice": 2, "multipleChoice": 4}`），非空（`@NotNull`）；  
- **`totalScore`（Integer）**：试卷总分目标，非空且≥0（`@Min(0)`）。  

### 3.4 难度与认知层次分布  
- **`difficultyCriteria`（Map<String, Double>）**：难度分布（如`{"easy": 0.3, "medium": 0.5, "hard": 0.2}`），非空（`@NotNull`），各难度占比之和为1；  
- **`cognitiveLevelCriteria`（Map<String, Double>）**：认知层次分布（基于布鲁姆分类法，如`{"remember": 0.3, "understand": 0.3, "apply": 0.4}`），若未显式设置则使用默认值。  

### 3.5 题目重用约束  
- **`minReuseIntervalDays`（Integer，可选）**：最小题目重用间隔天数，若为`null`则不启用，避免近期使用过的题目重复出现。  


## 4. 后端处理流程  
后端处理流程从控制器接收请求开始，经服务层协调，最终通过引擎生成试卷并持久化。以下为关键步骤：  

### 4.1 控制器层：请求接收与校验  
`PaperController`通过`POST /api/papers/generate`接口接收请求，使用Spring MVC反序列化请求体为`PaperGenerationRequest`对象，通过`@Valid`注解触发级联校验（包括`KnowledgePointConfigRequest`的嵌套校验）。校验失败时返回HTTP 400错误，成功则调用`PaperGenerationService.generatePaper()`。  

### 4.2 服务层：业务编排与引擎调用  
`PaperGenerationServiceImpl.generatePaper()`方法执行以下步骤：  
1. **日志记录**：记录请求参数（如知识点ID、目标总分）；  
2. **调用引擎**：传递`PaperGenerationRequest`至`PaperGenerationEngine.generatePaper()`，获取候选题目列表；  
3. **题型数量强制校验**：调用`enforceTypeCounts()`确保题型数量符合`topicTypeCounts`，裁剪多余题目并记录题量不足警告；  
4. **结果处理**：  
   - 检查题目列表是否为空，为空则返回失败响应；  
   - 统计实际题型数量，与请求值对比生成警告；  
   - 根据`typeScoreMap`设置题目分值；  
   - 调用`qualityScorer`评估试卷质量（基于难度分布、知识点覆盖等指标）；  
   - 构建`Paper`实体（包含标题、总分、题目ID列表、配置JSON），通过`paperRepository.save()`持久化；  
   - 生成`PaperGenerationResponse`（包含试卷ID、预览URL、警告信息）。  


## 5. 核心组卷引擎与关键组件微观分析  

### 5.1 `PaperGenerationEngine` 核心流程  
`PaperGenerationEngine`是系统的智能核心，其`generatePaper()`方法通过预处理候选题目池、调用遗传算法搜索最优解、动态规划调整总分，最终输出高质量试卷。  

#### 5.1.1 候选题目池构建与预处理  
引擎首先根据`knowledgePointConfigs`构建候选题目池：  
- **预留池**：对`questionCount`非`null`的知识点，调用`topicMapper.findTopicsByKnowledgePointAndCriteria()`查询题目，从全局题型数量中扣除预留题量；  
- **通用池**：对`questionCount`为`null`的知识点，查询所有合格题目加入通用池；  
- **合并与过滤**：合并预留池与通用池，通过`enhancementDataMapper`加载题目增强数据（如历史使用时间、认知层次），移除重复题目，并应用`minReuseIntervalDays`过滤（排除近期使用过的题目）和`diversityFilter`（保证题目多样性）。  


### 5.2 遗传算法求解器（`GeneticSolver`）  
`GeneticSolver`是引擎的核心组件，通过模拟自然选择与遗传机制搜索最优题目组合。  

#### 5.2.1 类结构与参数配置  
`GeneticSolver`为Spring管理的Service组件（`@Service`），依赖可配置的GA参数（如种群大小`POPULATION_SIZE=100`、最大迭代次数`MAX_GENERATIONS=200`）与适应度权重（如总分权重`WEIGHT_SCORE=0.4`），使用`Random`生成随机数，通过`ForkJoinPool`并行化适应度评估以提升效率。  

#### 5.2.2 核心方法`solve()`流程  
`solve()`方法接收候选题目列表、目标总分、题型约束等参数，执行以下步骤：  

1. **初始化种群**：生成初始种群（`List<Chromosome>`），每个染色体为`BitSet`类型（长度为候选题目数），随机设置基因位（1表示选中题目）。预留题目通过适应度函数强制包含（若未选中则适应度极低）。  

2. **进化迭代**：  
   - **适应度评估**：并行计算每个染色体的适应度（通过`evaluateChromosome()`），按适应度降序排序；  
   - **终止条件**：达到最大迭代次数（`MAX_GENERATIONS`）或提前收敛（适应度≥0.98且迭代次数≥`MIN_GENERATIONS=30`）；  
   - **选择、交叉、变异**：  
     - 精英保留：最优个体直接进入下一代；  
     - 锦标赛选择（`tournamentSelection()`）：从`TOURNAMENT_SIZE=5`个个体中选择高适应度父代；  
     - 单点交叉（概率`CROSSOVER_RATE=0.8`）：交换父代染色体片段生成子代；  
     - 位变异（概率`MUTATION_RATE=0.1`）：随机翻转基因位，维持种群多样性。  

3. **结果提取**：进化结束后，最优染色体解码为题目列表（选中基因位对应的题目）。  

#### 5.2.3 适应度函数设计  
适应度函数通过加权和综合评估试卷质量，关键维度包括：  
- **总分匹配度**：实际总分与目标分的接近程度（使用高斯函数计算，差异越小适应度越高）；  
- **题型数量匹配度**：各题型实际数量与目标值的差异（通过`calculateDistributionFitness()`计算百分比差异平方和）；  
- **难度分布匹配度**：题目难度占比与目标分布的吻合度；  
- **认知层次匹配度**：认知层次占比与目标分布的吻合度；  
- **知识点覆盖度**：实际覆盖知识点数与目标数的比例（未全覆盖则惩罚）；  
- **题目质量**：基于增强数据（如历史答对率）的平均质量分。  


### 5.3 动态规划调整器（`DPAdjuster`）  
`DPAdjuster`用于在遗传算法输出后调整总分，解决总分过高问题（当前仅支持“减法”调整）。其核心方法`adjust()`流程如下：  

1. **当前总分计算**：遍历题目列表，根据`typeScoreMap`累加题目分值得到`currentScore`；  
2. **调整逻辑**：  
   - 若`currentScore == targetScore`，直接返回原列表；  
   - 若`currentScore < targetScore`，记录警告并返回原列表（不支持加法调整）；  
   - 若`currentScore > targetScore`，调用`findOptimalSubset()`执行0/1背包问题求解：  
     - 初始化动态规划表`dp[i][j]`（表示前`i`个题目能否凑出总分`j`）；  
     - 填充DP表，通过状态转移方程`dp[i][j] = dp[i-1][j] || (topicScore≤j && dp[i-1][j-topicScore])`；  
     - 若`dp[n][targetScore]`为真，回溯DP表构造最优子集（移除多余题目）。  


### 5.4 多样性过滤器（`DiversityFilter`）  
`DiversityFilter`通过`filter()`方法保证题目新鲜度，核心逻辑如下：  
- 若`minReuseIntervalDays`有效（>0），计算重用阈值`reuseThresholdDate`（当前时间减去`minReuseIntervalDays`）；  
- 过滤候选题目，仅保留上次使用时间早于`reuseThresholdDate`的题目（无增强数据或未记录使用时间的题目默认保留）；  
- 返回过滤后的题目列表，避免短时间内重复使用相同题目。  


## 6. 算法关键设计与考量  
### 6.1 适应度函数的多目标平衡  
适应度函数通过加权和综合多维度约束（总分、题型、难度等），权重参数（如`WEIGHT_SCORE=0.4`）可配置，支持不同场景下的优化侧重（如考试更重视难度分布，练习更重视知识点覆盖）。  

### 6.2 约束处理策略  
- **硬约束**：预留题目通过适应度函数强制包含（未选中则适应度极低）；题型数量通过`enforceTypeCounts()`后处理强制满足；  
- **软约束**：难度分布、认知层次等通过适应度函数引导算法优化，允许一定偏差。
