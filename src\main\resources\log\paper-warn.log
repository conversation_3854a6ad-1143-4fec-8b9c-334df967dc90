2025-05-27 19:42:55.964 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type choice. Required: 20, Available: 0
2025-05-27 19:42:55.965 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Type count mismatch for choice: current=0, target=20. Cannot perform strict adjustment.
2025-05-27 19:42:55.967 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type SINGLE_CHOICE, still short by 20 topics
2025-05-27 19:42:55.968 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Unable to find a subset of topics that exactly matches target score 40. Returning original list.
2025-05-27 19:42:55.980 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Unable to find a subset of topics that exactly matches target score 40. Returning original list.
2025-05-27 19:42:57.321 [http-nio-8081-exec-10] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 19:43:14.419 [http-nio-8081-exec-2] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 19:47:36.013 [http-nio-8081-exec-1] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 21:37:54.907 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type multiple. Required: 30, Available: 0
2025-05-27 21:37:54.907 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type short. Required: 2, Available: 0
2025-05-27 21:37:54.907 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type judge. Required: 10, Available: 0
2025-05-27 21:37:54.907 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type choice. Required: 50, Available: 0
2025-05-27 21:37:54.907 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Type count mismatch for multiple: current=0, target=30. Cannot perform strict adjustment.
2025-05-27 21:37:54.909 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type SINGLE_CHOICE, still short by 50 topics
2025-05-27 21:37:54.909 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type JUDGE, still short by 10 topics
2025-05-27 21:37:54.909 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type MULTIPLE_CHOICE, still short by 30 topics
2025-05-27 21:37:54.909 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type SHORT, still short by 2 topics
2025-05-27 21:37:54.910 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (276) is less than target score (300). Upward adjustment is not supported. Returning original list.
2025-05-27 21:37:54.926 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (276) is less than target score (300). Upward adjustment is not supported. Returning original list.
2025-05-27 21:38:31.103 [http-nio-8081-exec-8] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 21:38:35.693 [http-nio-8081-exec-2] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 加载SimSun字体失败: fonts/simsun.ttf is not a valid TTF or OTF file.
2025-05-27 21:38:35.693 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: fonts/simsun.ttf is not a valid TTF or OTF file.
2025-05-27 21:38:53.177 [http-nio-8081-exec-8] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 21:46:57.413 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type multiple. Required: 20, Available: 0
2025-05-27 21:46:57.413 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type short. Required: 2, Available: 0
2025-05-27 21:46:57.413 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type judge. Required: 10, Available: 0
2025-05-27 21:46:57.414 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Not enough topics of type choice. Required: 40, Available: 0
2025-05-27 21:46:57.414 [DP-Adjuster-Thread] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Type count mismatch for multiple: current=0, target=20. Cannot perform strict adjustment.
2025-05-27 21:46:57.414 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type SINGLE_CHOICE, still short by 40 topics
2025-05-27 21:46:57.414 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type JUDGE, still short by 10 topics
2025-05-27 21:46:57.414 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type MULTIPLE_CHOICE, still short by 20 topics
2025-05-27 21:46:57.414 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Unable to fully satisfy requirement for type SHORT, still short by 2 topics
2025-05-27 21:46:57.414 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (216) is less than target score (300). Upward adjustment is not supported. Returning original list.
2025-05-27 21:46:57.424 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (216) is less than target score (300). Upward adjustment is not supported. Returning original list.
2025-05-27 21:47:00.604 [http-nio-8081-exec-9] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 21:47:04.382 [http-nio-8081-exec-5] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 加载SimSun字体失败: fonts/simsun.ttf is not a valid TTF or OTF file.
2025-05-27 21:47:04.382 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: fonts/simsun.ttf is not a valid TTF or OTF file.
2025-05-27 21:48:17.456 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
2025-05-27 21:48:51.772 [http-nio-8081-exec-4] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Error parsing difficulty distribution JSON: Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Double>` from Array value (token `JsonToken.START_ARRAY`)
 at [Source: (String)"[{"easy": 0.2, "hard": 0.1, "medium": 0.7}]"; line: 1, column: 1]
