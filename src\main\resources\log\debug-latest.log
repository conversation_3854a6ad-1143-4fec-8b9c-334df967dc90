2025-05-19 00:12:31.545 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 00:12:44.473 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:13:08.813 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:13:10.588 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:19:24.915 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:55:55.816 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 00:56:12.655 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:12.674 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:12.674 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:12.694 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 00:56:12.694 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:12.695 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:12.707 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:12.710 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 00:56:12.740 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:14.498 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:32.622 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:34.623 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:56:55.253 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:57:13.270 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:57:15.167 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.377 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.382 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 00:58:34.382 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.384 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.388 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.388 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.388 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.388 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 00:58:34.390 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 00:58:36.802 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.825 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.828 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 01:13:32.828 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.829 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.832 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 01:13:32.834 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.834 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.834 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:32.834 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:13:36.921 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:14:12.416 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:14:17.361 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:14:17.850 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:19.890 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:19.908 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:22.514 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:22.530 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.752 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.754 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 01:16:24.754 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.755 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.759 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 01:16:24.767 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.767 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.776 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:24.779 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:25.947 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:25.959 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:36.777 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:36.790 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.473 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.476 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 01:16:52.476 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.477 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.482 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 01:16:52.491 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.491 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.497 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:52.499 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:54.219 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:16:56.639 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:13.496 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:14.876 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:14.889 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:14.897 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:18.320 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.333 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.345 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.354 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.783 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.796 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.806 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.962 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.972 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:17:19.980 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:18:23.476 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:18:24.360 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:25:58.379 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 01:27:26.438 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 01:29:28.254 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 01:30:47.970 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 01:36:10.538 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 01:37:41.713 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.723 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.723 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.723 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.723 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.752 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 01:37:41.752 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.754 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:41.782 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 01:37:44.922 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:37:45.454 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:46.353 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 01:44:54.026 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:54.031 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:54.033 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:54.063 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...h3zQ
2025-05-19 01:44:54.063 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:54.064 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:54.079 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:54.086 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 01:44:54.118 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:56.508 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 01:44:56.914 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:27.069 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-19 11:50:37.275 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:37.296 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:42.264 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-05-19 11:50:43.457 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.457 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.468 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...TGnw
2025-05-19 11:50:43.468 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...TGnw
2025-05-19 11:50:43.468 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.468 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.469 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.469 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.473 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 11:50:43.473 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 11:50:43.476 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.477 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...TGnw
2025-05-19 11:50:43.477 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.478 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.481 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 11:50:46.168 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.169 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...TGnw
2025-05-19 11:50:46.169 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.170 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.173 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-19 11:50:46.177 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.177 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.184 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-19 11:50:46.184 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-19 11:50:46.187 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-19 11:50:46.188 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-19 11:50:46.230 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.267 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:46.268 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-19 11:50:46.268 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-19 11:50:46.268 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-19 11:50:46.268 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-19 11:50:47.112 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:50:49.556 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:51:28.952 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.153 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.155 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.156 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:29.160 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:29.176 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SINGLE_CHOICE'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SINGLE_CHOICE': 'singleChoice'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'MULTIPLE_CHOICE'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'MULTIPLE_CHOICE': 'multipleChoice'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'JUDGMENT'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'JUDGMENT': 'judgment'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'FILL_IN_BLANKS'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'FILL_IN_BLANKS': 'FILL_IN_BLANKS'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'SHORT_ANSWER'
2025-05-19 11:51:29.177 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'SHORT_ANSWER': 'shortAnswer'
2025-05-19 11:51:29.178 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Updating existing TopicEnhancementData for Topic ID: 173688 to usageCount=2, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.180 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173691 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.183 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173700 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.185 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173707 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.186 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173711 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.187 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173714 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.189 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173716 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.190 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173718 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.191 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173719 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.192 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173725 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.193 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173731 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.195 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173734 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.196 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173735 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.197 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173738 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.198 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173739 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.199 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173741 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.201 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 173742 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.202 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 174370 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.202 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Inserting new TopicEnhancementData for Topic ID: 174372 with usageCount=1, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:29.204 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Updating existing TopicEnhancementData for Topic ID: 174375 to usageCount=2, lastUsedTime=2025-05-19T11:51:29.177
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:30.653 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.076 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.076 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.076 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:31.077 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:32.947 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.649 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.756 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.757 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.765 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.765 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.766 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.766 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.768 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.768 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.769 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.769 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.770 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.770 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.771 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.771 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.772 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.772 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.773 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.773 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.774 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-19 11:51:51.774 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-19 11:51:51.775 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.775 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.776 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.776 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.776 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.776 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.776 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.776 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.777 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.778 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.778 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-19 11:51:51.778 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-19 11:51:51.778 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
