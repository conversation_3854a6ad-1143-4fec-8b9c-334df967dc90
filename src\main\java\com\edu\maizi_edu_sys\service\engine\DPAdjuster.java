package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.util.TopicTypeUtils;

import com.edu.maizi_edu_sys.entity.Topic;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态规划调整器组件 (DPAdjuster)。
 * <p>
 * 该组件的核心职责是使用动态规划算法对初步选定的题目列表进行微调，
 * 目的是在保持题目集合尽可能接近原选集的基础上，使其总分精确匹配目标分数。
 * 这通常作为遗传算法或其他启发式选题算法的后处理步骤，以弥补它们在总分精确性上的不足。
 * </p>
 * <p>
 * 主要应用场景是在试卷生成过程中，当遗传算法选出的题目总分与期望总分有偏差时，
 * DPAdjuster 尝试通过移除部分题目或（在更复杂的实现中）替换题目来达到精确总分。
 * 当前实现侧重于通过"移除"多余分值的题目来向下调整至目标分数。
 * </p>
 */
@Component
@Slf4j
public class DPAdjuster {

    /**
     * 使用动态规划调整选定的题目列表，以使其总分精确匹配目标分数 {@code targetScore}。
     * <p>
     * 调整策略：
     * <ul>
     *   <li>如果当前题目总分已等于目标分数，则直接返回原列表。</li>
     *   <li>如果当前题目总分小于目标分数，当前实现不进行向上调整（假设之前的选题步骤已是最优），直接返回原列表。</li>
     *   <li>如果当前题目总分大于目标分数，则尝试通过移除一部分题目，使得剩余题目的总分恰好等于 {@code targetScore}。
     *       这是通过经典的0/1背包问题的动态规划变种来实现的，目标是找到一个子集，其元素（题目分数）之和为特定值。</li>
     * </ul>
     * </p>
     *
     * @param topics 候选题目列表，通常是遗传算法或其他初步选择算法的结果。
     * @param targetScore 期望达到的试卷确切总分。
     * @param typeScores 题目类型分数映射表。
     * @return 调整后的题目列表。如果无法精确匹配，则根据实现策略可能返回原列表或最接近的调整结果。
     *         当前实现下，若无法精确匹配（特别是在需要减分的情况下），会返回原始列表。
     */
    public List<Topic> adjust(List<Topic> topics, int targetScore, Map<String, Integer> typeScores) {
        if (topics == null || topics.isEmpty()) {
            log.warn("DPAdjuster: Input topics list is null or empty. Returning empty list.");
            return Collections.emptyList();
        }

        // Calculate current score using the provided typeScores map
        int currentScore = topics.stream()
                                 .mapToInt(t -> getTopicScore(t, typeScores))
                                 .sum();
        log.info("DPAdjuster: Adjusting topics. Current score: {}, Target score: {}. Number of topics: {}",
                 currentScore, targetScore, topics.size());

        if (currentScore == targetScore) {
            log.info("DPAdjuster: Current score already matches target score. No adjustment needed.");
            return topics;
        }

        if (currentScore < targetScore) {
            log.warn("DPAdjuster: Current score ({}) is less than target score ({}). Upward adjustment is not supported. Returning original list.",
                     currentScore, targetScore);
            return topics;
        }

        log.info("DPAdjuster: Current score ({}) is greater than target ({}). Attempting to find a subset that sums to target score.", currentScore, targetScore);
        return findOptimalSubset(topics, targetScore, typeScores);
    }

    /**
     * 获取题目的分数，优先使用typeScores中的配置，如果没有则使用题目自身的分数
     *
     * @param topic 题目
     * @param typeScores 题型分数映射
     * @return 题目分数
     */
    private int getTopicScore(Topic topic, Map<String, Integer> typeScores) {
        String normalizedType = TopicTypeUtils.normalize(topic.getType());
        return typeScores.getOrDefault(normalizedType,
                                      topic.getScore() != null ? topic.getScore() : 0);
    }

    /**
     * 增强版调整方法，同时考虑分数和题型约束
     * <p>
     * 此方法尝试在满足目标分数的同时，尽可能满足题型分布的要求
     * </p>
     *
     * @param topics 候选题目列表
     * @param targetScore 目标总分
     * @param typeScores 题目类型分数映射表
     * @param targetTypeCounts 各题型目标数量
     * @return 同时满足分数和题型约束的题目列表
     */
    public List<Topic> adjust(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                             Map<String, Integer> targetTypeCounts) {
        if (topics == null || topics.isEmpty()) {
            log.warn("DPAdjuster: Input topics list is null or empty. Returning empty list.");
            return Collections.emptyList();
        }

        // Calculate current score using the provided typeScores map
        int currentScore = topics.stream()
                                 .mapToInt(t -> getTopicScore(t, typeScores))
                                 .sum();
        log.info("DPAdjuster: Adjusting topics with type constraints. Current score: {}, Target score: {}. Number of topics: {}",
                 currentScore, targetScore, topics.size());

        // 题型约束检查
        Map<String, Integer> currentTypeCounts = topics.stream()
            .collect(Collectors.groupingBy(
                topic -> TopicTypeUtils.normalize(topic.getType()),
                Collectors.counting()))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> e.getValue().intValue()));

        log.info("DPAdjuster: Current type counts: {}, Target type counts: {}", currentTypeCounts, targetTypeCounts);

        // 检查当前分数是否已经满足要求
        if (currentScore == targetScore) {
            log.info("DPAdjuster: Current score already matches target score.");

            // 检查题型约束是否满足
            boolean typeConstraintsSatisfied = true;
            for (Map.Entry<String, Integer> entry : targetTypeCounts.entrySet()) {
                String type = entry.getKey();
                int targetCount = entry.getValue();
                int currentCount = currentTypeCounts.getOrDefault(type, 0);

                if (targetCount > 0 && currentCount != targetCount) {
                    typeConstraintsSatisfied = false;
                    log.warn("DPAdjuster: Type constraint not satisfied for {}: required={}, actual={}",
                            type, targetCount, currentCount);
                }
            }

            if (typeConstraintsSatisfied) {
                log.info("DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.");
                return topics;
            } else {
                log.info("DPAdjuster: Score matches but type constraints are not satisfied. Applying type-preserving optimization.");
            }
        }

        // 使用考虑题型约束的优化方法
        return findOptimalSubsetWithTypeConstraints(topics, targetScore, typeScores, targetTypeCounts);
    }

    /**
     * 寻找给定题目列表的一个子集，使其总分精确等于 {@code targetScore}。
     * <p>
     * 此方法使用动态规划（0/1背包问题的变种）来解决子集和问题。
     * DP状态 {@code dp[i][j]} 表示是否可以使用前 {@code i} 个题目达到总分 {@code j}。
     * </p>
     * <p>
     * 算法步骤：
     * <ol>
     *   <li>初始化DP表：{@code dp[i][0]} 为 true（总能用任意题目达到0分，即不选）。</li>
     *   <li>填充DP表：对于每道题目 {@code topics[i-1]} 和每个可能的分数 {@code j}：
     *     <ul>
     *       <li>不选当前题目：{@code dp[i][j] = dp[i-1][j]}</li>
     *       <li>选择当前题目（如果其分数 {@code topicScore <= j}）：{@code dp[i][j] = dp[i][j] || dp[i-1][j - topicScore]}</li>
     *     </ul>
     *   </li>
     *   <li>检查是否能达到目标分数：如果 {@code dp[n][targetScore]} 为 false，则无法精确匹配，返回原始列表。</li>
     *   <li>回溯DP表：如果可以达到目标分数，则从 {@code dp[n][targetScore]} 开始回溯，构造出实际选择的题目列表。</li>
     * </ol>
     * </p>
     *
     * @param topics 原始题目列表。
     * @param targetScore 目标总分数。
     * @param typeScores 题目类型分数映射表。
     * @return 一个新的题目列表，其总分精确等于 {@code targetScore}。
     *         如果无法找到这样的子集，则返回原始 {@code topics} 列表。
     */
    private List<Topic> findOptimalSubset(List<Topic> topics, int targetScore, Map<String, Integer> typeScores) {
        int n = topics.size();
        boolean[][] dp = new boolean[n + 1][targetScore + 1];

        for (int i = 0; i <= n; i++) {
            dp[i][0] = true;
        }

        for (int i = 1; i <= n; i++) {
            Topic currentTopic = topics.get(i - 1);
            // Get score using our helper method
            int topicScore = getTopicScore(currentTopic, typeScores);
            if (topicScore < 0) topicScore = 0; // Ensure non-negative for DP

            for (int j = 1; j <= targetScore; j++) {
                dp[i][j] = dp[i - 1][j];
                if (topicScore <= j && topicScore > 0) {
                    dp[i][j] = dp[i][j] || dp[i - 1][j - topicScore];
                }
            }
        }

        if (!dp[n][targetScore]) {
            log.warn("DPAdjuster: Unable to find a subset of topics that exactly matches target score {}. Returning original list.", targetScore);
            return topics;
        }

        List<Topic> result = new ArrayList<>();
        int remainingScore = targetScore;

        for (int i = n; i > 0 && remainingScore > 0; i--) {
            Topic currentTopic = topics.get(i - 1);
            int topicScore = getTopicScore(currentTopic, typeScores);
            if (topicScore < 0) topicScore = 0;

            if (topicScore > 0 && remainingScore >= topicScore && dp[i - 1][remainingScore - topicScore] && !dp[i-1][remainingScore]) {
                result.add(currentTopic);
                remainingScore -= topicScore;
            }
        }
        Collections.reverse(result);
        log.info("DPAdjuster: Successfully found a subset of {} topics matching target score {}. Original list size: {}.",
                 result.size(), targetScore, topics.size());
        return result;
    }

    // Removed mapTopicType method as we now use TopicTypeUtils.normalize

    /**
     * 寻找给定题目列表的一个子集，使其总分精确等于 {@code targetScore}。
     * <p>
     * 此方法使用动态规划（0/1背包问题的变种）来解决子集和问题。
     * DP状态 {@code dp[i][j]} 表示是否可以使用前 {@code i} 个题目达到总分 {@code j}。
     * </p>
     * <p>
     * 算法步骤：
     * <ol>
     *   <li>初始化DP表：{@code dp[i][0]} 为 true（总能用任意题目达到0分，即不选）。</li>
     *   <li>填充DP表：对于每道题目 {@code topics[i-1]} 和每个可能的分数 {@code j}：
     *     <ul>
     *       <li>不选当前题目：{@code dp[i][j] = dp[i-1][j]}</li>
     *       <li>选择当前题目（如果其分数 {@code topicScore <= j}）：{@code dp[i][j] = dp[i][j] || dp[i-1][j - topicScore]}</li>
     *     </ul>
     *   </li>
     *   <li>检查是否能达到目标分数：如果 {@code dp[n][targetScore]} 为 false，则无法精确匹配，返回原始列表。</li>
     *   <li>回溯DP表：如果可以达到目标分数，则从 {@code dp[n][targetScore]} 开始回溯，构造出实际选择的题目列表。</li>
     * </ol>
     * </p>
     *
     * @param topics 原始题目列表。
     * @param targetScore 目标总分数。
     * @param typeScores 题目类型分数映射表。
     * @param targetTypeCounts 各题型目标数量
     * @return 一个新的题目列表，其总分精确等于 {@code targetScore}。
     *         如果无法找到这样的子集，则返回原始 {@code topics} 列表。
     */
    private List<Topic> findOptimalSubsetWithTypeConstraints(List<Topic> topics, int targetScore,
                                                            Map<String, Integer> typeScores,
                                                            Map<String, Integer> targetTypeCounts) {
        // 性能优化: 如果题型约束很宽松，直接使用传统DP算法
        if (targetTypeCounts == null || targetTypeCounts.isEmpty()) {
            log.info("DPAdjuster: No type constraints provided, using traditional DP algorithm");
            return findOptimalSubset(topics, targetScore, typeScores);
        }

        log.info("DPAdjuster: Starting strict type-preserving optimization");

        // STEP 1: 确保首先满足题型约束 - 创建一个基础解
        List<Topic> mandatoryTopics = new ArrayList<>();
        Map<String, List<Topic>> topicsByType = topics.stream()
            .collect(Collectors.groupingBy(t -> TopicTypeUtils.normalize(t.getType()), Collectors.toList()));
        Map<String, Integer> currentTypeCounts = new HashMap<>();
        int currentTotalScore = 0;

        // 为每种题型选择精确数量的题目
        for (Map.Entry<String, Integer> entry : targetTypeCounts.entrySet()) {
            String type = entry.getKey();
            int targetCount = entry.getValue();

            if (targetCount <= 0) continue; // 跳过不需要的题型

            List<Topic> availableOfType = topicsByType.getOrDefault(type, Collections.emptyList());
            int availableCount = availableOfType.size();

            if (availableCount < targetCount) {
                log.warn("DPAdjuster: Not enough topics of type {}. Required: {}, Available: {}",
                         type, targetCount, availableCount);
                // 选择所有可用的
                mandatoryTopics.addAll(availableOfType);
                currentTypeCounts.put(type, availableCount);

                // 累加分数
                for (Topic t : availableOfType) {
                    currentTotalScore += getTopicScore(t, typeScores);
                }
            } else {
                // 有足够的题目，需要选择最佳子集
                // 按分数排序，首选选择分数接近或等于目标平均分的题目
                int targetTypeScore = (int)Math.round((double)targetScore * targetCount /
                                                    targetTypeCounts.values().stream().mapToInt(Integer::intValue).sum());
                int targetAvgScore = targetTypeScore / targetCount;

                // 按与目标平均分的接近程度排序
                List<Topic> sortedOfType = new ArrayList<>(availableOfType);
                final int finalTargetAvgScore = targetAvgScore;
                sortedOfType.sort(Comparator.comparingInt(t -> {
                    int score = typeScores.getOrDefault(type, t.getScore() != null ? t.getScore() : 0);
                    return Math.abs(score - finalTargetAvgScore);
                }));

                // 选择所需数量的题目
                List<Topic> selectedOfType = sortedOfType.subList(0, targetCount);
                mandatoryTopics.addAll(selectedOfType);
                currentTypeCounts.put(type, targetCount);

                // 累加分数
                for (Topic t : selectedOfType) {
                    currentTotalScore += getTopicScore(t, typeScores);
                }
            }
        }

        log.info("DPAdjuster: Initial selection with strict type constraints: {} topics, score={}, type distribution={}",
                 mandatoryTopics.size(), currentTotalScore, currentTypeCounts);

        // STEP 2: 如果当前总分与目标分数有偏差，尝试调整但保持题型数量不变
        int scoreDiff = currentTotalScore - targetScore;

        if (Math.abs(scoreDiff) <= 3) {
            // 如果分数接近，接受轻微偏差，优先保证题型分布
            log.info("DPAdjuster: Score {} is close to target {} (diff={}). Accepting minor score deviation to maintain type distribution.",
                     currentTotalScore, targetScore, scoreDiff);
            return mandatoryTopics;
        }

        // 尝试通过题目替换来调整分数
        if (scoreDiff != 0) {
            // 首先尝试严格保持题型数量的调整
            boolean strictSuccess = adjustScoreWithStrictTypePreserving(mandatoryTopics, targetScore, typeScores,
                                       currentTotalScore, currentTypeCounts, targetTypeCounts,
                                       topicsByType);

            // 如果严格调整失败，尝试更灵活的调整方式
            if (!strictSuccess) {
                log.info("DPAdjuster: Strict type-preserving adjustment failed, trying more flexible approach");
                adjustScoreWithTypePreserving(mandatoryTopics, targetScore, typeScores,
                                           currentTotalScore, currentTypeCounts, targetTypeCounts,
                                           topicsByType);
            }

            // 重新计算调整后分数
            int adjustedScore = mandatoryTopics.stream()
                .mapToInt(t -> getTopicScore(t, typeScores))
                .sum();

            // 重新计算调整后的题型分布
            Map<String, Long> adjustedTypeCounts = mandatoryTopics.stream()
                .collect(Collectors.groupingBy(
                    t -> TopicTypeUtils.normalize(t.getType()),
                    Collectors.counting()));

            log.info("DPAdjuster: After type-preserving adjustment: score={} (target={}), type distribution={}",
                     adjustedScore, targetScore, adjustedTypeCounts);
        }

        return mandatoryTopics;
    }

    /**
     * 严格保持题型数量的前提下调整总分
     * 此方法确保每种题型的数量与目标数量完全一致
     */
    private boolean adjustScoreWithStrictTypePreserving(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                                                     int currentScore, Map<String, Integer> currentTypeCounts,
                                                     Map<String, Integer> targetTypeCounts,
                                                     Map<String, List<Topic>> allAvailableTopicsByType) {
        if (currentScore == targetScore) return true; // 已经达到目标

        boolean needToIncrease = currentScore < targetScore;
        int targetDiff = Math.abs(currentScore - targetScore);
        boolean improved = false;

        // 按题型分组当前选择的题目
        Map<String, List<Topic>> selectedTopicsByType = topics.stream()
            .collect(Collectors.groupingBy(t -> TopicTypeUtils.normalize(t.getType()), Collectors.toList()));

        // 对于每种题型，尝试替换题目以调整分数，但严格保持题型数量
        for (String type : targetTypeCounts.keySet()) {
            int targetTypeCount = targetTypeCounts.getOrDefault(type, 0);
            if (targetTypeCount <= 0) continue;

            int currentTypeCount = currentTypeCounts.getOrDefault(type, 0);
            if (currentTypeCount != targetTypeCount) {
                log.warn("DPAdjuster: Type count mismatch for {}: current={}, target={}. Cannot perform strict adjustment.",
                         type, currentTypeCount, targetTypeCount);
                return false; // 题型数量不匹配，无法进行严格调整
            }

            List<Topic> selectedOfType = selectedTopicsByType.getOrDefault(type, new ArrayList<>());
            List<Topic> availableOfType = allAvailableTopicsByType.getOrDefault(type, new ArrayList<>());

            // 从可选池中移除已选择的题目
            availableOfType = new ArrayList<>(availableOfType);
            availableOfType.removeAll(selectedOfType);

            if (availableOfType.isEmpty()) {
                log.debug("DPAdjuster: No additional topics of type {} available for replacement", type);
                continue;
            }

            if (needToIncrease) {
                // 需要增加分数：尝试用高分题替换低分题
                // 按分数升序排序已选题目（低分在前）
                selectedOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));
                // 按分数降序排序可选题目（高分在前）
                availableOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic lowScoreTopic = selectedOfType.get(i);
                    int lowScore = getTopicScore(lowScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic highScoreTopic = it.next();
                        int highScore = getTopicScore(highScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0) {
                            if (scoreDelta == targetDiff) {
                                // 完美匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(lowScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, highScoreTopic);
                                    log.debug("Perfect match! Replaced topic of type {} with score {} with topic with score {}",
                                             type, lowScore, highScore);
                                    return true; // 完美匹配，直接返回成功
                                }
                            } else if (scoreDelta < targetDiff) {
                                // 部分匹配，继续寻找更好的匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(lowScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, highScoreTopic);
                                    improved = true;
                                    targetDiff -= scoreDelta;
                                    log.debug("Replaced topic of type {} with score {} with topic with score {}, remaining diff: {}",
                                             type, lowScore, highScore, targetDiff);
                                    break; // 继续下一个低分题目
                                }
                            }
                            // 如果scoreDelta > targetDiff，跳过此题目，寻找更合适的
                        }
                    }
                }
            } else {
                // 需要减少分数：尝试用低分题替换高分题
                // 按分数降序排序已选题目（高分在前）
                selectedOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));
                // 按分数升序排序可选题目（低分在前）
                availableOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic highScoreTopic = selectedOfType.get(i);
                    int highScore = getTopicScore(highScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic lowScoreTopic = it.next();
                        int lowScore = getTopicScore(lowScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0) {
                            if (scoreDelta == targetDiff) {
                                // 完美匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(highScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, lowScoreTopic);
                                    log.debug("Perfect match! Replaced topic of type {} with score {} with topic with score {}",
                                             type, highScore, lowScore);
                                    return true; // 完美匹配，直接返回成功
                                }
                            } else if (scoreDelta < targetDiff) {
                                // 部分匹配，继续寻找更好的匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(highScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, lowScoreTopic);
                                    improved = true;
                                    targetDiff -= scoreDelta;
                                    log.debug("Replaced topic of type {} with score {} with topic with score {}, remaining diff: {}",
                                             type, highScore, lowScore, targetDiff);
                                    break; // 继续下一个高分题目
                                }
                            }
                            // 如果scoreDelta > targetDiff，跳过此题目，寻找更合适的
                        }
                    }
                }
            }
        }

        return improved;
    }

    /**
     * 在保持题型分布的前提下调整总分
     * 此方法允许题型数量有一定的灵活性
     */
    private boolean adjustScoreWithTypePreserving(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                                                int currentScore, Map<String, Integer> currentTypeCounts,
                                                Map<String, Integer> targetTypeCounts,
                                                Map<String, List<Topic>> allAvailableTopicsByType) {
        if (currentScore == targetScore) return true; // 已经达到目标

        boolean needToIncrease = currentScore < targetScore;
        int targetDiff = Math.abs(currentScore - targetScore);
        boolean improved = false;

        // 按题型分组当前选择的题目
        Map<String, List<Topic>> selectedTopicsByType = topics.stream()
            .collect(Collectors.groupingBy(t -> TopicTypeUtils.normalize(t.getType()), Collectors.toList()));

        // 对于每种题型，尝试替换题目以调整分数
        for (String type : targetTypeCounts.keySet()) {
            int targetTypeCount = targetTypeCounts.getOrDefault(type, 0);
            if (targetTypeCount <= 0) continue;

            int currentTypeCount = currentTypeCounts.getOrDefault(type, 0);
            if (currentTypeCount < targetTypeCount) continue; // 题型数量不足，不能调整

            List<Topic> selectedOfType = selectedTopicsByType.getOrDefault(type, new ArrayList<>());
            List<Topic> availableOfType = allAvailableTopicsByType.getOrDefault(type, new ArrayList<>());

            // 从可选池中移除已选择的题目
            availableOfType = new ArrayList<>(availableOfType);
            availableOfType.removeAll(selectedOfType);

            if (needToIncrease) {
                // 需要增加分数：尝试用高分题替换低分题
                selectedOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));
                availableOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic lowScoreTopic = selectedOfType.get(i);
                    int lowScore = getTopicScore(lowScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic highScoreTopic = it.next();
                        int highScore = getTopicScore(highScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0 && scoreDelta <= targetDiff) {
                            // 可以替换
                            it.remove(); // 从可选池移除
                            int topicIndex = topics.indexOf(lowScoreTopic);
                            if (topicIndex >= 0) {
                                topics.set(topicIndex, highScoreTopic);
                                improved = true;
                                targetDiff -= scoreDelta;
                                log.debug("Replaced topic of type {} with score {} with topic with score {}, remaining diff: {}",
                                         type, lowScore, highScore, targetDiff);

                                // 如果已达到目标，退出
                                if (targetDiff == 0) return true;
                                break;
                            }
                        }
                    }
                }
            } else {
                // 需要减少分数：尝试用低分题替换高分题
                selectedOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));
                availableOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic highScoreTopic = selectedOfType.get(i);
                    int highScore = getTopicScore(highScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic lowScoreTopic = it.next();
                        int lowScore = getTopicScore(lowScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0 && scoreDelta <= targetDiff) {
                            // 可以替换
                            it.remove(); // 从可选池移除
                            int topicIndex = topics.indexOf(highScoreTopic);
                            if (topicIndex >= 0) {
                                topics.set(topicIndex, lowScoreTopic);
                                improved = true;
                                targetDiff -= scoreDelta;
                                log.debug("Replaced topic of type {} with score {} with topic with score {}, remaining diff: {}",
                                         type, highScore, lowScore, targetDiff);

                                // 如果已达到目标，退出
                                if (targetDiff == 0) return true;
                                break;
                            }
                        }
                    }
                }
            }
        }

        return improved;
    }
}