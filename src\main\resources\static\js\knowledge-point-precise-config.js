/**
 * 知识点精确配置模块
 * 支持每个知识点的精确题型数量控制
 */

/**
 * 渲染知识点精确配置界面
 */
function renderKnowledgePointPreciseConfig(knowledgePoints) {
    const container = $('#knowledge-point-precise-config');

    if (!knowledgePoints || knowledgePoints.length === 0) {
        container.html('<div class="alert alert-warning">请先选择知识点</div>');
        return;
    }

    let html = `
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> 知识点精确配置
                    <small class="text-muted">（精确控制每个知识点的题型数量）</small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="enablePreciseConfig">
                            <label class="form-check-label" for="enablePreciseConfig">
                                <strong>启用精确配置模式</strong>
                                <small class="text-muted d-block">开启后可以精确控制每个知识点的题型数量，关闭则使用传统的总数量分配模式</small>
                            </label>
                        </div>
                    </div>
                </div>

                <div id="precise-config-content" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>精确配置说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li>可以为每个知识点单独设置各题型的数量</li>
                            <li>设置为0表示该知识点不出该题型的题目</li>
                            <li>系统会严格按照设置的数量进行组卷</li>
                            <li>如果某知识点的某题型题目不足，会在结果中提示</li>
                        </ul>
                    </div>

                    <div class="row">
    `;

    // 为每个知识点生成配置界面
    knowledgePoints.forEach((kp, index) => {
        html += `
            <div class="col-md-6 mb-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-book"></i> ${kp.name}
                            <small class="float-right">ID: ${kp.id}</small>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- 单选题 -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">单选题</label>
                                <div class="input-group input-group-sm">
                                    <input type="number"
                                           class="form-control precise-config-input"
                                           id="kp-${kp.id}-single-choice"
                                           data-kp-id="${kp.id}"
                                           data-type="singleChoice"
                                           min="0"
                                           value="0"
                                           placeholder="0">
                                    <span class="input-group-text">题</span>
                                </div>
                            </div>

                            <!-- 多选题 -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">多选题</label>
                                <div class="input-group input-group-sm">
                                    <input type="number"
                                           class="form-control precise-config-input"
                                           id="kp-${kp.id}-multiple-choice"
                                           data-kp-id="${kp.id}"
                                           data-type="multipleChoice"
                                           min="0"
                                           value="0"
                                           placeholder="0">
                                    <span class="input-group-text">题</span>
                                </div>
                            </div>

                            <!-- 判断题 -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">判断题</label>
                                <div class="input-group input-group-sm">
                                    <input type="number"
                                           class="form-control precise-config-input"
                                           id="kp-${kp.id}-judge"
                                           data-kp-id="${kp.id}"
                                           data-type="judge"
                                           min="0"
                                           value="0"
                                           placeholder="0">
                                    <span class="input-group-text">题</span>
                                </div>
                            </div>

                            <!-- 填空题 -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">填空题</label>
                                <div class="input-group input-group-sm">
                                    <input type="number"
                                           class="form-control precise-config-input"
                                           id="kp-${kp.id}-fill"
                                           data-kp-id="${kp.id}"
                                           data-type="fill"
                                           min="0"
                                           value="0"
                                           placeholder="0">
                                    <span class="input-group-text">题</span>
                                </div>
                            </div>

                            <!-- 简答题（独立计算） -->
                            <div class="col-md-12 mb-3">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-edit"></i> 简答题配置（独立计算）
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input short-answer-switch"
                                                   type="checkbox"
                                                   id="kp-${kp.id}-short-switch"
                                                   data-kp-id="${kp.id}">
                                            <label class="form-check-label" for="kp-${kp.id}-short-switch">
                                                启用简答题
                                            </label>
                                        </div>
                                        <div class="short-answer-config" id="kp-${kp.id}-short-config" style="display: none;">
                                            <label class="form-label">简答题数量（额外的，不计入总题量）</label>
                                            <div class="input-group input-group-sm">
                                                <input type="number"
                                                       class="form-control precise-config-input short-answer-count"
                                                       id="kp-${kp.id}-short-answer"
                                                       data-kp-id="${kp.id}"
                                                       data-type="shortAnswer"
                                                       min="0"
                                                       value="0"
                                                       placeholder="0">
                                                <span class="input-group-text">题（额外）</span>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle"></i>
                                                简答题数量不计入该知识点的总体量，是额外增加的题目
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 知识点总计 -->
                        <div class="border-top pt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>该知识点总题数：</strong>
                                    <span class="badge badge-primary" id="kp-${kp.id}-total">0</span>
                                </div>
                                <div class="col-md-6">
                                    <button type="button"
                                            class="btn btn-sm btn-outline-secondary"
                                            onclick="clearKnowledgePointConfig(${kp.id})">
                                        <i class="fas fa-eraser"></i> 清空
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                    </div>

                    <!-- 全局统计 -->
                    <div class="card bg-light mt-4">
                        <div class="card-body">
                            <h6><i class="fas fa-chart-bar"></i> 全局统计</h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <div class="h4 text-primary" id="global-single-choice-count">0</div>
                                        <small>单选题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <div class="h4 text-success" id="global-multiple-choice-count">0</div>
                                        <small>多选题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <div class="h4 text-warning" id="global-judge-count">0</div>
                                        <small>判断题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <div class="h4 text-info" id="global-fill-count">0</div>
                                        <small>填空题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <div class="h4 text-danger" id="global-short-answer-count">0</div>
                                        <small>简答题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <div class="h4 text-dark" id="global-total-count">0</div>
                                        <small>总题数</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-primary" onclick="applyPreciseConfig()">
                            <i class="fas fa-check"></i> 应用精确配置
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="resetPreciseConfig()">
                            <i class="fas fa-undo"></i> 重置配置
                        </button>
                        <button type="button" class="btn btn-info ml-2" onclick="previewPreciseConfig()">
                            <i class="fas fa-eye"></i> 预览配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.html(html);

    // 绑定事件
    bindPreciseConfigEvents();
}

/**
 * 绑定精确配置相关事件
 */
function bindPreciseConfigEvents() {
    // 启用/禁用精确配置
    $('#enablePreciseConfig').change(function() {
        const enabled = $(this).is(':checked');
        $('#precise-config-content').toggle(enabled);

        if (enabled) {
            showToast('已启用精确配置模式', 'success');
        } else {
            showToast('已禁用精确配置模式', 'info');
        }
    });

    // 简答题开关
    $('.short-answer-switch').change(function() {
        const kpId = $(this).data('kp-id');
        const enabled = $(this).is(':checked');
        const configDiv = $(`#kp-${kpId}-short-config`);

        if (enabled) {
            configDiv.show();
            // 设置默认值
            $(`#kp-${kpId}-short-answer`).val(1);
        } else {
            configDiv.hide();
            $(`#kp-${kpId}-short-answer`).val(0);
        }

        updateKnowledgePointTotal(kpId);
        updateGlobalStatistics();
    });

    // 题目数量输入变化
    $('.precise-config-input').on('input', function() {
        updateKnowledgePointTotal($(this).data('kp-id'));
        updateGlobalStatistics();
    });
}

/**
 * 更新知识点总计
 */
function updateKnowledgePointTotal(kpId) {
    let basicTotal = 0;
    let shortAnswerCount = 0;

    // 计算基础题型总数（不包括简答题）
    $(`[data-kp-id="${kpId}"]`).each(function() {
        const value = parseInt($(this).val()) || 0;
        if ($(this).data('type') === 'shortAnswer') {
            shortAnswerCount = value;
        } else {
            basicTotal += value;
        }
    });

    // 更新显示：基础题目 + 简答题（额外）
    const totalDisplay = shortAnswerCount > 0 ?
        `${basicTotal} + ${shortAnswerCount}(简答)` :
        `${basicTotal}`;

    $(`#kp-${kpId}-total`).html(totalDisplay);
}

/**
 * 更新全局统计
 */
function updateGlobalStatistics() {
    const stats = {
        singleChoice: 0,
        multipleChoice: 0,
        judge: 0,
        fill: 0,
        shortAnswer: 0
    };

    $('.precise-config-input').each(function() {
        const type = $(this).data('type');
        const value = parseInt($(this).val()) || 0;

        if (stats.hasOwnProperty(type)) {
            stats[type] += value;
        }
    });

    // 更新显示
    $('#global-single-choice-count').text(stats.singleChoice);
    $('#global-multiple-choice-count').text(stats.multipleChoice);
    $('#global-judge-count').text(stats.judge);
    $('#global-fill-count').text(stats.fill);
    $('#global-short-answer-count').text(stats.shortAnswer);

    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    $('#global-total-count').text(total);
}

/**
 * 清空知识点配置
 */
function clearKnowledgePointConfig(kpId) {
    $(`[data-kp-id="${kpId}"]`).val(0);
    updateKnowledgePointTotal(kpId);
    updateGlobalStatistics();
    showToast(`已清空知识点 ${kpId} 的配置`, 'info');
}

/**
 * 应用精确配置
 */
function applyPreciseConfig() {
    if (!$('#enablePreciseConfig').is(':checked')) {
        showToast('请先启用精确配置模式', 'warning');
        return;
    }

    const configs = collectPreciseConfigs();

    if (configs.length === 0) {
        showToast('没有配置任何题目数量', 'warning');
        return;
    }

    // 验证配置
    const validation = validatePreciseConfigs(configs);
    if (!validation.valid) {
        showToast(validation.message, 'error');
        return;
    }

    // 应用到主界面
    applyConfigsToMainInterface(configs);

    showToast('精确配置已应用到主界面', 'success');
}

/**
 * 收集精确配置
 */
function collectPreciseConfigs() {
    const configs = [];
    const kpIds = new Set();

    $('.precise-config-input').each(function() {
        const kpId = $(this).data('kp-id');
        kpIds.add(kpId);
    });

    kpIds.forEach(kpId => {
        const config = {
            knowledgeId: kpId,
            singleChoiceCount: parseInt($(`#kp-${kpId}-single-choice`).val()) || 0,
            multipleChoiceCount: parseInt($(`#kp-${kpId}-multiple-choice`).val()) || 0,
            judgeCount: parseInt($(`#kp-${kpId}-judge`).val()) || 0,
            fillCount: parseInt($(`#kp-${kpId}-fill`).val()) || 0,
            shortAnswerCount: parseInt($(`#kp-${kpId}-short-answer`).val()) || 0
        };

        // 计算基础题目数量（不包括简答题）
        config.questionCount = config.singleChoiceCount + config.multipleChoiceCount +
                              config.judgeCount + config.fillCount;

        // 简答题独立设置
        config.includeShortAnswer = config.shortAnswerCount > 0;

        // 只要有基础题目或简答题就添加配置
        if (config.questionCount > 0 || config.shortAnswerCount > 0) {
            configs.push(config);
        }
    });

    return configs;
}

/**
 * 验证精确配置
 */
function validatePreciseConfigs(configs) {
    // 检查是否有配置
    if (configs.length === 0) {
        return { valid: false, message: '请至少为一个知识点配置题目数量' };
    }

    // 检查总题目数量
    const totalQuestions = configs.reduce((sum, config) => sum + config.questionCount, 0);
    if (totalQuestions === 0) {
        return { valid: false, message: '总题目数量不能为0' };
    }

    if (totalQuestions > 200) {
        return { valid: false, message: '总题目数量不能超过200题' };
    }

    return { valid: true, message: '配置验证通过' };
}

/**
 * 应用配置到主界面
 */
function applyConfigsToMainInterface(configs) {
    // 这里需要根据实际的主界面结构来实现
    // 示例：更新知识点配置表单

    // 清空现有配置
    $('.knowledge-point-config').remove();

    // 应用新配置
    configs.forEach(config => {
        // 更新或创建知识点配置项
        updateMainInterfaceKnowledgePoint(config);
    });

    // 更新全局题型统计
    updateMainInterfaceGlobalStats(configs);
}

/**
 * 重置精确配置
 */
function resetPreciseConfig() {
    $('.precise-config-input').val(0);
    $('.precise-config-input').each(function() {
        updateKnowledgePointTotal($(this).data('kp-id'));
    });
    updateGlobalStatistics();
    showToast('已重置所有精确配置', 'info');
}

/**
 * 预览精确配置
 */
function previewPreciseConfig() {
    const configs = collectPreciseConfigs();

    if (configs.length === 0) {
        showToast('没有配置任何题目数量', 'warning');
        return;
    }

    let previewHtml = '<h5>精确配置预览</h5><div class="table-responsive"><table class="table table-sm">';
    previewHtml += '<thead><tr><th>知识点ID</th><th>单选</th><th>多选</th><th>判断</th><th>填空</th><th>简答</th><th>总计</th></tr></thead><tbody>';

    configs.forEach(config => {
        previewHtml += `
            <tr>
                <td>${config.knowledgeId}</td>
                <td>${config.singleChoiceCount}</td>
                <td>${config.multipleChoiceCount}</td>
                <td>${config.judgeCount}</td>
                <td>${config.fillCount}</td>
                <td>${config.shortAnswerCount}</td>
                <td><strong>${config.questionCount}</strong></td>
            </tr>
        `;
    });

    const totalQuestions = configs.reduce((sum, config) => sum + config.questionCount, 0);
    previewHtml += `</tbody><tfoot><tr class="table-info"><td><strong>总计</strong></td><td><strong>${configs.reduce((sum, c) => sum + c.singleChoiceCount, 0)}</strong></td><td><strong>${configs.reduce((sum, c) => sum + c.multipleChoiceCount, 0)}</strong></td><td><strong>${configs.reduce((sum, c) => sum + c.judgeCount, 0)}</strong></td><td><strong>${configs.reduce((sum, c) => sum + c.fillCount, 0)}</strong></td><td><strong>${configs.reduce((sum, c) => sum + c.shortAnswerCount, 0)}</strong></td><td><strong>${totalQuestions}</strong></td></tr></tfoot></table></div>`;

    Swal.fire({
        title: '配置预览',
        html: previewHtml,
        width: '80%',
        confirmButtonText: '确定'
    });
}
