function showConfirmDialog(message, onConfirm, onCancel) {
    const dialog = document.createElement('div');
    dialog.className = 'dialog-overlay';
    dialog.innerHTML = `
        <div class="dialog">
            <p>${message}</p>
            <div class="dialog-buttons">
                <button class="btn-cancel">取消</button>
                <button class="btn-confirm">确认</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(dialog);
    
    const confirmBtn = dialog.querySelector('.btn-confirm');
    const cancelBtn = dialog.querySelector('.btn-cancel');
    
    confirmBtn.addEventListener('click', () => {
        onConfirm?.();
        document.body.removeChild(dialog);
    });
    
    cancelBtn.addEventListener('click', () => {
        onCancel?.();
        document.body.removeChild(dialog);
    });
} 