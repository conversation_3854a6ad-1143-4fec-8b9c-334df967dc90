package com.edu.maizi_edu_sys.util;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * HTML转PDF工具类
 * 支持两种转换方式：
 * 1. 使用iText的XMLWorker
 * 2. 使用Flying Saucer
 */
@Slf4j
public class HtmlToPdfConverter {

    /**
     * 使用iText的XMLWorker将HTML转换为PDF
     *
     * @param html HTML内容
     * @return PDF资源
     */
    public static Resource convertHtmlToPdfWithIText(String html) {
        try {
            Document document = new Document();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, baos);
            document.open();

            // 添加CSS样式，支持数学公式
            String htmlWithStyles = addStyles(html);

            // 转换HTML为PDF
            InputStream is = new ByteArrayInputStream(htmlWithStyles.getBytes(StandardCharsets.UTF_8));
            XMLWorkerHelper.getInstance().parseXHtml(writer, document, is, StandardCharsets.UTF_8);
            document.close();

            log.info("成功使用iText XMLWorker将HTML转换为PDF，HTML长度: {}", html.length());
            return new ByteArrayResource(baos.toByteArray());
        } catch (DocumentException | IOException e) {
            log.error("使用iText XMLWorker将HTML转换为PDF时出错: {}", e.getMessage(), e);
            return createErrorPdf("使用iText XMLWorker将HTML转换为PDF时出错: " + e.getMessage());
        }
    }

    /**
     * 使用Flying Saucer将HTML转换为PDF
     * 这种方法对CSS支持更好，特别是对于复杂的布局和样式
     *
     * @param html HTML内容
     * @return PDF资源
     */
    public static Resource convertHtmlToPdfWithFlyingSaucer(String html) {
        log.info("开始使用Flying Saucer将HTML转换为PDF，HTML长度: {}", html != null ? html.length() : 0);

        try {
            if (html == null || html.trim().isEmpty()) {
                log.error("HTML内容为空");
                return createErrorPdf("HTML内容为空");
            }

            // 检查是否已经是完整的XHTML文档
            String processedHtml = html;
            if (!html.contains("<?xml version") || !html.contains("<!DOCTYPE html PUBLIC")) {
                log.debug("HTML不是完整的XHTML文档，进行处理");
                // 添加CSS样式，支持数学公式
                String htmlWithStyles = addStyles(html);
                // 创建完整的HTML文档
                processedHtml = createCompleteXhtmlDocument(htmlWithStyles);
            } else {
                log.debug("HTML已经是完整的XHTML文档");
            }

            // 清理HTML以确保XHTML兼容性
            processedHtml = cleanHtmlForXhtml(processedHtml);

            log.debug("处理后HTML长度: {}", processedHtml.length());

            // 使用Flying Saucer渲染PDF
            // 设置系统属性以避免XML转换器兼容性问题
            System.setProperty("javax.xml.transform.TransformerFactory",
                "com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl");

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(processedHtml);
            renderer.layout();
            renderer.createPDF(baos);

            byte[] pdfBytes = baos.toByteArray();
            log.info("成功使用Flying Saucer生成PDF，大小: {} bytes", pdfBytes.length);
            return new ByteArrayResource(pdfBytes);
        } catch (Exception e) {
            log.error("使用Flying Saucer将HTML转换为PDF时出错: {}", e.getMessage(), e);
            if (e.getCause() != null) {
                log.error("根本原因: {}", e.getCause().getMessage());
            }
            return createErrorPdf("PDF生成失败: " + e.getMessage());
        }
    }

    /**
     * 添加CSS样式，支持数学公式
     *
     * @param html 原始HTML
     * @return 添加了样式的HTML
     */
    private static String addStyles(String html) {
        // KaTeX CSS样式
        String katexCss = "<style>\n" +
                ".katex { font-size: 1em !important; }\n" +
                ".katex-display { margin: 1em 0 !important; }\n" +
                // 添加其他必要的KaTeX样式
                "</style>\n";

        // 试卷样式 - 增强版
        String paperCss = "<style>\n" +
                "body { font-family: 'SimSun', 'Microsoft YaHei', serif; font-size: 12pt; line-height: 1.6; margin: 20pt; color: #333; }\n" +
                "h1 { font-size: 20pt; text-align: center; margin-bottom: 15pt; font-weight: bold; border-bottom: 2pt solid #000; padding-bottom: 8pt; }\n" +
                "h2 { font-size: 16pt; margin-top: 20pt; margin-bottom: 12pt; font-weight: bold; border-left: 3pt solid #007cba; padding-left: 8pt; }\n" +
                ".question { margin-bottom: 15pt; page-break-inside: avoid; padding: 10pt; border: 1pt solid #f0f0f0; }\n" +
                ".question-title { font-weight: bold; margin-bottom: 8pt; line-height: 1.8; color: #000; font-size: 13pt; }\n" +
                ".options { margin-left: 20pt; margin-bottom: 10pt; margin-top: 8pt; }\n" +
                ".option-item { margin-bottom: 6pt; line-height: 1.6; padding: 2pt 0; }\n" +
                ".answer-line { margin-top: 10pt; margin-bottom: 10pt; font-weight: bold; border-bottom: 1pt solid #ccc; padding-bottom: 3pt; }\n" +
                ".answer-area { margin-top: 10pt; margin-bottom: 10pt; font-weight: bold; }\n" +
                ".answer { margin-bottom: 15pt; page-break-inside: avoid; padding: 8pt; background-color: #f9f9f9; border-left: 3pt solid #28a745; }\n" +
                ".answer-header { font-weight: bold; margin-bottom: 5pt; }\n" +
                ".analysis { color: #666; font-style: italic; margin-top: 5pt; padding-left: 10pt; border-left: 2pt solid #ddd; }\n" +
                ".unknown-type { color: red; font-weight: bold; background-color: #ffe6e6; padding: 3pt; border: 1pt solid #ff9999; }\n" +
                "@page { size: A4; margin: 2cm; }\n" +
                "</style>\n";

        // 在<head>标签中添加样式
        if (html.contains("<head>")) {
            return html.replace("<head>", "<head>\n" + katexCss + paperCss);
        } else if (html.contains("<html>")) {
            return html.replace("<html>", "<html>\n<head>\n" + katexCss + paperCss + "</head>");
        } else {
            return "<html>\n<head>\n" + katexCss + paperCss + "</head>\n<body>\n" + html + "\n</body>\n</html>";
        }
    }

    /**
     * 创建完整的XHTML文档
     *
     * @param html HTML内容
     * @return 完整的XHTML文档
     */
    private static String createCompleteXhtmlDocument(String html) {
        if (html.contains("<?xml version") && html.contains("<!DOCTYPE html PUBLIC")) {
            return html;
        }

        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n" +
                "<html xmlns=\"http://www.w3.org/1999/xhtml\" xml:lang=\"zh-CN\" lang=\"zh-CN\">\n" +
                "<head>\n" +
                "<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/>\n" +
                "<title>试卷</title>\n" +
                "</head>\n" +
                "<body>\n" +
                html +
                "\n</body>\n" +
                "</html>";
    }

    /**
     * 清理HTML以确保XHTML兼容性
     *
     * @param html 原始HTML
     * @return 清理后的XHTML
     */
    private static String cleanHtmlForXhtml(String html) {
        if (html == null) {
            return "";
        }

        return html
                // 确保自闭合标签格式正确
                .replaceAll("<br\\s*/?>", "<br />")
                .replaceAll("<hr\\s*/?>", "<hr />")
                .replaceAll("<img([^>]*?)(?<!/)>", "<img$1 />")
                .replaceAll("<input([^>]*?)(?<!/)>", "<input$1 />")
                .replaceAll("<meta([^>]*?)(?<!/)>", "<meta$1 />")
                // 转义特殊字符（但保留已经转义的）
                .replaceAll("&(?!amp;|lt;|gt;|quot;|apos;|#\\d+;|#x[0-9a-fA-F]+;)", "&amp;")
                // 确保属性值都有引号
                .replaceAll("(\\w+)=([^\"'\\s>]+)", "$1=\"$2\"")
                // 移除可能导致问题的空属性
                .replaceAll("\\s+\\w+=\"\"", "");
    }

    /**
     * 创建完整的HTML文档（保留用于兼容性）
     *
     * @param html HTML内容
     * @return 完整的HTML文档
     */
    private static String createCompleteHtml(String html) {
        if (html.contains("<html") && html.contains("</html>")) {
            return html;
        }

        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "<meta charset=\"UTF-8\">\n" +
                "<title>试卷</title>\n" +
                "</head>\n" +
                "<body>\n" +
                html +
                "\n</body>\n" +
                "</html>";
    }

    /**
     * 创建错误PDF
     *
     * @param errorMessage 错误信息
     * @return 包含错误信息的PDF资源
     */
    private static Resource createErrorPdf(String errorMessage) {
        try {
            Document document = new Document();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter.getInstance(document, baos);
            document.open();
            document.add(new com.itextpdf.text.Paragraph("Error: " + errorMessage));
            document.close();
            return new ByteArrayResource(baos.toByteArray());
        } catch (DocumentException e) {
            log.error("创建错误PDF时出错: {}", e.getMessage(), e);
            return null;
        }
    }
}
