-- 创建测试用户（如果不存在）
-- 执行时间: 2024-05-24

-- 检查是否已存在测试用户
SET @user_exists = (
    SELECT COUNT(*)
    FROM user
    WHERE username = 'admin' AND deleted = 0
);

-- 如果不存在则创建测试用户
SET @sql = IF(@user_exists = 0,
    'INSERT INTO user (username, password, email, role, status, created_at, updated_at, deleted) VALUES (
        "admin", 
        "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLyU5V9.3Nh6", 
        "<EMAIL>", 
        3, 
        1, 
        NOW(), 
        NOW(), 
        0
    )',
    'SELECT "Test user admin already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证用户是否创建成功
SELECT 
    id,
    username,
    email,
    role,
    status,
    created_at
FROM user
WHERE username = 'admin' AND deleted = 0;

-- 显示用户统计信息
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN deleted = 0 THEN 1 END) as active_users,
    COUNT(CASE WHEN deleted = 1 THEN 1 END) as deleted_users
FROM user;
