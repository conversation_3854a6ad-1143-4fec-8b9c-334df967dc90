package com.edu.maizi_edu_sys.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 可靠的PDF生成器
 * 完全基于iText，避免HTML解析问题
 * 支持数学公式和中文字体
 */
@Slf4j
public class ReliablePdfGenerator {

    private static final Pattern MATH_INLINE_PATTERN = Pattern.compile("\\$([^$]+?)\\$");
    private static final Pattern MATH_BLOCK_PATTERN = Pattern.compile("\\$\\$([^$]+?)\\$\\$");
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");

    /**
     * 将HTML内容转换为PDF
     * @param html HTML内容
     * @return PDF资源
     */
    public static Resource convertHtmlToPdf(String html) {
        log.info("开始使用可靠PDF生成器转换HTML到PDF");

        try {
            // 1. 解析HTML内容
            ParsedContent content = parseHtmlContent(html);
            log.debug("HTML内容解析完成");

            // 2. 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Document document = new Document(PageSize.A4, 50, 50, 50, 50);
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            document.open();

            // 设置字体（使用系统默认字体，避免中文字体问题）
            BaseFont baseFont;
            try {
                // 尝试使用中文字体
                baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            } catch (Exception e) {
                log.warn("中文字体加载失败，使用默认字体: {}", e.getMessage());
                // 回退到默认字体
                baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
            }

            Font titleFont = new Font(baseFont, 20, Font.BOLD);
            Font headerFont = new Font(baseFont, 16, Font.BOLD);
            Font normalFont = new Font(baseFont, 12, Font.NORMAL);
            Font mathFont = new Font(baseFont, 12, Font.ITALIC);

            // 添加标题
            if (content.title != null && !content.title.trim().isEmpty()) {
                Paragraph title = new Paragraph(content.title, titleFont);
                title.setAlignment(Element.ALIGN_CENTER);
                title.setSpacingAfter(20);
                document.add(title);
            }

            // 添加内容
            for (ContentBlock block : content.blocks) {
                switch (block.type) {
                    case HEADER:
                        Paragraph header = new Paragraph(block.text, headerFont);
                        header.setSpacingBefore(15);
                        header.setSpacingAfter(10);
                        document.add(header);
                        break;

                    case PARAGRAPH:
                        Paragraph para = createParagraphWithMath(block.text, normalFont, mathFont);
                        para.setSpacingAfter(8);
                        document.add(para);
                        break;

                    case MATH_BLOCK:
                        Paragraph mathBlock = new Paragraph(processMathFormula(block.text), mathFont);
                        mathBlock.setAlignment(Element.ALIGN_CENTER);
                        mathBlock.setSpacingBefore(10);
                        mathBlock.setSpacingAfter(10);
                        document.add(mathBlock);
                        break;
                }
            }

            document.close();

            byte[] pdfBytes = outputStream.toByteArray();
            log.info("PDF生成成功，大小: {} bytes", pdfBytes.length);

            return new ByteArrayResource(pdfBytes);

        } catch (Exception e) {
            log.error("PDF生成失败: {}", e.getMessage(), e);
            return createErrorPdf("PDF生成失败: " + e.getMessage());
        }
    }

    /**
     * 解析HTML内容
     */
    private static ParsedContent parseHtmlContent(String html) {
        ParsedContent content = new ParsedContent();

        if (html == null || html.trim().isEmpty()) {
            return content;
        }

        // 提取标题
        Pattern titlePattern = Pattern.compile("<h1[^>]*>(.*?)</h1>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher titleMatcher = titlePattern.matcher(html);
        if (titleMatcher.find()) {
            content.title = stripHtmlTags(titleMatcher.group(1)).trim();
        }

        // 按行处理内容
        String[] lines = html.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            // 检查是否是标题
            if (line.matches("<h[2-6][^>]*>.*?</h[2-6]>")) {
                String headerText = stripHtmlTags(line).trim();
                if (!headerText.isEmpty()) {
                    content.blocks.add(new ContentBlock(ContentType.HEADER, headerText));
                }
            }
            // 检查是否是块级数学公式
            else if (MATH_BLOCK_PATTERN.matcher(line).find()) {
                Matcher matcher = MATH_BLOCK_PATTERN.matcher(line);
                while (matcher.find()) {
                    content.blocks.add(new ContentBlock(ContentType.MATH_BLOCK, matcher.group(1)));
                }
            }
            // 普通段落
            else if (line.contains("<p") || line.contains("<div") || (!line.startsWith("<") && !line.isEmpty())) {
                String paraText = stripHtmlTags(line).trim();
                if (!paraText.isEmpty()) {
                    content.blocks.add(new ContentBlock(ContentType.PARAGRAPH, paraText));
                }
            }
        }

        return content;
    }

    /**
     * 创建包含数学公式的段落
     */
    private static Paragraph createParagraphWithMath(String text, Font normalFont, Font mathFont) throws DocumentException {
        Paragraph paragraph = new Paragraph();

        // 处理行内数学公式
        Matcher matcher = MATH_INLINE_PATTERN.matcher(text);
        int lastEnd = 0;

        while (matcher.find()) {
            // 添加数学公式前的普通文本
            if (matcher.start() > lastEnd) {
                String normalText = text.substring(lastEnd, matcher.start());
                paragraph.add(new Chunk(normalText, normalFont));
            }

            // 添加数学公式
            String mathText = processMathFormula(matcher.group(1));
            paragraph.add(new Chunk(mathText, mathFont));

            lastEnd = matcher.end();
        }

        // 添加剩余的普通文本
        if (lastEnd < text.length()) {
            String remainingText = text.substring(lastEnd);
            paragraph.add(new Chunk(remainingText, normalFont));
        }

        // 如果没有数学公式，直接添加整个文本
        if (lastEnd == 0) {
            paragraph.add(new Chunk(text, normalFont));
        }

        return paragraph;
    }

    /**
     * 处理数学公式，转换LaTeX符号为Unicode
     */
    private static String processMathFormula(String formula) {
        return formula
                .replace("\\pi", "π")
                .replace("\\alpha", "α")
                .replace("\\beta", "β")
                .replace("\\gamma", "γ")
                .replace("\\delta", "δ")
                .replace("\\theta", "θ")
                .replace("\\lambda", "λ")
                .replace("\\mu", "μ")
                .replace("\\sigma", "σ")
                .replace("\\phi", "φ")
                .replace("\\omega", "ω")
                .replace("\\infty", "∞")
                .replace("\\sum", "Σ")
                .replace("\\int", "∫")
                .replace("\\sqrt", "√")
                .replace("\\rightarrow", "→")
                .replace("\\leftarrow", "←")
                .replace("\\Rightarrow", "⇒")
                .replace("\\Leftarrow", "⇐")
                .replace("\\frac{", "")
                .replace("}{", "/")
                .replaceAll("\\\\[a-zA-Z]+\\{([^}]*)\\}", "$1")
                .replaceAll("\\{([^}]*)\\}", "$1");
    }

    /**
     * 移除HTML标签
     */
    private static String stripHtmlTags(String html) {
        if (html == null) return "";
        return HTML_TAG_PATTERN.matcher(html).replaceAll("").trim();
    }

    /**
     * 创建错误PDF
     */
    private static Resource createErrorPdf(String errorMessage) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Document document = new Document(PageSize.A4);
            PdfWriter.getInstance(document, outputStream);
            document.open();

            // 使用默认字体，避免字体问题
            BaseFont baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
            Font font = new Font(baseFont, 14, Font.NORMAL);

            Paragraph error = new Paragraph("PDF Generation Failed: " + errorMessage, font);
            error.setAlignment(Element.ALIGN_CENTER);
            document.add(error);

            document.close();
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (Exception e) {
            log.error("创建错误PDF失败: {}", e.getMessage());
            return new ByteArrayResource("PDF Generation Failed".getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 解析后的内容结构
     */
    private static class ParsedContent {
        String title = "";
        java.util.List<ContentBlock> blocks = new java.util.ArrayList<>();
    }

    /**
     * 内容块
     */
    private static class ContentBlock {
        ContentType type;
        String text;

        ContentBlock(ContentType type, String text) {
            this.type = type;
            this.text = text;
        }
    }

    /**
     * 内容类型
     */
    private enum ContentType {
        HEADER, PARAGRAPH, MATH_BLOCK
    }
}
