// KaTeX 配置
document.addEventListener('DOMContentLoaded', function() {
    // 自动渲染页面上的所有公式
    renderMathInElement(document.body, {
        // 自定义分隔符
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        // 错误处理
        throwOnError: false,
        // 显示错误信息
        errorCallback: function(msg, err) {
            console.warn('KaTeX 渲染错误:', msg, err);
        },
        // 其他选项
        fleqn: false,
        leqno: false,
        strict: false,
        trust: false,
        macros: {}
    });
});

// 全局渲染函数，用于动态添加的内容
function renderKaTeX(element) {
    if (!element) {
        element = document.body;
    }
    
    try {
        renderMathInElement(element, {
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            errorCallback: function(msg, err) {
                console.warn('KaTeX 渲染错误:', msg, err);
            }
        });
        console.log('KaTeX 渲染完成');
        return true;
    } catch (e) {
        console.error('KaTeX 渲染异常:', e);
        return false;
    }
}
