package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 知识点级别多样性控制功能测试
 */
@DisplayName("知识点级别多样性控制测试")
public class KnowledgePointDiversityTest {

    private DiversityFilter diversityFilter;

    @BeforeEach
    void setUp() {
        diversityFilter = new DiversityFilter();
        
        // 设置测试配置参数
        ReflectionTestUtils.setField(diversityFilter, "knowledgePointLevelEnabled", true);
        ReflectionTestUtils.setField(diversityFilter, "knowledgePointMinReuseIntervalDays", 3);
        ReflectionTestUtils.setField(diversityFilter, "maxTopicsPerKnowledgePoint", 5);
        ReflectionTestUtils.setField(diversityFilter, "priorityWeightUsage", 10.0);
        ReflectionTestUtils.setField(diversityFilter, "priorityWeightTime", 1.0);
    }

    @Test
    @DisplayName("测试知识点级别过滤功能")
    void testKnowledgePointLevelFiltering() {
        // 准备测试数据
        List<Topic> topics = createTestTopics();
        Map<Integer, TopicEnhancementData> enhancementDataMap = createTestEnhancementData();

        // 执行知识点级别过滤
        List<Topic> filteredTopics = diversityFilter.filterByKnowledgePoint(
                topics, enhancementDataMap, 3, 3);

        // 验证结果
        assertNotNull(filteredTopics);
        assertTrue(filteredTopics.size() <= topics.size());

        // 验证每个知识点的题目数量不超过限制
        Map<Integer, Long> topicCountsByKnowledgePoint = filteredTopics.stream()
                .filter(topic -> topic.getKnowId() != null)
                .collect(java.util.stream.Collectors.groupingBy(
                        Topic::getKnowId, 
                        java.util.stream.Collectors.counting()));

        for (Long count : topicCountsByKnowledgePoint.values()) {
            assertTrue(count <= 3, "每个知识点的题目数量应该不超过限制");
        }
    }

    @Test
    @DisplayName("测试智能过滤功能")
    void testSmartFilter() {
        // 准备测试数据
        List<Topic> topics = createTestTopics();
        Map<Integer, TopicEnhancementData> enhancementDataMap = createTestEnhancementData();

        // 执行智能过滤
        List<Topic> filteredTopics = diversityFilter.smartFilter(topics, enhancementDataMap, 7);

        // 验证结果
        assertNotNull(filteredTopics);
        assertTrue(filteredTopics.size() <= topics.size());
    }

    @Test
    @DisplayName("测试禁用知识点级别控制")
    void testDisabledKnowledgePointControl() {
        // 禁用知识点级别控制
        ReflectionTestUtils.setField(diversityFilter, "knowledgePointLevelEnabled", false);

        // 准备测试数据
        List<Topic> topics = createTestTopics();
        Map<Integer, TopicEnhancementData> enhancementDataMap = createTestEnhancementData();

        // 执行智能过滤
        List<Topic> filteredTopics = diversityFilter.smartFilter(topics, enhancementDataMap, 7);

        // 验证结果 - 应该只使用全局过滤
        assertNotNull(filteredTopics);
    }

    @Test
    @DisplayName("测试题目优先级计算")
    void testTopicPriorityCalculation() {
        // 准备测试数据
        List<Topic> topics = createTestTopics();
        Map<Integer, TopicEnhancementData> enhancementDataMap = createTestEnhancementData();

        // 通过反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = DiversityFilter.class.getDeclaredMethod(
                    "prioritizeTopicsByUsageAndTime", List.class, Map.class);
            method.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            List<Topic> prioritizedTopics = (List<Topic>) method.invoke(
                    diversityFilter, topics, enhancementDataMap);

            assertNotNull(prioritizedTopics);
            assertEquals(topics.size(), prioritizedTopics.size());
            
        } catch (Exception e) {
            fail("优先级计算测试失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试题目数据
     */
    private List<Topic> createTestTopics() {
        List<Topic> topics = new ArrayList<>();
        
        // 知识点1的题目
        for (int i = 1; i <= 6; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(1);
            topic.setType("choice");
            topic.setTitle("知识点1题目" + i);
            topic.setScore(5);
            topics.add(topic);
        }
        
        // 知识点2的题目
        for (int i = 7; i <= 12; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(2);
            topic.setType("multiple");
            topic.setTitle("知识点2题目" + (i-6));
            topic.setScore(8);
            topics.add(topic);
        }
        
        return topics;
    }

    /**
     * 创建测试增强数据
     */
    private Map<Integer, TopicEnhancementData> createTestEnhancementData() {
        Map<Integer, TopicEnhancementData> dataMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 为部分题目创建增强数据
        for (int i = 1; i <= 12; i++) {
            TopicEnhancementData data = new TopicEnhancementData();
            data.setTopicId(i);
            data.setUsageCount(i % 3); // 不同的使用次数
            
            // 设置不同的最后使用时间
            if (i % 4 == 0) {
                data.setLastUsedTime(now.minusDays(1)); // 1天前使用
            } else if (i % 4 == 1) {
                data.setLastUsedTime(now.minusDays(5)); // 5天前使用
            } else if (i % 4 == 2) {
                data.setLastUsedTime(now.minusDays(10)); // 10天前使用
            }
            // i % 4 == 3 的情况不设置最后使用时间（从未使用）
            
            data.setCreateTime(now.minusDays(30));
            dataMap.put(i, data);
        }
        
        return dataMap;
    }
}
