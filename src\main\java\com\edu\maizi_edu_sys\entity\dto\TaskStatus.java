// com.edu.maizi_edu_sys.entity.dto.TaskStatus.java
package com.edu.maizi_edu_sys.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;

@Data
@AllArgsConstructor
public class TaskStatus {
    @JsonProperty("task_id")
    private String taskId;
    
    @JsonProperty("state")
    private String state;
    
    @JsonProperty("full_zip_url")
    private String zipUrl;
    
    @JsonProperty("err_msg")
    private String errorMessage;
    
    @JsonProperty("extract_progress")
    private ExtractProgress progress;

    @Data
    public static class ExtractProgress {
        @JsonProperty("extracted_pages")
        private Integer processedPages;
        
        @JsonProperty("total_pages")
        private Integer totalPages;
        
        @JsonProperty("start_time")
        private LocalDateTime startTime;
    }
}