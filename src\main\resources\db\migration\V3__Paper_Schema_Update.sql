-- Update papers table to match the schema
ALTER TABLE `papers` 
  ADD COLUMN `user_id` bigint unsigned DEFAULT NULL COMMENT '创建用户ID' AFTER `title`,
  ADD COLUMN `paper_type` varchar(20) NOT NULL DEFAULT 'regular' COMMENT '试卷类型：regular-普通试卷, teacher-教师试卷, standard-标准试卷' AFTER `config`,
  ADD COLUMN `file_format` varchar(10) NOT NULL DEFAULT 'pdf' COMMENT '文件格式：pdf, docx' AFTER `paper_type`,
  ADD COLUMN `download_count` int NOT NULL DEFAULT '0' COMMENT '下载次数' AFTER `file_format`,
  ADD COLUMN `last_download_time` datetime DEFAULT NULL COMMENT '最后下载时间' AFTER `download_count`,
  ADD KEY `idx_user_id` (`user_id`);

-- Add foreign key for user_id if not exists
ALTER TABLE `papers` 
  ADD CONSTRAINT `fk_papers_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL;

-- <PERSON>reate paper_downloads table
CREATE TABLE IF NOT EXISTS `paper_downloads` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `paper_id` bigint NOT NULL COMMENT '试卷ID',
  `download_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '下载IP地址',
  `file_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '下载格式：pdf, docx',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_paper_id` (`paper_id`) USING BTREE,
  KEY `idx_download_time` (`download_time`) USING BTREE,
  CONSTRAINT `fk_downloads_paper` FOREIGN KEY (`paper_id`) REFERENCES `papers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_downloads_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户试卷下载记录'; 