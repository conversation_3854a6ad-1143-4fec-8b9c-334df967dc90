package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.dto.ChatBookUpdateRequest;
import com.edu.maizi_edu_sys.entity.dto.ChatCreateRequest;
import com.edu.maizi_edu_sys.entity.dto.ChatMessageRequest;
import com.edu.maizi_edu_sys.entity.ChatSession;

import java.util.function.Consumer;

public interface ChatService {
    ApiResponse<?> createChat(ChatCreateRequest request, String token);
    ApiResponse<?> getChatHistory(String token);
    ApiResponse<?> getChatMessages(Long chatId, String token);
    ApiResponse<?> sendMessage(ChatMessageRequest request, String token);
    ApiResponse<?> deleteChat(Long chatId, String token);
    ApiResponse<?> updateChatTitle(Long chatId, String title, String token);
    
    /**
     * 更新聊天关联的教材配置
     * 
     * @param chatId 聊天ID
     * @param bookData 教材数据
     * @param token 认证令牌
     * @return 操作结果
     */
    ApiResponse<?> updateChatBook(Long chatId, ChatBookUpdateRequest bookData, String token);
    
    /**
     * 获取聊天详情
     * 
     * @param chatId 聊天ID
     * @param token 认证令牌
     * @return 聊天详情
     */
    ApiResponse<?> getChatDetail(Long chatId, String token);
    
    /**
     * Stream messages from AI with real-time responses
     * 
     * @param request The chat message request
     * @param token Authentication token
     * @param chunkConsumer Consumer for handling chunks of text as they arrive
     * @param errorConsumer Consumer for handling errors
     * @param completionCallback Runnable to call when streaming is complete
     */
    void streamMessage(
        ChatMessageRequest request, 
        String token,
        Consumer<String> chunkConsumer,
        Consumer<String> errorConsumer,
        Runnable completionCallback
    );

    /**
     * 更新聊天标题
     * 
     * @param chatId 聊天ID
     * @param newTitle 新标题
     * @return 是否成功
     */
    boolean updateChatTitle(Long chatId, String newTitle);

    /**
     * 删除聊天及其所有消息
     * 
     * @param chatId 聊天ID
     * @return 是否成功
     */
    boolean deleteChat(Long chatId);

    /**
     * 根据ID获取聊天会话
     * 
     * @param chatId 聊天会话ID
     * @return 聊天会话实体
     */
    ChatSession getChatById(Long chatId);
} 