/**
 * 试卷提交功能
 * 处理试卷生成表单的提交和响应
 */

// 显示错误消息
function showError(title, message) {
    Swal.fire({
        icon: 'error',
        title: title,
        text: message,
        confirmButtonText: '确定'
    });
}

// 显示成功消息
function showSuccess(title, message) {
    Swal.fire({
        icon: 'success',
        title: title,
        text: message,
        confirmButtonText: '确定'
    });
}

// 显示加载中
function showLoading(message) {
    return Swal.fire({
        title: '处理中...',
        text: message || '正在生成试卷，请稍候...',
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

$(document).ready(function() {
    // 试卷提交脚本已加载

    // 提交生成试卷按钮点击事件
    $('#submitGeneratePaperBtn').on('click', function() {
        console.log('提交生成试卷按钮被点击');

        const $btn = $(this);
        const originalText = $btn.html();

        // 表单验证
        const paperTitle = $('#paperTitle').val().trim();
        if (!paperTitle) {
            showError('验证失败', '请输入试卷标题');
            return;
        }

        // 检查配置模式
        const configMode = $('input[name="configMode"]:checked').val();
        let knowledgeConfigs;

        if (configMode === 'precise') {
            // 精确配置模式
            if (typeof window.modalPreciseConfigs !== 'undefined' && window.modalPreciseConfigs.length > 0) {
                knowledgeConfigs = window.modalPreciseConfigs;
                console.log('使用精确配置模式:', knowledgeConfigs);
            } else {
                showError('配置错误', '精确配置模式下未找到配置，请先完成精确配置');
                $('#knowledge-tab').tab('show');
                return;
            }
        } else {
            // 基础配置模式
            knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs');

            //  调试：检查知识点配置数据来源
            console.log('🔍 基础配置模式 - 知识点配置数据检查:', {
                fromFormData: $('#generatePaperForm').data('knowledgeConfigs'),
                dataLength: knowledgeConfigs ? knowledgeConfigs.length : 0,
                formDataKeys: Object.keys($('#generatePaperForm').data() || {}),
                knowledgeConfigsRaw: knowledgeConfigs
            });

            if (!knowledgeConfigs || !Array.isArray(knowledgeConfigs) || knowledgeConfigs.length === 0) {
                showError('配置错误', '未找到知识点配置，请先选择知识点');
                return;
            }
        }

        // 验证题量是否匹配
        let singleChoice, multipleChoice, judgment, fillBlank, shortAnswer;

        if (configMode === 'precise') {
            // 精确配置模式：从配置中计算总量
            singleChoice = 0;
            multipleChoice = 0;
            judgment = 0;
            fillBlank = 0;
            shortAnswer = 0;

            knowledgeConfigs.forEach(config => {
                singleChoice += config.singleChoiceCount || 0;
                multipleChoice += config.multipleChoiceCount || 0;
                judgment += config.judgeCount || 0;
                fillBlank += config.fillCount || 0;
                shortAnswer += config.shortAnswerCount || 0;
            });

            console.log('精确配置模式题型统计:', {
                singleChoice, multipleChoice, judgment, fillBlank, shortAnswer
            });
        } else {
            // 基础配置模式：从表单获取
            singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
            multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
            judgment = parseInt($('#judgmentCount').val()) || 0;
            fillBlank = parseInt($('#fillCount').val()) || 0;
            shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

            //  修复：基础题型总量不包括简答题（简答题是额外的）
            const basicTypesTotal = singleChoice + multipleChoice + judgment + fillBlank;

            // 计算知识点配置的基础题量（不包括简答题）
            let totalKnowledgeBasic = 0;
            knowledgeConfigs.forEach(function(config) {
                totalKnowledgeBasic += parseInt(config.questionCount) || 0;
            });

            //  调试信息：打印详细的验证数据
            console.log('🔍 题量验证详情:', {
                singleChoice,
                multipleChoice,
                judgment,
                fillBlank,
                shortAnswer,
                basicTypesTotal,
                totalKnowledgeBasic,
                knowledgeConfigs: knowledgeConfigs.map(config => ({
                    knowledgeId: config.knowledgeId,
                    questionCount: config.questionCount,
                    includeShortAnswer: config.includeShortAnswer,
                    shortAnswerCount: config.shortAnswerCount
                }))
            });

            //  修复：只比较基础题型数量，简答题是额外配置的
            if (basicTypesTotal !== totalKnowledgeBasic) {
                console.error('❌ 题量不匹配:', {
                    basicTypesTotal,
                    totalKnowledgeBasic,
                    difference: basicTypesTotal - totalKnowledgeBasic
                });

                showError('题量不匹配', `基础题型设置的总题量(${basicTypesTotal})与知识点配置的总题量(${totalKnowledgeBasic})不一致，请调整后再提交。\n\n注意：简答题是额外配置的，不计入基础题量。\n\n调试信息：\n- 单选题: ${singleChoice}\n- 多选题: ${multipleChoice}\n- 判断题: ${judgment}\n- 填空题: ${fillBlank}\n- 知识点配置详情: ${JSON.stringify(knowledgeConfigs, null, 2)}`);
                // 切换到知识点配置选项卡
                $('#knowledge-tab').tab('show');
                return;
            }

            //  新增：验证简答题配置的合理性
            if (shortAnswer > 0) {
                // 计算知识点配置的简答题总数
                let totalShortAnswerFromKP = 0;
                knowledgeConfigs.forEach(function(config) {
                    if (config.includeShortAnswer && config.shortAnswerCount) {
                        totalShortAnswerFromKP += parseInt(config.shortAnswerCount) || 0;
                    }
                });

                // 如果知识点配置了具体的简答题数量，检查是否匹配
                if (totalShortAnswerFromKP > 0 && totalShortAnswerFromKP !== shortAnswer) {
                    showError('简答题数量不匹配', `题型设置的简答题数量(${shortAnswer})与知识点配置的简答题总数(${totalShortAnswerFromKP})不一致。\n\n请确保两处设置一致。`);
                    $('#knowledge-tab').tab('show');
                    return;
                }
            }
        }

        // 验证简答题配置
        if (shortAnswer > 0) {
            // 检查是否有知识点开启了简答题
            const hasShortAnswerEnabled = knowledgeConfigs.some(config => config.includeShortAnswer === true);
            if (!hasShortAnswerEnabled) {
                showError('简答题配置错误', '您设置了简答题数量，但没有知识点开启简答题选项。请在知识点配置中至少开启一个知识点的简答题选项。');
                // 切换到知识点配置选项卡
                $('#knowledge-tab').tab('show');
                return;
            }
        }

        // 验证难度分布总和是否为100%
        const easyPercentage = parseInt($('#easyPercentage').val()) || 0;
        const mediumPercentage = parseInt($('#mediumPercentage').val()) || 0;
        const hardPercentage = parseInt($('#hardPercentage').val()) || 0;

        const difficultySum = easyPercentage + mediumPercentage + hardPercentage;
        if (difficultySum !== 100) {
            showError('验证失败', `难度分布总和应为100%，当前为${difficultySum}%`);
            // 切换到难度分布选项卡
            $('#difficulty-tab').tab('show');
            return;
        }

        // 计算总分
        const singleChoiceScore = parseFloat($('#singleChoiceScore').val()) || 0;
        const multipleChoiceScore = parseFloat($('#multipleChoiceScore').val()) || 0;
        const judgmentScore = parseFloat($('#judgmentScore').val()) || 0;
        const fillScore = parseFloat($('#fillScore').val()) || 0;
        const shortAnswerScore = parseFloat($('#shortAnswerScore').val()) || 0;

        const totalScore =
            singleChoice * singleChoiceScore +
            multipleChoice * multipleChoiceScore +
            judgment * judgmentScore +
            fillBlank * fillScore +
            shortAnswer * shortAnswerScore;

        // 获取试卷版本和生成套数
        const paperType = $('#paperType').val() || 'standard';
        const paperCount = parseInt($('#paperCount').val()) || 1; //  获取生成套数

        //  批量生成逻辑
        if (paperCount > 1) {
            // 使用后台批量生成接口
            generateBatchPapersWithBackend(paperTitle, paperType, paperCount, knowledgeConfigs,
                                         singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                         singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                         $btn, originalText);
            return;
        }

        // 构建请求数据（单套试卷）
        const requestData = {
            title: paperTitle,
            paperType: paperType, // 添加试卷版本字段
            knowledgePointConfigs: knowledgeConfigs,
            totalScore: totalScore, // 添加总分字段
            topicTypeCounts: {
                SINGLE_CHOICE: singleChoice,
                MULTIPLE_CHOICE: multipleChoice,
                JUDGE: judgment,
                FILL: fillBlank,
                SHORT: shortAnswer
            },
            typeScoreMap: {
                SINGLE_CHOICE: singleChoiceScore,
                MULTIPLE_CHOICE: multipleChoiceScore,
                JUDGE: judgmentScore,
                FILL: fillScore,
                SHORT: shortAnswerScore
            },
            difficultyCriteria: {
                easy: parseInt($('#easyPercentage').val()) / 100,
                medium: parseInt($('#mediumPercentage').val()) / 100,
                hard: parseInt($('#hardPercentage').val()) / 100
            }
        };

        console.log('生成试卷请求数据:', requestData);

        // 验证请求数据
        const validationErrors = validateRequestData(requestData);
        if (validationErrors.length > 0) {
            // 显示验证错误
            Swal.fire({
                icon: 'warning',
                title: '请求数据不完整',
                html: `
                    <div class="text-left">
                        <p>请求数据缺少以下必要字段：</p>
                        <ul class="pl-4">
                            ${validationErrors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle mr-2"></i>
                            请完善以上信息后再次提交。
                        </div>
                    </div>
                `,
                confirmButtonText: '返回修改'
            });
            return;
        }

        // 显示加载中
        $('#paperGenerationModal').modal('hide');
        const loadingDialog = showLoading('正在从题库中选择合适的题目，请稍候...');

        // 禁用按钮
        $btn.html('<i class="fas fa-spinner fa-spin mr-1"></i>处理中...');
        $btn.prop('disabled', true);

        // 验证请求数据的辅助函数
        function validateRequestData(data) {
            const errors = [];

            // 检查标题
            if (!data.title) {
                errors.push('试卷标题');
            }

            // 检查知识点配置
            if (!data.knowledgePointConfigs || data.knowledgePointConfigs.length === 0) {
                errors.push('知识点配置');
            } else {
                // 检查每个知识点配置
                data.knowledgePointConfigs.forEach((config, index) => {
                    if (!config.knowledgeId) {
                        errors.push(`知识点${index + 1}的ID`);
                    }
                    if (config.questionCount === undefined || config.questionCount === null) {
                        errors.push(`知识点${index + 1}的题目数量`);
                    }
                });
            }

            // 检查总分
            if (data.totalScore === undefined || data.totalScore === null) {
                errors.push('试卷总分');
            }

            // 检查题型数量
            if (!data.topicTypeCounts) {
                errors.push('题型数量配置');
            } else {
                let hasAtLeastOneType = false;
                for (const type in data.topicTypeCounts) {
                    if (data.topicTypeCounts[type] > 0) {
                        hasAtLeastOneType = true;
                        break;
                    }
                }
                if (!hasAtLeastOneType) {
                    errors.push('至少一种题型的数量');
                }
            }

            // 检查题型分数
            if (!data.typeScoreMap) {
                errors.push('题型分数配置');
            } else {
                for (const type in data.topicTypeCounts) {
                    if (data.topicTypeCounts[type] > 0 && (!data.typeScoreMap[type] || data.typeScoreMap[type] <= 0)) {
                        errors.push(`${getTypeDisplayName(type)}的分数`);
                    }
                }
            }

            // 检查难度分布
            if (!data.difficultyCriteria) {
                errors.push('难度分布配置');
            } else {
                const { easy, medium, hard } = data.difficultyCriteria;
                if (easy === undefined || medium === undefined || hard === undefined) {
                    errors.push('完整的难度分布（简单、中等、困难）');
                } else {
                    const sum = (easy || 0) + (medium || 0) + (hard || 0);
                    if (Math.abs(sum - 1) > 0.01) { // 允许小误差
                        errors.push('难度分布总和应为1（当前为' + sum.toFixed(2) + '）');
                    }
                }
            }

            return errors;
        }

        // 获取题型显示名称
        function getTypeDisplayName(type) {
            const typeMap = {
                'SINGLE_CHOICE': '单选题',
                'MULTIPLE_CHOICE': '多选题',
                'JUDGMENT': '判断题',
                'FILL_BLANK': '填空题',
                'SHORT_ANSWER': '简答题'
            };
            return typeMap[type] || type;
        }

        // 发送请求
        $.ajax({
            url: '/api/papers/generate',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                // 隐藏加载中
                loadingDialog.close();

                // 恢复按钮
                $btn.html(originalText);
                $btn.prop('disabled', false);

                console.log('生成试卷响应:', response);

                if (response && response.success && response.data) {
                    const paperData = response.data;

                    // 显示成功消息
                    Swal.fire({
                        icon: 'success',
                        title: '试卷生成成功',
                        text: `已生成试卷: ${paperData.paperTitle || paperTitle}`,
                        confirmButtonText: '查看试卷',
                        showCancelButton: true,
                        cancelButtonText: '关闭'
                    }).then((result) => {
                        if (result.isConfirmed && paperData.paperId) {
                            // 跳转到试卷预览页面
                            window.location.href = `/papers/preview/${paperData.paperId}`;
                        } else {
                            // 刷新历史记录
                            if (typeof window.loadPaperHistory === 'function') {
                                window.loadPaperHistory();
                            }
                        }
                    });
                } else {
                    // 显示错误消息
                    let errorMessage = response.message || '试卷生成失败，请稍后重试';
                    let errorDetails = '';

                    // 检查是否有详细错误信息
                    if (response.data && response.data.errorDetails) {
                        errorDetails = response.data.errorDetails;
                    }

                    // 根据错误类型提供不同的提示
                    if (errorMessage.includes('题库题量不足') || errorMessage.includes('无法满足')) {
                        Swal.fire({
                            icon: 'warning',
                            title: '题库题量不足',
                            html: `
                                <div class="text-left">
                                    <p>${errorMessage}</p>
                                    ${errorDetails ? `<p class="mt-2 small text-muted">${errorDetails}</p>` : ''}
                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-lightbulb mr-2"></i>建议:
                                        <ul class="mb-0 pl-4">
                                            <li>减少题目总数量</li>
                                            <li>选择更多知识点</li>
                                            <li>调整知识点题量分配</li>
                                        </ul>
                                    </div>
                                </div>
                            `,
                            confirmButtonText: '返回修改',
                            showCancelButton: true,
                            cancelButtonText: '关闭'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // 切换到知识点配置选项卡
                                $('#knowledge-tab').tab('show');
                            }
                        });
                    } else {
                        showError('生成失败', errorMessage);
                    }
                }
            },
            error: function(xhr) {
                // 隐藏加载中
                loadingDialog.close();

                // 恢复按钮
                $btn.html(originalText);
                $btn.prop('disabled', false);

                console.error('生成试卷请求失败:', xhr);

                // 解析错误信息
                let errorMessage = '服务器连接错误';
                let errorDetails = '';
                let errorType = 'general';

                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMessage = response.message || '试卷生成失败，请稍后重试';

                    if (response.data && response.data.errorDetails) {
                        errorDetails = response.data.errorDetails;
                    }

                    // 尝试识别错误类型
                    if (xhr.status === 500 && xhr.responseText.includes('NullPointerException')) {
                        errorType = 'nullPointer';
                        errorMessage = '系统处理请求时遇到错误，可能是参数不完整或格式不正确';
                        errorDetails = '请检查您的试卷配置，确保所有必填字段都已填写，并且格式正确。';
                    } else if (errorMessage.includes('题库题量不足') || errorMessage.includes('无法满足')) {
                        errorType = 'insufficientTopics';
                    } else if (errorMessage.includes('知识点') && (errorMessage.includes('不存在') || errorMessage.includes('未找到'))) {
                        errorType = 'invalidKnowledgePoint';
                    }
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                    // 检查是否是空指针异常
                    if (xhr.responseText && xhr.responseText.includes('NullPointerException')) {
                        errorType = 'nullPointer';
                        errorMessage = '系统处理请求时遇到错误，可能是参数不完整或格式不正确';
                        errorDetails = '请检查您的试卷配置，确保所有必填字段都已填写，并且格式正确。';
                    }
                }

                // 根据错误类型提供不同的提示
                let alertHtml = '';

                switch (errorType) {
                    case 'nullPointer':
                        alertHtml = `
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <strong>可能的原因：</strong>
                                <ul class="mb-0 pl-4">
                                    <li>试卷总分计算错误</li>
                                    <li>知识点配置不完整</li>
                                    <li>题型数量或分值设置有误</li>
                                </ul>
                            </div>
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-lightbulb mr-2"></i>
                                <strong>建议操作：</strong>
                                <ul class="mb-0 pl-4">
                                    <li>检查所有题型的数量和分值设置</li>
                                    <li>确保难度分布总和为100%</li>
                                    <li>尝试减少知识点数量或题目总数</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case 'insufficientTopics':
                        alertHtml = `
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <strong>题库题量不足：</strong>
                                <ul class="mb-0 pl-4">
                                    <li>所选知识点的题库中没有足够的题目</li>
                                    <li>特定题型（如简答题）的题目数量不足</li>
                                </ul>
                            </div>
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-lightbulb mr-2"></i>
                                <strong>建议操作：</strong>
                                <ul class="mb-0 pl-4">
                                    <li>减少题目总数量</li>
                                    <li>选择更多知识点</li>
                                    <li>调整题型分布，减少题量不足的题型</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case 'invalidKnowledgePoint':
                        alertHtml = `
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <strong>知识点错误：</strong>
                                <ul class="mb-0 pl-4">
                                    <li>所选知识点不存在或已被删除</li>
                                    <li>知识点ID格式不正确</li>
                                </ul>
                            </div>
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-lightbulb mr-2"></i>
                                <strong>建议操作：</strong>
                                <ul class="mb-0 pl-4">
                                    <li>刷新页面，重新选择知识点</li>
                                    <li>检查知识点是否有效</li>
                                </ul>
                            </div>
                        `;
                        break;
                    default:
                        alertHtml = `
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                如果问题持续存在，请联系管理员或稍后重试。
                            </div>
                        `;
                }

                // 显示错误消息
                Swal.fire({
                    icon: 'error',
                    title: '生成失败',
                    html: `
                        <div class="text-left">
                            <p>${errorMessage}</p>
                            ${errorDetails ? `<p class="mt-2 small text-muted">${errorDetails}</p>` : ''}
                            ${alertHtml}
                        </div>
                    `,
                    confirmButtonText: '返回修改',
                    showCancelButton: true,
                    cancelButtonText: '关闭'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 根据错误类型切换到相应的选项卡
                        if (errorType === 'nullPointer' || errorType === 'insufficientTopics') {
                            $('#knowledge-tab').tab('show');
                        } else if (errorType === 'invalidKnowledgePoint') {
                            // 关闭模态框，返回知识点选择界面
                            $('#paperGenerationModal').modal('hide');
                        }
                    }
                });
            }
        });
    });

    //  使用后台批量生成接口
    function generateBatchPapersWithBackend(paperTitle, paperType, paperCount, knowledgeConfigs,
                                          singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                          singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                          $btn, originalText) {

        console.log(` 使用后台接口批量生成 ${paperCount} 套试卷`);

        // 计算总分
        const totalScore =
            singleChoice * singleChoiceScore +
            multipleChoice * multipleChoiceScore +
            judgment * judgmentScore +
            fillBlank * fillScore +
            shortAnswer * shortAnswerScore;

        // 显示批量生成确认对话框
        Swal.fire({
            icon: 'question',
            title: '批量生成确认',
            html: `
                <div class="text-left">
                    <p>您即将生成 <strong>${paperCount}</strong> 套试卷：</p>
                    <ul class="pl-4">
                        <li><strong>试卷标题：</strong>${paperTitle}</li>
                        <li><strong>试卷版本：</strong>${paperType === 'standard' ? '标准版' : paperType === 'teacher' ? '教师版' : '自定义版'}</li>
                        <li><strong>题目总数：</strong>${singleChoice + multipleChoice + judgment + fillBlank + shortAnswer} 题</li>
                        <li><strong>试卷总分：</strong>${totalScore} 分</li>
                    </ul>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle mr-2"></i>
                        每套试卷的题目都会不同，确保试卷的多样性。
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: `生成 ${paperCount} 套试卷`,
            cancelButtonText: '取消',
            confirmButtonColor: '#007bff'
        }).then((result) => {
            if (result.isConfirmed) {
                executeBatchGenerationWithBackend(paperTitle, paperType, paperCount, knowledgeConfigs,
                                                singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                                singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                                totalScore, $btn, originalText);
            }
        });
    }

    //  执行后台批量生成
    function executeBatchGenerationWithBackend(paperTitle, paperType, paperCount, knowledgeConfigs,
                                             singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                             singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                             totalScore, $btn, originalText) {

        // 隐藏模态框
        $('#paperGenerationModal').modal('hide');

        // 显示进度对话框
        const progressDialog = Swal.fire({
            title: '批量生成进行中...',
            html: `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="sr-only">生成中...</span>
                    </div>
                    <p>正在生成 ${paperCount} 套试卷，请耐心等待...</p>
                    <small class="text-muted">后台正在处理您的请求</small>
                </div>
            `,
            allowOutsideClick: false,
            showConfirmButton: false,
            showCancelButton: false
        });

        // 禁用按钮
        $btn.html('<i class="fas fa-spinner fa-spin mr-1"></i>批量生成中...');
        $btn.prop('disabled', true);

        // 构建批量生成请求数据
        const batchRequestData = {
            title: paperTitle,
            paperCount: paperCount,
            paperType: paperType,
            knowledgePointConfigs: knowledgeConfigs,
            totalScore: totalScore,
            topicTypeCounts: {
                SINGLE_CHOICE: singleChoice,
                MULTIPLE_CHOICE: multipleChoice,
                JUDGE: judgment,
                FILL: fillBlank,
                SHORT: shortAnswer
            },
            typeScoreMap: {
                SINGLE_CHOICE: singleChoiceScore,
                MULTIPLE_CHOICE: multipleChoiceScore,
                JUDGE: judgmentScore,
                FILL: fillScore,
                SHORT: shortAnswerScore
            },
            difficultyCriteria: {
                easy: parseInt($('#easyPercentage').val()) / 100,
                medium: parseInt($('#mediumPercentage').val()) / 100,
                hard: parseInt($('#hardPercentage').val()) / 100
            }
        };

        console.log(' 批量生成请求数据:', batchRequestData);

        // 发送批量生成请求
        $.ajax({
            url: '/api/papers/generate-batch',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(batchRequestData),
            success: function(response) {
                console.log(' 批量生成响应:', response);

                // 恢复按钮
                $btn.html(originalText);
                $btn.prop('disabled', false);

                // 关闭进度对话框
                progressDialog.close();

                if (response && response.success && response.data) {
                    showBatchGenerationResults(response.data);
                } else {
                    showError('批量生成失败', response.message || '未知错误');
                }
            },
            error: function(xhr) {
                console.error('❌ 批量生成请求失败:', xhr);

                // 恢复按钮
                $btn.html(originalText);
                $btn.prop('disabled', false);

                // 关闭进度对话框
                progressDialog.close();

                let errorMessage = '网络错误或服务器错误';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showError('批量生成失败', errorMessage);
            }
        });
    }

    //  显示批量生成结果
    function showBatchGenerationResults(batchData) {
        const successCount = batchData.successCount || 0;
        const failedCount = batchData.failedCount || 0;
        const totalCount = batchData.totalCount || 0;
        const results = batchData.results || [];

        console.log(`🎉 批量生成完成: 成功 ${successCount} 套, 失败 ${failedCount} 套`);

        // 显示完成结果
        let resultHtml = `
            <div class="text-left">
                <div class="alert alert-${successCount > 0 ? 'success' : 'danger'}">
                    <h5><i class="fas fa-${successCount > 0 ? 'check-circle' : 'exclamation-triangle'} mr-2"></i>批量生成完成</h5>
                    <p class="mb-0">
                        总计 ${totalCount} 套试卷：
                        <strong class="text-success">${successCount} 套成功</strong>
                        ${failedCount > 0 ? `，<strong class="text-danger">${failedCount} 套失败</strong>` : ''}
                    </p>
                </div>
        `;

        if (successCount > 0) {
            resultHtml += `
                <h6><i class="fas fa-check-circle text-success mr-2"></i>成功生成的试卷：</h6>
                <ul class="list-group mb-3">
            `;
            results.forEach((result) => {
                if (result.success) {
                    resultHtml += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>${result.title}</span>
                            <a href="/papers/preview/${result.paperId}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-eye mr-1"></i>查看
                            </a>
                        </li>
                    `;
                }
            });
            resultHtml += `</ul>`;
        }

        if (failedCount > 0) {
            resultHtml += `
                <h6><i class="fas fa-exclamation-triangle text-danger mr-2"></i>生成失败的试卷：</h6>
                <ul class="list-group mb-3">
            `;
            results.forEach((result) => {
                if (!result.success) {
                    resultHtml += `
                        <li class="list-group-item">
                            <strong>${result.title}</strong><br>
                            <small class="text-danger">${result.errorMessage}</small>
                        </li>
                    `;
                }
            });
            resultHtml += `</ul>`;
        }

        resultHtml += `</div>`;

        Swal.fire({
            icon: successCount > 0 ? 'success' : 'error',
            title: '批量生成完成',
            html: resultHtml,
            confirmButtonText: '确定',
            width: '600px'
        }).then(() => {
            // 刷新历史记录
            if (typeof window.loadPaperHistory === 'function') {
                window.loadPaperHistory();
            }
        });
    }

    //  批量生成多套试卷函数（前端递归方式，保留作为备用）
    function generateMultiplePapers(paperTitle, paperType, paperCount, knowledgeConfigs,
                                   singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                   singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                   $btn, originalText) {

        console.log(` 开始批量生成 ${paperCount} 套试卷`);

        // 计算总分
        const totalScore =
            singleChoice * singleChoiceScore +
            multipleChoice * multipleChoiceScore +
            judgment * judgmentScore +
            fillBlank * fillScore +
            shortAnswer * shortAnswerScore;

        // 显示批量生成确认对话框
        Swal.fire({
            icon: 'question',
            title: '批量生成确认',
            html: `
                <div class="text-left">
                    <p>您即将生成 <strong>${paperCount}</strong> 套试卷：</p>
                    <ul class="pl-4">
                        <li><strong>试卷标题：</strong>${paperTitle}</li>
                        <li><strong>试卷版本：</strong>${paperType === 'standard' ? '标准版' : paperType === 'teacher' ? '教师版' : '自定义版'}</li>
                        <li><strong>题目总数：</strong>${singleChoice + multipleChoice + judgment + fillBlank + shortAnswer} 题</li>
                        <li><strong>试卷总分：</strong>${totalScore} 分</li>
                    </ul>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle mr-2"></i>
                        每套试卷的题目都会不同，确保试卷的多样性。
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: `生成 ${paperCount} 套试卷`,
            cancelButtonText: '取消',
            confirmButtonColor: '#007bff'
        }).then((result) => {
            if (result.isConfirmed) {
                executeBatchGeneration(paperTitle, paperType, paperCount, knowledgeConfigs,
                                     singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                     singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                     totalScore, $btn, originalText);
            }
        });
    }

    //  执行批量生成
    function executeBatchGeneration(paperTitle, paperType, paperCount, knowledgeConfigs,
                                   singleChoice, multipleChoice, judgment, fillBlank, shortAnswer,
                                   singleChoiceScore, multipleChoiceScore, judgmentScore, fillScore, shortAnswerScore,
                                   totalScore, $btn, originalText) {

        // 显示批量生成进度
        $('#paperGenerationModal').modal('hide');

        let currentPaper = 0;
        const generatedPapers = [];
        const failedPapers = [];

        // 显示进度对话框
        const progressDialog = Swal.fire({
            title: '批量生成进行中...',
            html: `
                <div class="text-center">
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" id="batchProgress">
                            0 / ${paperCount}
                        </div>
                    </div>
                    <p id="batchStatus">正在生成第 1 套试卷...</p>
                    <small class="text-muted">请耐心等待，每套试卷需要几秒钟时间</small>
                </div>
            `,
            allowOutsideClick: false,
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: '停止生成'
        });

        // 禁用按钮
        $btn.html('<i class="fas fa-spinner fa-spin mr-1"></i>批量生成中...');
        $btn.prop('disabled', true);

        // 递归生成每套试卷
        function generateNextPaper() {
            if (currentPaper >= paperCount) {
                // 所有试卷生成完成
                completeBatchGeneration(generatedPapers, failedPapers, paperCount, $btn, originalText);
                return;
            }

            currentPaper++;
            const currentTitle = paperCount > 1 ? `${paperTitle} (第${currentPaper}套)` : paperTitle;

            // 更新进度
            const progress = (currentPaper / paperCount) * 100;
            $('#batchProgress').css('width', `${progress}%`).text(`${currentPaper} / ${paperCount}`);
            $('#batchStatus').text(`正在生成第 ${currentPaper} 套试卷...`);

            // 构建请求数据
            const requestData = {
                title: currentTitle,
                paperType: paperType,
                knowledgePointConfigs: knowledgeConfigs,
                totalScore: totalScore,
                topicTypeCounts: {
                    SINGLE_CHOICE: singleChoice,
                    MULTIPLE_CHOICE: multipleChoice,
                    JUDGE: judgment,
                    FILL: fillBlank,
                    SHORT: shortAnswer
                },
                typeScoreMap: {
                    SINGLE_CHOICE: singleChoiceScore,
                    MULTIPLE_CHOICE: multipleChoiceScore,
                    JUDGE: judgmentScore,
                    FILL: fillScore,
                    SHORT: shortAnswerScore
                },
                difficultyCriteria: {
                    easy: parseInt($('#easyPercentage').val()) / 100,
                    medium: parseInt($('#mediumPercentage').val()) / 100,
                    hard: parseInt($('#hardPercentage').val()) / 100
                }
            };

            // 发送生成请求
            $.ajax({
                url: '/api/papers/generate',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    if (response && response.success && response.data) {
                        generatedPapers.push({
                            title: currentTitle,
                            paperId: response.data.id,
                            data: response.data
                        });
                        console.log(`✅ 第 ${currentPaper} 套试卷生成成功:`, currentTitle);
                    } else {
                        failedPapers.push({
                            title: currentTitle,
                            error: response.message || '生成失败'
                        });
                        console.error(`❌ 第 ${currentPaper} 套试卷生成失败:`, currentTitle);
                    }

                    // 延迟一下再生成下一套，避免服务器压力过大
                    setTimeout(() => {
                        generateNextPaper();
                    }, 1000);
                },
                error: function(xhr) {
                    failedPapers.push({
                        title: currentTitle,
                        error: '网络错误或服务器错误'
                    });
                    console.error(`❌ 第 ${currentPaper} 套试卷生成失败:`, currentTitle, xhr);

                    // 延迟一下再生成下一套
                    setTimeout(() => {
                        generateNextPaper();
                    }, 1000);
                }
            });
        }

        // 开始生成第一套试卷
        generateNextPaper();
    }

    //  完成批量生成
    function completeBatchGeneration(generatedPapers, failedPapers, totalCount, $btn, originalText) {
        // 恢复按钮
        $btn.html(originalText);
        $btn.prop('disabled', false);

        const successCount = generatedPapers.length;
        const failedCount = failedPapers.length;

        console.log(`🎉 批量生成完成: 成功 ${successCount} 套, 失败 ${failedCount} 套`);

        // 显示完成结果
        let resultHtml = `
            <div class="text-left">
                <div class="alert alert-${successCount > 0 ? 'success' : 'danger'}">
                    <h5><i class="fas fa-${successCount > 0 ? 'check-circle' : 'exclamation-triangle'} mr-2"></i>批量生成完成</h5>
                    <p class="mb-0">
                        总计 ${totalCount} 套试卷：
                        <strong class="text-success">${successCount} 套成功</strong>
                        ${failedCount > 0 ? `，<strong class="text-danger">${failedCount} 套失败</strong>` : ''}
                    </p>
                </div>
        `;

        if (successCount > 0) {
            resultHtml += `
                <h6><i class="fas fa-check-circle text-success mr-2"></i>成功生成的试卷：</h6>
                <ul class="list-group mb-3">
            `;
            generatedPapers.forEach((paper, index) => {
                resultHtml += `
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>${paper.title}</span>
                        <a href="/papers/preview/${paper.paperId}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-eye mr-1"></i>查看
                        </a>
                    </li>
                `;
            });
            resultHtml += `</ul>`;
        }

        if (failedCount > 0) {
            resultHtml += `
                <h6><i class="fas fa-exclamation-triangle text-danger mr-2"></i>生成失败的试卷：</h6>
                <ul class="list-group mb-3">
            `;
            failedPapers.forEach((paper, index) => {
                resultHtml += `
                    <li class="list-group-item">
                        <strong>${paper.title}</strong><br>
                        <small class="text-danger">${paper.error}</small>
                    </li>
                `;
            });
            resultHtml += `</ul>`;
        }

        resultHtml += `</div>`;

        Swal.fire({
            icon: successCount > 0 ? 'success' : 'error',
            title: '批量生成完成',
            html: resultHtml,
            confirmButtonText: '确定',
            width: '600px'
        }).then(() => {
            // 刷新历史记录
            if (typeof window.loadPaperHistory === 'function') {
                window.loadPaperHistory();
            }
        });
    }
});
