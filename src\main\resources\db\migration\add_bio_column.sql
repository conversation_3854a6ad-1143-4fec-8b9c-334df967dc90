-- 数据库结构更新脚本
-- 执行时间: 2024-05-24
-- 功能: 添加用户个人简介字段，确保下载记录功能正常工作

-- 1. 检查并添加bio字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user'
    AND COLUMN_NAME = 'bio'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE user ADD COLUMN bio VARCHAR(500) COMMENT "个人简介"',
    'SELECT "Column bio already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 检查用户表和下载记录表的外键约束
SELECT
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
AND (
    (TABLE_NAME = 'paper_downloads' AND COLUMN_NAME = 'user_id')
    OR (TABLE_NAME = 'paper_downloads' AND COLUMN_NAME = 'paper_id')
);

-- 3. 验证数据类型匹配
SELECT
    'user' as table_name,
    'id' as column_name,
    DATA_TYPE,
    COLUMN_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user'
AND COLUMN_NAME = 'id'

UNION ALL

SELECT
    'paper_downloads' as table_name,
    'user_id' as column_name,
    DATA_TYPE,
    COLUMN_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'paper_downloads'
AND COLUMN_NAME = 'user_id';

-- 4. 验证bio字段是否添加成功
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user'
AND COLUMN_NAME = 'bio';
