:root {
    --primary-color: #0066FF;
    --nav-height: 60px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

.navbar {
    height: var(--nav-height);
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    padding: 0 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.nav-brand a {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    margin-left: 3rem;
    display: flex;
    gap: 2rem;
}

.nav-item {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.nav-item:hover {
    background: rgba(0,102,255,0.1);
    color: var(--primary-color);
}

.nav-item.active {
    color: var(--primary-color);
    background: rgba(0,102,255,0.1);
}

.nav-user {
    margin-left: auto;
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
}

.user-info:hover {
    background: rgba(0,0,0,0.05);
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    background-color: #f0f0f0;
}

.large-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    background-color: #f0f0f0;
}

/* 添加头像加载动画 */
.avatar, .large-avatar {
    transition: opacity 0.3s ease;
}

.avatar:not([src]), .large-avatar:not([src]) {
    opacity: 0;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    padding: 0.5rem;
    display: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.5rem 1rem;
    color: #333;
    text-decoration: none;
    white-space: nowrap;
    border-radius: 4px;
}

.dropdown-menu a:hover {
    background: rgba(0,0,0,0.05);
}

.main-content {
    margin-top: var(--nav-height);
    padding: 2rem;
}

.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
} 