package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.Book;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import java.util.List;

public interface BookService {
    
    /**
     * Search books by keyword in title or type
     */
    ApiResponse<?> searchBooks(String keyword);
    
    /**
     * Get all books
     */
    ApiResponse<?> getAllBooks();
    
    /**
     * Get book by id
     */
    ApiResponse<?> getBookById(Long id);
    
    /**
     * Create a new book
     */
    ApiResponse<?> createBook(Book book, String token);
    
    /**
     * Update an existing book
     */
    ApiResponse<?> updateBook(Long id, Book book, String token);
    
    /**
     * Delete a book
     */
    ApiResponse<?> deleteBook(Long id, String token);
    
    /**
     * Get books by type
     */
    ApiResponse<?> getBooksByType(String type);
    
    /**
     * Get books by user
     */
    ApiResponse<?> getBooksByUser(String token);

    List<Book> searchBooks(String query, int maxResults);
} 