package com.edu.maizi_edu_sys.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

/**
 * 简单的PDF生成器
 * 使用iText直接生成PDF，避免HTML解析问题
 * 支持数学公式和中文字体
 */
@Slf4j
public class SimplePdfGenerator {

    /**
     * 将HTML内容转换为PDF
     * @param html HTML内容
     * @return PDF资源
     */
    public static Resource convertHtmlToPdf(String html) {
        log.info("开始使用简单PDF生成器转换HTML到PDF");
        
        try {
            // 1. 处理数学公式
            String processedHtml = processMathFormulas(html);
            log.debug("数学公式处理完成");
            
            // 2. 解析HTML内容
            PdfContent content = parseHtmlContent(processedHtml);
            log.debug("HTML内容解析完成");
            
            // 3. 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Document document = new Document(PageSize.A4, 50, 50, 50, 50);
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            
            // 设置中文字体
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font titleFont = new Font(baseFont, 18, Font.BOLD);
            Font headerFont = new Font(baseFont, 14, Font.BOLD);
            Font normalFont = new Font(baseFont, 12, Font.NORMAL);
            Font mathFont = new Font(baseFont, 12, Font.ITALIC);
            
            // 添加标题
            if (content.title != null && !content.title.trim().isEmpty()) {
                Paragraph title = new Paragraph(content.title, titleFont);
                title.setAlignment(Element.ALIGN_CENTER);
                title.setSpacingAfter(20);
                document.add(title);
            }
            
            // 添加基本信息
            if (content.info != null && !content.info.trim().isEmpty()) {
                Paragraph info = new Paragraph(content.info, normalFont);
                info.setAlignment(Element.ALIGN_CENTER);
                info.setSpacingAfter(20);
                document.add(info);
            }
            
            // 添加题目内容
            for (QuestionContent question : content.questions) {
                // 题型标题
                if (question.typeTitle != null) {
                    Paragraph typeTitle = new Paragraph(question.typeTitle, headerFont);
                    typeTitle.setSpacingBefore(15);
                    typeTitle.setSpacingAfter(10);
                    document.add(typeTitle);
                }
                
                // 题目标题
                Paragraph questionTitle = new Paragraph(question.title, normalFont);
                questionTitle.setSpacingAfter(8);
                document.add(questionTitle);
                
                // 选项
                for (String option : question.options) {
                    Paragraph optionPara = new Paragraph("    " + option, normalFont);
                    optionPara.setSpacingAfter(3);
                    document.add(optionPara);
                }
                
                // 答案区域
                if (question.answerArea != null) {
                    Paragraph answerArea = new Paragraph(question.answerArea, normalFont);
                    answerArea.setSpacingAfter(10);
                    document.add(answerArea);
                }
                
                // 添加间距
                document.add(new Paragraph(" ", normalFont));
            }
            
            document.close();
            
            byte[] pdfBytes = outputStream.toByteArray();
            log.info("PDF生成成功，大小: {} bytes", pdfBytes.length);
            
            return new ByteArrayResource(pdfBytes);
            
        } catch (Exception e) {
            log.error("PDF生成失败: {}", e.getMessage(), e);
            return createErrorPdf("PDF生成失败: " + e.getMessage());
        }
    }

    /**
     * 处理数学公式
     */
    private static String processMathFormulas(String html) {
        if (html == null) return "";
        
        String processed = html;
        
        // 将LaTeX公式转换为普通文本表示
        processed = processed.replaceAll("\\$\\$([^$]+?)\\$\\$", "【公式】$1【/公式】");
        processed = processed.replaceAll("\\$([^$]+?)\\$", "($1)");
        
        // 处理常见的数学符号
        processed = processed.replace("\\pi", "π")
                           .replace("\\alpha", "α")
                           .replace("\\beta", "β")
                           .replace("\\gamma", "γ")
                           .replace("\\delta", "δ")
                           .replace("\\theta", "θ")
                           .replace("\\lambda", "λ")
                           .replace("\\mu", "μ")
                           .replace("\\sigma", "σ")
                           .replace("\\phi", "φ")
                           .replace("\\omega", "ω")
                           .replace("\\infty", "∞")
                           .replace("\\sum", "Σ")
                           .replace("\\int", "∫")
                           .replace("\\frac", "")
                           .replace("\\sqrt", "√")
                           .replace("\\rightarrow", "→")
                           .replace("\\leftarrow", "←")
                           .replace("\\Rightarrow", "⇒")
                           .replace("\\Leftarrow", "⇐");
        
        return processed;
    }

    /**
     * 解析HTML内容
     */
    private static PdfContent parseHtmlContent(String html) {
        PdfContent content = new PdfContent();
        
        if (html == null || html.trim().isEmpty()) {
            return content;
        }
        
        // 简单的HTML解析
        String[] lines = html.split("\n");
        QuestionContent currentQuestion = null;
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            
            // 提取标题
            if (line.contains("<h1>") && line.contains("</h1>")) {
                content.title = extractTextBetweenTags(line, "h1");
            }
            // 提取基本信息
            else if (line.contains("总分:") || line.contains("难度:")) {
                content.info = stripHtmlTags(line);
            }
            // 提取题型标题
            else if (line.contains("<h2>") && line.contains("</h2>")) {
                String typeTitle = extractTextBetweenTags(line, "h2");
                currentQuestion = new QuestionContent();
                currentQuestion.typeTitle = typeTitle;
                content.questions.add(currentQuestion);
            }
            // 提取题目标题
            else if (line.contains("question-title")) {
                if (currentQuestion == null) {
                    currentQuestion = new QuestionContent();
                    content.questions.add(currentQuestion);
                }
                currentQuestion.title = stripHtmlTags(line);
            }
            // 提取选项
            else if (line.contains("option-item")) {
                if (currentQuestion != null) {
                    currentQuestion.options.add(stripHtmlTags(line));
                }
            }
            // 提取答案区域
            else if (line.contains("answer-line") || line.contains("answer-area")) {
                if (currentQuestion != null) {
                    currentQuestion.answerArea = stripHtmlTags(line);
                }
            }
        }
        
        return content;
    }

    /**
     * 提取HTML标签之间的文本
     */
    private static String extractTextBetweenTags(String html, String tag) {
        String startTag = "<" + tag + ">";
        String endTag = "</" + tag + ">";
        int start = html.indexOf(startTag);
        int end = html.indexOf(endTag);
        
        if (start >= 0 && end > start) {
            return html.substring(start + startTag.length(), end).trim();
        }
        return "";
    }

    /**
     * 移除HTML标签
     */
    private static String stripHtmlTags(String html) {
        if (html == null) return "";
        return html.replaceAll("<[^>]+>", "").trim();
    }

    /**
     * 创建错误PDF
     */
    private static Resource createErrorPdf(String errorMessage) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Document document = new Document(PageSize.A4);
            PdfWriter.getInstance(document, outputStream);
            document.open();
            
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font font = new Font(baseFont, 14, Font.NORMAL);
            
            Paragraph error = new Paragraph("PDF生成失败: " + errorMessage, font);
            error.setAlignment(Element.ALIGN_CENTER);
            document.add(error);
            
            document.close();
            return new ByteArrayResource(outputStream.toByteArray());
            
        } catch (Exception e) {
            log.error("创建错误PDF失败: {}", e.getMessage());
            return new ByteArrayResource("PDF生成失败".getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * PDF内容结构
     */
    private static class PdfContent {
        String title = "";
        String info = "";
        java.util.List<QuestionContent> questions = new java.util.ArrayList<>();
    }

    /**
     * 题目内容结构
     */
    private static class QuestionContent {
        String typeTitle;
        String title = "";
        java.util.List<String> options = new java.util.ArrayList<>();
        String answerArea;
    }
}
