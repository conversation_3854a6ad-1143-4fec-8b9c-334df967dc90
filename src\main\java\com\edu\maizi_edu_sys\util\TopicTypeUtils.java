package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;

/**
 * Common utility for normalising and mapping various front-end / DB / enum representations of topic types
 * to the system-wide canonical keys used in algorithms:  singleChoice, multipleChoice, judgment,
 * fillBlank, shortAnswer, subjective, groupQuestion.
 */
@Slf4j
public final class TopicTypeUtils {
    private TopicTypeUtils() {}

    /**
     * Normalise an arbitrary topic type label to the canonical internal key.
     * The logic is case-insensitive, ignores underscores / spaces and falls back to the original if unknown.
     */
    public static String normalize(String typeKey) {
        if (typeKey == null) {
            return "unknown";
        }
        
        // 直接处理数据库中的原始值，无需标准化处理
        // 注意：这里的匹配需要精确，因为可能直接来自数据库查询
        switch (typeKey) {
            // 数据库可能存储的原始值
            case "choice": return "singleChoice";
            case "multiple": return "multipleChoice";
            case "judge": return "judgment";
            case "fill": return "fillBlank";
            case "short": return "shortAnswer";
            case "subjective": return "subjective";
            case "group": return "groupQuestion";
            
            // 数据库可能存储的全称或变体
            case "single_choice": return "singleChoice";
            case "multiple_choice": return "multipleChoice";
            case "judgment": return "judgment";
            case "fill_in_blanks": return "fillBlank";
            case "fill_blank": return "fillBlank";
            case "short_answer": return "shortAnswer";
            
            // 前端可能发送的格式
            case "SINGLE_CHOICE": return "singleChoice";
            case "MULTIPLE_CHOICE": return "multipleChoice";
            case "JUDGMENT": return "judgment";
            case "FILL_IN_BLANKS": return "fillBlank";
            case "FILL_BLANKS": return "fillBlank";
            case "SHORT_ANSWER": return "shortAnswer";
            
            // 数字编码匹配（如果数据库使用数字编码）
            case "1": return "singleChoice";
            case "2": return "multipleChoice";
            case "3": return "judgment";
            case "4": return "fillBlank";
            case "5": return "shortAnswer";
            case "6": return "subjective";
            case "7": return "groupQuestion";
        }
        
        // 处理前端传入的值，需要标准化处理
        // Remove whitespace and underscores, lower-case everything for easy matching
        String key = typeKey.replaceAll("[\\s_]+", "").toLowerCase();
        switch (key) {
            // single choice
            case "singlechoice": case "choice": case "single": case "singleselect":
            case "单选题": case "单选": case "select": case "single_choice": case "singlechoicequestion":
                return "singleChoice";
            // multiple choice
            case "multiplechoice": case "multiple": case "multi": case "multiselect":
            case "multiplechoicequestion": case "多选题": case "多选": case "multiple_choice":
                return "multipleChoice";
            // judgment / true-false
            case "judgment": case "judge": case "truefalse": case "tf": case "truefalsequestion":
            case "判断题": case "判断": case "true_false": case "judgement":
                return "judgment";
            // fill in blank
            case "fillblank": case "fillintheblank": case "fill": case "blank": case "填空题": case "填空":
            case "fillinblanks": case "fill_in_blanks": case "fillintheblanks": case "fillblanks":
                return "fillBlank";
            // short answer / essay
            case "shortanswer": case "essay": case "text": case "short": case "简答题": case "简答": case "short_answer":
                return "shortAnswer";
            // subjective long answer
            case "subjective": case "论述题": case "论述": case "主观题": case "主观":
                return "subjective";
            // group / composite question
            case "groupquestion": case "group": case "composite": case "组合题": case "组合":
                return "groupQuestion";
            default:
                log.warn("TopicTypeUtils: Unrecognised topic type key '{}'. Returning as-is.", typeKey);
                return typeKey; // leave untouched – helps logging further downstream
        }
    }
}
