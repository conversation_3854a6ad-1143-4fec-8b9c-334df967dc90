<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript文件调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .file-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔧 JavaScript文件调试页面</h1>
    
    <div class="test-section">
        <h3>JavaScript文件加载测试</h3>
        <div id="js-files-result" class="result">检测中...</div>
    </div>

    <div class="test-section">
        <h3>API端点测试</h3>
        <button onclick="testAllAPIs()">测试所有API</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>路径测试</h3>
        <div id="path-result" class="result"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 测试JavaScript文件是否能加载
        function testJavaScriptFiles() {
            const files = [
                '/static/js/common.js',
                '/static/js/paper-config-management.js'
            ];
            
            const resultDiv = document.getElementById('js-files-result');
            let results = [];
            
            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            results.push(`✅ ${file} - 加载成功 (${response.status})`);
                        } else {
                            results.push(`❌ ${file} - 加载失败 (${response.status})`);
                        }
                        updateResults();
                    })
                    .catch(error => {
                        results.push(`❌ ${file} - 网络错误: ${error.message}`);
                        updateResults();
                    });
            });
            
            function updateResults() {
                resultDiv.textContent = results.join('\n');
                if (results.length === files.length) {
                    resultDiv.className = results.every(r => r.includes('✅')) ? 'result success' : 'result error';
                }
            }
        }

        // 测试所有API端点
        function testAllAPIs() {
            const apis = [
                { name: '用户认证', url: '/api/user/current' },
                { name: '配置统计', url: '/api/paper-configs/statistics' },
                { name: '配置列表', url: '/api/paper-configs' }
            ];
            
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '测试中...';
            
            let results = [];
            
            apis.forEach(api => {
                $.ajax({
                    url: api.url,
                    method: 'GET',
                    beforeSend: function(xhr) {
                        const token = localStorage.getItem('token');
                        if (token) {
                            xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                        }
                    },
                    success: function(response) {
                        results.push(`✅ ${api.name} (${api.url}) - 成功`);
                        updateAPIResults();
                    },
                    error: function(xhr) {
                        results.push(`❌ ${api.name} (${api.url}) - 失败 (${xhr.status}): ${xhr.responseText}`);
                        updateAPIResults();
                    }
                });
            });
            
            function updateAPIResults() {
                resultDiv.textContent = results.join('\n\n');
                if (results.length === apis.length) {
                    resultDiv.className = results.every(r => r.includes('✅')) ? 'result success' : 'result error';
                }
            }
        }

        // 显示路径信息
        function showPathInfo() {
            const pathInfo = {
                '当前URL': window.location.href,
                '协议': window.location.protocol,
                '主机': window.location.host,
                '路径': window.location.pathname,
                '基础URL': window.location.origin,
                'Token存在': localStorage.getItem('token') ? '是' : '否',
                'Token长度': localStorage.getItem('token') ? localStorage.getItem('token').length : 0,
                '用户信息': localStorage.getItem('currentUser') ? '存在' : '不存在'
            };
            
            const resultDiv = document.getElementById('path-result');
            resultDiv.textContent = Object.entries(pathInfo)
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n');
            resultDiv.className = 'result';
        }

        // 页面加载时运行测试
        window.onload = function() {
            testJavaScriptFiles();
            showPathInfo();
        };
    </script>
</body>
</html>
