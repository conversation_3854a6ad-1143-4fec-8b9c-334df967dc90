package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.service.PaperGenerationService;
import com.edu.maizi_edu_sys.util.HtmlToPdfConverter;
import com.edu.maizi_edu_sys.util.UltraSimplePdfGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * HTML试卷生成服务
 * 用于生成包含数学公式的HTML试卷，并转换为PDF
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HtmlPaperGenerationServiceImpl {

    private final PaperGenerationService paperGenerationService;
    private final ObjectMapper objectMapper;

    /**
     * 生成HTML格式的试卷，并转换为PDF
     *
     * @param paperId   试卷ID
     * @param paperType 试卷类型
     * @return PDF资源
     */
    public Resource generateHtmlPaperResource(Long paperId, String paperType) {
        log.info("开始生成HTML试卷PDF, id: {}, type: {}", paperId, paperType);

        // 获取试卷信息
        Paper paper = paperGenerationService.getPaperById(paperId);
        if (paper == null) {
            log.error("试卷不存在, id: {}", paperId);
            return null;
        }

        // 如果没有指定类型，则使用试卷默认类型
        if (paperType == null || paperType.isEmpty()) {
            paperType = paper.getPaperType() != null ? paper.getPaperType() : "regular";
        }

        try {
            // 获取试卷题目
            List<Topic> topics = getTopicsForPaper(paper);
            if (topics.isEmpty()) {
                log.warn("试卷 {} 中没有题目", paperId);
            }

            // 生成HTML试卷
            String htmlContent = generateHtmlPaper(paper, topics, paperType);

            // 创建完整的HTML文档
            String completeHtml = createCompleteHtmlDocument(htmlContent);
            log.debug("完整HTML文档长度: {}", completeHtml.length());

            // 转换HTML为PDF - 使用超简单PDF生成器避免Flying Saucer问题
            try {
                Resource pdfResource = UltraSimplePdfGenerator.convertHtmlToPdf(completeHtml);
                if (pdfResource != null) {
                    log.info("成功使用超简单PDF生成器生成试卷PDF, id: {}, type: {}", paperId, paperType);
                    return pdfResource;
                }
            } catch (Exception e) {
                log.warn("超简单PDF生成器失败，尝试Flying Saucer: {}", e.getMessage());
            }

            // 回退到Flying Saucer（可能失败）
            Resource pdfResource = HtmlToPdfConverter.convertHtmlToPdfWithFlyingSaucer(completeHtml);
            log.info("成功生成HTML试卷PDF, id: {}, type: {}", paperId, paperType);
            return pdfResource;
        } catch (Exception e) {
            log.error("生成HTML试卷PDF时出错, id: {}, type: {}: {}", paperId, paperType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 使用超简单PDF生成器生成试卷PDF（完全避免Flying Saucer）
     */
    public Resource generateSimplePaperResource(Long paperId, String paperType) {
        try {
            log.info("开始使用超简单PDF生成器生成试卷PDF - 试卷ID: {}, 类型: {}", paperId, paperType);

            // 获取试卷信息
            PaperDetailDTO paperDetail = paperGenerationService.getPaperDetail(paperId);
            if (paperDetail == null) {
                log.error("试卷不存在或获取失败, id: {}", paperId);
                return null;
            }

            // 从DTO创建Paper对象
            Paper paper = new Paper();
            paper.setId(paperDetail.getId());
            paper.setTitle(paperDetail.getTitle());
            paper.setTotalScore(paperDetail.getTotalScore());
            paper.setDifficulty(paperDetail.getDifficulty());
            paper.setKnowledgeId(paperDetail.getKnowledgeId());
            paper.setKnowledgeName(paperDetail.getKnowledgeName());

            List<Topic> topics = paperDetail.getTopics();

            if (topics == null || topics.isEmpty()) {
                log.warn("试卷 {} 没有题目", paperId);
                return null;
            }

            // 生成HTML内容
            String htmlContent = generateHtmlPaper(paper, topics, paperType);

            // 直接使用超简单PDF生成器
            Resource pdfResource = UltraSimplePdfGenerator.convertHtmlToPdf(htmlContent);

            if (pdfResource != null) {
                log.info("成功使用超简单PDF生成器生成试卷PDF, id: {}, type: {}", paperId, paperType);
                return pdfResource;
            } else {
                log.error("超简单PDF生成器返回null, id: {}, type: {}", paperId, paperType);
                return null;
            }

        } catch (Exception e) {
            log.error("使用超简单PDF生成器生成试卷PDF时出错, id: {}, type: {}: {}", paperId, paperType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成HTML格式的试卷（公共方法，用于调试）
     *
     * @param paper     试卷实体
     * @param topics    题目列表
     * @param paperType 试卷类型
     * @return HTML字符串
     */
    public String generateHtmlPaper(Paper paper, List<Topic> topics, String paperType) {
        log.info("开始生成HTML试卷 - 试卷ID: {}, 题目数量: {}, 试卷类型: {}",
            paper.getId(), topics.size(), paperType);

        StringBuilder html = new StringBuilder();

        // 添加试卷标题
        String title = paper.getTitle() != null ? paper.getTitle() : "未命名试卷";
        html.append("<h1>").append(title).append("</h1>");
        log.debug("添加试卷标题: {}", title);

        // 添加试卷基本信息
        String paperInfoStr = String.format("总分: %s | 难度: %.2f | %s试卷",
                paper.getTotalScore() != null ? paper.getTotalScore() : "未设置",
                paper.getDifficulty() != null ? paper.getDifficulty() : 0.0,
                getPaperTypeDisplayName(paperType));
        html.append("<div style='text-align: center; margin-bottom: 20px;'>").append(paperInfoStr).append("</div>");
        log.debug("添加试卷基本信息: {}", paperInfoStr);

        // 添加姓名栏
        if ("regular".equals(paperType) || "standard".equals(paperType)) {
            html.append("<div style='text-align: right; margin-bottom: 20px;'>姓名：________________     班级：________________</div>");
        }

        // 解析题型分值配置
        Map<String, Integer> typeScoresFromConfig = parseTypeScoreMapFromConfig(paper.getConfig());

        // 按题型分组
        Map<String, List<Topic>> topicsByType = topics.stream()
                .collect(Collectors.groupingBy(t -> mapTopicType(t.getType())));

        // 判断是否需要显示题目和答案
        boolean showQuestions = "regular".equals(paperType) || "standard".equals(paperType);
        boolean showAnswers = "teacher".equals(paperType) || "standard".equals(paperType);

        // 按顺序添加题目
        String[] orderedTypes = {"singleChoice", "multipleChoice", "judgment", "fillBlank", "shortAnswer", "groupQuestion"};
        int questionNumber = 1;

        if (showQuestions) {
            log.info("开始生成题目内容，题型顺序: {}", Arrays.toString(orderedTypes));
            for (String type : orderedTypes) {
                List<Topic> topicsOfType = topicsByType.getOrDefault(type, Collections.emptyList());
                if (topicsOfType.isEmpty()) {
                    log.debug("题型 {} 没有题目，跳过", type);
                    continue;
                }

                log.info("处理题型: {}, 题目数量: {}", type, topicsOfType.size());

                // 添加题型标题
                String typeChineseName = getChineseTopicTypeName(type);
                Integer scorePerQuestion = typeScoresFromConfig.getOrDefault(type, 0);
                Integer totalScore = scorePerQuestion * topicsOfType.size();

                html.append("<h2>").append(typeChineseName)
                        .append(" (每题").append(scorePerQuestion).append("分，共").append(totalScore).append("分)")
                        .append("</h2>");
                log.debug("添加题型标题: {}", typeChineseName);

                // 添加题目
                for (Topic topic : topicsOfType) {
                    log.debug("生成题目 {}: ID={}, 标题={}, 类型={}",
                        questionNumber, topic.getId(),
                        topic.getTitle() != null ? topic.getTitle().substring(0, Math.min(50, topic.getTitle().length())) : "无标题",
                        topic.getType());
                    String questionHtml = generateQuestionHtml(topic, questionNumber++, type);
                    html.append(questionHtml);
                    log.debug("题目HTML长度: {}", questionHtml.length());
                }
            }
        } else {
            log.info("showQuestions=false，跳过题目内容生成");
        }

        // 如果是教师试卷类型，则添加答案部分
        if (showAnswers) {
            html.append("<div style='page-break-before: always;'></div>");
            html.append("<h1>参考答案与解析</h1>");

            int answerNumber = 1;
            for (String type : orderedTypes) {
                List<Topic> topicsOfType = topicsByType.getOrDefault(type, Collections.emptyList());
                if (topicsOfType.isEmpty()) {
                    continue;
                }

                String typeChineseName = getChineseTopicTypeName(type);
                html.append("<h2>").append(typeChineseName).append(" 答案").append("</h2>");

                for (Topic topic : topicsOfType) {
                    html.append("<div class='answer'>");
                    html.append("<div class='answer-header'>").append(answerNumber++).append(". 答案: ");
                    html.append("<span style='color: red; font-weight: bold;'>").append(topic.getAnswer()).append("</span></div>");

                    if (topic.getAnalysis() != null && !topic.getAnalysis().isEmpty()) {
                        html.append("<div class='analysis' style='margin-left: 20px;'>解析: ").append(topic.getAnalysis()).append("</div>");
                    }

                    html.append("</div>");
                }
            }
        }

        return html.toString();
    }

    /**
     * 生成题目的HTML
     *
     * @param topic         题目实体
     * @param questionNumber 题号
     * @param type          题目类型
     * @return HTML字符串
     */
    private String generateQuestionHtml(Topic topic, int questionNumber, String type) {
        log.debug("生成题目HTML - 题号: {}, 题目ID: {}, 类型: {}, 标题: {}",
            questionNumber, topic.getId(), type,
            topic.getTitle() != null ? topic.getTitle().substring(0, Math.min(30, topic.getTitle().length())) : "null");

        StringBuilder html = new StringBuilder();
        html.append("<div class='question'>");

        // 确保题目标题不为空
        String title = topic.getTitle() != null ? topic.getTitle() : "[题目标题缺失]";
        html.append("<div class='question-title'>").append(questionNumber).append(". ").append(title).append("</div>");

        // 根据题型添加不同的内容
        if ("singleChoice".equals(type) || "multipleChoice".equals(type)) {
            log.debug("处理选择题，题目ID: {}, 选项数据: {}", topic.getId(), topic.getOptions());
            try {
                // 解析选项
                List<Map<String, String>> options = parseOptions(topic.getOptions());
                log.debug("题目 {} 解析得到 {} 个选项", topic.getId(), options.size());

                if (!options.isEmpty()) {
                    html.append("<div class='topic-options mt-3'>");
                    for (Map<String, String> option : options) {
                        String key = option.getOrDefault("key", "");
                        String name = option.getOrDefault("name", "");
                        log.debug("题目 {} 添加选项: {} - {}", topic.getId(), key, name);

                        html.append("<div class='option-item mb-2 p-2 border rounded'>")
                            .append("<strong>").append(key).append(".</strong> ")
                            .append(name)
                            .append("</div>");
                    }
                    html.append("</div>");
                } else {
                    log.warn("题目 {} 没有有效选项，原始数据: {}", topic.getId(), topic.getOptions());
                    html.append("<div class='topic-options mt-3'>");
                    html.append("<div class='alert alert-warning'>[选项数据为空]</div>");
                    html.append("</div>");
                }
            } catch (Exception e) {
                log.error("解析题目 {} 选项失败，原始数据: {}, 错误: {}", topic.getId(), topic.getOptions(), e.getMessage(), e);
                html.append("<div class='topic-options mt-3'>");
                html.append("<div class='alert alert-danger'>(选项解析失败: ").append(e.getMessage()).append(")</div>");
                html.append("</div>");
            }
        } else if ("judgment".equals(type)) {
            html.append("<div class='options'>");
            html.append("<div class='option-item'>A. 正确</div>");
            html.append("<div class='option-item'>B. 错误</div>");
            html.append("</div>");
        } else if ("fillBlank".equals(type)) {
            html.append("<div class='answer-line'>答案: __________________</div>");
        } else if ("shortAnswer".equals(type)) {
            html.append("<div class='answer-area'>答案:</div>");
            // 添加答题空间
            html.append("<div style='height: 100px; border: 1px solid #ddd; margin: 10px 0;'></div>");
        } else if ("groupQuestion".equals(type)) {
            // 组合题处理
            html.append("<div class='group-question'>");
            html.append("<div class='group-note'>注：本题为组合题，请按要求作答。</div>");
            html.append("</div>");
        } else if ("subjective".equals(type)) {
            // 主观题处理
            html.append("<div class='subjective-area'>答案:</div>");
            html.append("<div style='height: 120px; border: 1px solid #ddd; margin: 10px 0;'></div>");
        } else {
            log.warn("未知题型: {}", type);
            html.append("<div class='unknown-type'>[未知题型: ").append(type).append("]</div>");
        }

        html.append("</div>");
        String result = html.toString();
        log.debug("题目 {} HTML生成完成，长度: {}", questionNumber, result.length());
        return result;
    }

    /**
     * 创建完整的HTML文档
     * @param content HTML内容
     * @return 完整的HTML文档
     */
    private String createCompleteHtmlDocument(String content) {
        StringBuilder html = new StringBuilder();
        // 使用XHTML DOCTYPE以确保Flying Saucer兼容性
        html.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        html.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n");
        html.append("<html xmlns=\"http://www.w3.org/1999/xhtml\" xml:lang=\"zh-CN\" lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/>\n");
        html.append("    <title>试卷</title>\n");
        html.append("    <style type=\"text/css\">\n");
        html.append(getEmbeddedCss());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        // 确保内容符合XHTML标准
        String cleanContent = cleanHtmlForXhtml(content);
        html.append(cleanContent);
        html.append("\n</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 清理HTML内容以符合XHTML标准
     * @param content 原始HTML内容
     * @return 清理后的XHTML内容
     */
    private String cleanHtmlForXhtml(String content) {
        if (content == null) {
            return "";
        }

        // 确保所有标签都正确闭合
        String cleaned = content
                // 确保自闭合标签格式正确
                .replaceAll("<br>", "<br />")
                .replaceAll("<hr>", "<hr />")
                .replaceAll("<img([^>]*)>", "<img$1 />")
                .replaceAll("<input([^>]*)>", "<input$1 />")
                .replaceAll("<meta([^>]*)>", "<meta$1 />")
                // 转义特殊字符
                .replaceAll("&(?!amp;|lt;|gt;|quot;|apos;)", "&amp;")
                // 确保属性值都有引号
                .replaceAll("(\\w+)=([^\"'\\s>]+)", "$1=\"$2\"");

        return cleaned;
    }

    /**
     * 获取嵌入式CSS样式
     * @return CSS样式字符串
     */
    private String getEmbeddedCss() {
        return "body { font-family: 'SimSun', 'Microsoft YaHei', serif; font-size: 14px; line-height: 1.6; margin: 20px; color: #333; background-color: white; }\n" +
                "h1 { text-align: center; font-size: 24px; margin-bottom: 20px; color: #000; font-weight: bold; border-bottom: 2px solid #000; padding-bottom: 10px; }\n" +
                "h2 { font-size: 18px; margin-top: 30px; margin-bottom: 15px; color: #000; font-weight: bold; border-left: 4px solid #007cba; padding-left: 10px; }\n" +
                ".question { margin-bottom: 25px; page-break-inside: avoid; padding: 15px; border: 1px solid #e0e0e0; background-color: #fafafa; }\n" +
                ".question-title { font-weight: bold; margin-bottom: 12px; line-height: 1.8; color: #000; font-size: 15px; }\n" +
                ".options { margin-left: 25px; margin-bottom: 15px; margin-top: 10px; }\n" +
                ".option-item { margin-bottom: 8px; line-height: 1.6; padding: 3px 0; color: #333; }\n" +
                ".answer-line { margin-top: 15px; margin-bottom: 15px; font-weight: bold; border-bottom: 1px solid #ccc; padding-bottom: 5px; }\n" +
                ".answer-area { margin-top: 15px; margin-bottom: 15px; font-weight: bold; color: #000; }\n" +
                ".answer { margin-bottom: 20px; page-break-inside: avoid; padding: 10px; background-color: #f9f9f9; border-left: 3px solid #28a745; }\n" +
                ".answer-header { font-weight: bold; margin-bottom: 8px; color: #000; }\n" +
                ".analysis { color: #666; font-style: italic; margin-top: 8px; padding-left: 15px; border-left: 2px solid #ddd; }\n" +
                ".unknown-type { color: red; font-weight: bold; background-color: #ffe6e6; padding: 5px; border: 1px solid #ff9999; }\n" +
                ".group-question { margin-top: 10px; margin-bottom: 10px; }\n" +
                ".group-note { font-style: italic; color: #666; margin-bottom: 10px; }\n" +
                ".subjective-area { margin-top: 15px; margin-bottom: 15px; font-weight: bold; color: #000; }\n" +
                "@page { size: A4; margin: 2cm; }\n" +
                "@media print { body { font-size: 12px; background-color: white !important; } .question { border: none !important; background-color: white !important; } }";
    }

    /**
     * 解析选项JSON
     */
    private List<Map<String, String>> parseOptions(String optionsJson) {
        try {
            if (optionsJson == null || optionsJson.isEmpty()) {
                log.debug("选项JSON为空");
                return Collections.emptyList();
            }

            log.debug("开始解析选项JSON: {}", optionsJson);
            List<Map<String, String>> options = objectMapper.readValue(optionsJson, new TypeReference<List<Map<String, String>>>() {});
            log.debug("成功解析选项，数量: {}", options.size());

            // 验证选项内容
            for (int i = 0; i < options.size(); i++) {
                Map<String, String> option = options.get(i);
                log.debug("选项 {}: key={}, name={}", i, option.get("key"), option.get("name"));
            }

            return options;
        } catch (Exception e) {
            log.error("解析选项JSON失败，原始数据: {}, 错误: {}", optionsJson, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 解析题型分值配置
     */
    private Map<String, Integer> parseTypeScoreMapFromConfig(String configJson) {
        try {
            if (configJson == null || configJson.isEmpty()) {
                return Collections.emptyMap();
            }

            Map<String, Object> config = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});

            // 尝试从 globalTypeScoreMap 获取分值映射（新格式）
            Object rawTypeScoreMap = config.get("globalTypeScoreMap");
            if (rawTypeScoreMap == null) {
                // 回退到旧格式
                rawTypeScoreMap = config.get("typeScoreMap");
            }

            if (rawTypeScoreMap instanceof Map) {
                Map<?, ?> rawMap = (Map<?, ?>) rawTypeScoreMap;
                Map<String, Integer> typeScores = new HashMap<>();

                for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
                    if (entry.getKey() instanceof String && entry.getValue() != null) {
                        try {
                            String frontendKey = (String) entry.getKey();
                            Integer score = Integer.parseInt(entry.getValue().toString());

                            // 将前端格式的键名转换为数据库格式
                            String dbKey = mapTopicType(frontendKey);
                            typeScores.put(dbKey, score);

                            log.debug("映射题型分值: {} ({}) -> {} 分", frontendKey, dbKey, score);
                        } catch (NumberFormatException nfe) {
                            log.warn("Skipping invalid score value for key '{}': {}", entry.getKey(), entry.getValue());
                        }
                    }
                }

                log.info("解析配置得到的分值映射（数据库格式）: {}", typeScores);
                return typeScores;
            }
            return Collections.emptyMap();
        } catch (Exception e) {
            log.warn("解析题型分值配置失败: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 获取试卷类型显示名称
     */
    private String getPaperTypeDisplayName(String paperType) {
        if (paperType == null) return "普通";

        switch (paperType) {
            case "regular": return "学生";
            case "teacher": return "教师";
            case "standard": return "标准";
            default: return paperType;
        }
    }

    /**
     * 获取题型中文名称 - 使用统一的TopicTypeMapper工具类
     */
    private String getChineseTopicTypeName(String type) {
        if (type == null) return "未知题型";

        // 先转换为数据库格式，再获取中文名称
        String dbFormat = com.edu.maizi_edu_sys.util.TopicTypeMapper.toDbFormat(type);
        return com.edu.maizi_edu_sys.util.TopicTypeMapper.getChineseName(dbFormat);
    }

    /**
     * 映射题目类型
     */
    /**
     * 获取试卷的题目列表
     *
     * @param paper 试卷实体
     * @return 题目列表
     */
    private List<Topic> getTopicsForPaper(Paper paper) {
        try {
            // 直接从PaperGenerationService获取题目详情
            PaperDetailDTO paperDetail = paperGenerationService.getPaperDetail(paper.getId());
            if (paperDetail != null && paperDetail.getTopics() != null) {
                log.info("成功获取试卷 {} 的题目，共 {} 道题", paper.getId(), paperDetail.getTopics().size());
                return paperDetail.getTopics();
            } else {
                log.warn("试卷 {} 没有题目或获取失败", paper.getId());
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("获取试卷 {} 的题目时出错: {}", paper.getId(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 映射题目类型 - 使用统一的TopicTypeMapper工具类
     */
    private String mapTopicType(String type) {
        if (type == null) return "";

        // 使用统一的TopicTypeMapper工具类，转换为数据库标准格式
        return com.edu.maizi_edu_sys.util.TopicTypeMapper.toDbFormat(type);
    }
}
