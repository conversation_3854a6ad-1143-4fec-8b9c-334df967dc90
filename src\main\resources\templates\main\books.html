<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>教材资源管理 - 麦子教育</title>
    <!-- Add environment check script for earliest possible detection -->
    <script src="/static/js/environment-check.js"></script>
    <!-- Add avatar fix script in the head for earliest possible execution -->
    <script src="/static/js/avatar-fix.js"></script>
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/chat.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 默认头像修复脚本 -->
    <script src="/static/js/default-avatar-fix.js"></script>
    <style>
        /* 书籍管理页面样式 */
        .books-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .books-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .books-title {
            font-size: 1.75rem;
            color: #2c3e50;
            margin: 0;
        }
        
        .search-bar {
            display: flex;
            flex: 1;
            max-width: 600px;
            position: relative;
        }
        
        .search-bar input {
            flex: 1;
            border-radius: 4px 0 0 4px;
            border: 1px solid #ced4da;
            padding: 0.5rem 1rem;
            font-size: 1rem;
        }
        
        .search-bar button {
            border-radius: 0 4px 4px 0;
            border: 1px solid #0d6efd;
            background-color: #0d6efd;
            color: white;
            padding: 0.5rem 1rem;
        }
        
        .add-book-btn {
            background-color: #198754;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .book-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }
        
        .filter-item {
            background-color: #e9ecef;
            color: #495057;
            border-radius: 20px;
            padding: 0.25rem 1rem;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.2s ease;
        }
        
        .filter-item:hover {
            background-color: #dee2e6;
        }
        
        .filter-item.active {
            background-color: #0d6efd;
            color: white;
        }
        
        .books-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .book-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: white;
        }
        
        .book-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-4px);
        }
        
        .book-card-header {
            padding: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .book-title {
            font-size: 1.25rem;
            margin: 0;
            color: #2c3e50;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .book-type {
            display: inline-block;
            background-color: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        
        .book-card-body {
            padding: 1rem;
            flex: 1;
        }
        
        .book-description {
            color: #6c757d;
            margin: 0;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .book-card-footer {
            padding: 1rem;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            gap: 0.5rem;
        }
        
        .book-action-btn {
            flex: 1;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }
        
        .pagination {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .pagination li {
            margin: 0 0.25rem;
        }
        
        .pagination a {
            display: block;
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            color: #0d6efd;
            text-decoration: none;
        }
        
        .pagination a:hover {
            background-color: #e9ecef;
        }
        
        .pagination .active a {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .empty-state-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .empty-state-title {
            font-size: 1.5rem;
            color: #343a40;
            margin-bottom: 0.5rem;
        }
        
        .empty-state-message {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }
        
        /* 添加书籍模态框样式 */
        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .modal-title {
            color: #2c3e50;
        }
        
        .modal-footer {
            border-top: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
        }
        
        .form-text {
            color: #6c757d;
        }
        
        /* 书籍详情模态框 */
        .book-details-container {
            padding: 1rem;
        }
        
        .book-details-header {
            margin-bottom: 1.5rem;
        }
        
        .book-details-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .book-details-type {
            display: inline-block;
            background-color: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        
        .book-details-meta {
            margin-top: 1rem;
            color: #6c757d;
            font-size: 0.875rem;
        }
        
        .book-details-description {
            margin-bottom: 1.5rem;
        }
        
        .book-details-description h5 {
            font-size: 1rem;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .book-details-url {
            margin-top: 1.5rem;
        }
        
        .book-details-url h5 {
            font-size: 1rem;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .book-url-link {
            word-break: break-all;
            color: #0d6efd;
        }
        
        /* 加载状态 */
        .loading-overlay {
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 1100;
        }
        
        .spinner-container {
            text-align: center;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <a href="/">Maizi EDU</a>
        </div>
        <div class="nav-menu">
            <a href="/main/chat" class="nav-item">出题</a>
            <a href="/topics/upload-topics" class="nav-item">上传</a>
            <a href="/paper/generate" class="nav-item">组卷</a>
            <a href="/paper/check" class="nav-item">查重</a>
            <a href="/topics/bank" class="nav-item">题库</a>
            <a href="/main/books" class="nav-item active">教材资源</a>
        </div>
        <div class="nav-user">
            <div class="user-info">
                <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                <span class="username">加载中...</span>
            </div>
            <div class="dropdown-menu">
                <a href="/user/profile">个人信息</a>
                <a href="#" id="logout">退出登录</a>
            </div>
        </div>
    </nav>

    <div class="books-container">
        <div class="books-header">
            <h1 class="books-title">教材资源管理</h1>
            <div class="search-bar">
                <input type="text" id="searchInput" placeholder="搜索教材...">
                <button id="searchBtn"><i class="bi bi-search"></i> 搜索</button>
            </div>
            <button class="add-book-btn" data-bs-toggle="modal" data-bs-target="#addBookModal">
                <i class="bi bi-plus-circle"></i> 添加教材
            </button>
        </div>
        
        <div class="book-filters">
            <div class="filter-item active" data-filter="all">全部</div>
            <div class="filter-item" data-filter="my">我的教材</div>
        </div>
        
        <div id="booksContainer">
            <!-- 书籍卡片将在这里动态加载 -->
        </div>
        
        <div class="pagination-container">
            <ul class="pagination" id="pagination">
                <!-- 分页按钮将在这里动态加载 -->
            </ul>
        </div>
    </div>
    
    <!-- 添加教材模态框 -->
    <div class="modal fade" id="addBookModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新教材</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addBookForm">
                        <div class="mb-3">
                            <label for="knowId" class="form-label">知识点ID</label>
                            <input type="number" class="form-control" id="knowId" required>
                            <div class="form-text">请在麦子教育后台登录后查看【题库管理】->【知识点列表】->【知识点分类】获取知识点ID</div>
                        </div>
                        <div class="mb-3">
                            <label for="bookTitle" class="form-label">教材标题</label>
                            <input type="text" class="form-control" id="bookTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="bookType" class="form-label">教材类型</label>
                            <input type="text" class="form-control" id="bookType" placeholder="例如：语文、数学、信息技术" required>
                        </div>
                        <div class="mb-3">
                            <label for="bookUrl" class="form-label">教材链接</label>
                            <input type="url" class="form-control" id="bookUrl" placeholder="输入教材的网址链接" required>
                        </div>
                        <div class="mb-3">
                            <label for="bookDescription" class="form-label">教材描述</label>
                            <textarea class="form-control" id="bookDescription" rows="4" placeholder="描述教材的内容、章节等信息" required></textarea>
                            <div class="form-text">描述将保持原始格式，换行符会被保留</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAddBook">添加</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 教材详情模态框 -->
    <div class="modal fade" id="bookDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">教材详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="bookDetailsContent">
                    <!-- 教材详情将在这里动态显示 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="selectBookBtn">选择此教材</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status"></div>
            <p class="mt-2">加载中...</p>
        </div>
    </div>
    
    <script>
        // 获取认证令牌
        function getAuthToken() {
            return localStorage.getItem('token') || '';
        }
        
        // 显示提示信息
        function showToast(title, message, type = 'success') {
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <strong>${title}</strong>: ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            
            const toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                const container = document.createElement('div');
                container.id = 'toastContainer';
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(container);
            }
            
            $('#toastContainer').append(toastHtml);
            const toastElement = document.querySelector('#toastContainer .toast:last-child');
            const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
            toast.show();
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingOverlay').show();
        }
        
        // 隐藏加载状态
        function hideLoading() {
            $('#loadingOverlay').hide();
        }
        
        // 获取所有教材
        function loadBooks(page = 1, filter = 'all') {
            showLoading();
            
            let url = '/api/v1/books';
            if (filter === 'my') {
                url = '/api/v1/books/user';
            }
            
            $.ajax({
                url: url,
                method: 'GET',
                headers: {
                    'Authorization': getAuthToken()
                },
                success: function(response) {
                    hideLoading();
                    if (response.code === 200 && response.data) {
                        displayBooks(response.data);
                    } else {
                        showEmptyState('加载失败', response.message || '无法加载教材列表');
                    }
                },
                error: function() {
                    hideLoading();
                    showEmptyState('加载失败', '请检查网络连接后重试');
                }
            });
        }
        
        // 搜索教材
        function searchBooks(query) {
            if (!query.trim()) {
                loadBooks();
                return;
            }
            
            showLoading();
            
            $.ajax({
                url: '/api/v1/books/search',
                method: 'GET',
                data: { query: query },
                success: function(response) {
                    hideLoading();
                    if (response.code === 200 && response.data) {
                        displayBooks(response.data);
                    } else {
                        showEmptyState('未找到结果', '没有找到匹配的教材');
                    }
                },
                error: function() {
                    hideLoading();
                    showEmptyState('搜索失败', '请检查网络连接后重试');
                }
            });
        }
        
        // 展示教材卡片
        function displayBooks(books) {
            if (!books || books.length === 0) {
                showEmptyState('暂无教材', '暂时没有教材资源');
                return;
            }
            
            const booksHtml = `
                <div class="books-grid">
                    ${books.map(book => `
                        <div class="book-card" data-book-id="${book.id}" data-know-id="${book.knowId || ''}">
                            <div class="book-card-header">
                                <h3 class="book-title">${book.title}</h3>
                                <span class="book-type">${book.type || '未分类'}</span>
                            </div>
                            <div class="book-card-body">
                                <p class="book-description">${book.description || '暂无描述'}</p>
                            </div>
                            <div class="book-card-footer">
                                <button class="btn btn-outline-primary book-action-btn view-details-btn" data-book-id="${book.id}">
                                    <i class="bi bi-info-circle"></i> 查看详情
                                </button>
                                <button class="btn btn-primary select-book-btn" 
                                        data-book-id="${book.id}" 
                                        data-book-url="${book.url}" 
                                        data-book-title="${book.title}"
                                        data-know-id="${book.knowId || ''}">
                                    选择
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            $('#booksContainer').html(booksHtml);
            
            // 绑定详情按钮事件
            $('.view-details-btn').click(function() {
                const bookId = $(this).data('book-id');
                viewBookDetails(bookId);
            });
            
            // 绑定选择按钮事件
            $('.select-book-btn').click(function() {
                const bookId = $(this).data('book-id');
                const bookUrl = $(this).data('book-url');
                const bookTitle = $(this).data('book-title');
                const knowId = $(this).data('know-id');
                selectBook(bookId, bookUrl, bookTitle, knowId);
            });
        }
        
        // 显示空状态
        function showEmptyState(title, message) {
            const emptyStateHtml = `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="bi bi-journal-x"></i>
                    </div>
                    <h3 class="empty-state-title">${title}</h3>
                    <p class="empty-state-message">${message}</p>
                    <button class="btn btn-primary add-book-btn" data-bs-toggle="modal" data-bs-target="#addBookModal">
                        <i class="bi bi-plus-circle"></i> 添加新教材
                    </button>
                </div>
            `;
            
            $('#booksContainer').html(emptyStateHtml);
        }
        
        // 查看教材详情
        function viewBookDetails(bookId) {
            showLoading();
            
            $.ajax({
                url: `/api/v1/books/${bookId}`,
                method: 'GET',
                success: function(response) {
                    hideLoading();
                    if (response.code === 200 && response.data) {
                        const book = response.data;
                        const detailsHtml = `
                            <div class="book-details-container">
                                <div class="book-details-header">
                                    <h3 class="book-details-title">${book.title}</h3>
                                    <span class="book-details-type">${book.type || '未分类'}</span>
                                    <div class="book-details-meta">
                                        <div>知识点ID：${book.knowId}</div>
                                        <div>创建时间：${new Date(book.createdAt).toLocaleString()}</div>
                                        <div>更新时间：${new Date(book.updatedAt).toLocaleString()}</div>
                                    </div>
                                </div>
                                <div class="book-details-description">
                                    <h5>教材描述：</h5>
                                    <div class="p-3 bg-light rounded">${book.description ? book.description.replace(/\n/g, '<br>') : '暂无描述'}</div>
                                </div>
                                <div class="book-details-url">
                                    <h5>教材链接：</h5>
                                    <a href="${book.url}" target="_blank" class="book-url-link">${book.url}</a>
                                </div>
                            </div>
                        `;
                        
                        $('#bookDetailsContent').html(detailsHtml);
                        $('#selectBookBtn').data('book-id', book.id).data('book-url', book.url);
                        $('#bookDetailsModal').modal('show');
                    } else {
                        showToast('错误', '获取教材详情失败', 'error');
                    }
                },
                error: function() {
                    hideLoading();
                    showToast('错误', '获取教材详情失败，请重试', 'error');
                }
            });
        }
        
        // 修改selectBook函数
        function selectBook(bookId, bookUrl, bookTitle, knowId) {
            // 检查用户是否已登录
            const token = getAuthToken();
            if (!token) {
                showToast('请先登录后再选择教材', 'warning');
                
                // 保存选择的教材ID，登录后可以恢复
                localStorage.setItem('pendingBookSelection', JSON.stringify({
                    id: bookId,
                    title: bookTitle
                }));
                
                // 跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/login?redirect=' + encodeURIComponent('/main/books');
                }, 1500);
                return;
            }
            
            // 显示加载状态
            showToast('正在创建新对话...', 'info');
            
            // 直接创建新对话
            $.ajax({
                url: '/api/chat/create',
                method: 'POST',
                headers: {
                    'Authorization': getAuthToken(),
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    title: `基于《${bookTitle || '未命名教材'}》的对话`,
                    bookId: bookId
                }),
                success: function(response) {
                    if (response.code === 200 && response.data && response.data.id) {
                        // 跳转到新创建的对话
                        window.location.href = `/main/chat?id=${response.data.id}`;
                    } else {
                        showToast('创建对话成功，请刷新页面', 'success');
                        setTimeout(() => { window.location.reload(); }, 1000);
                    }
                },
                error: function(xhr) {
                    console.error('创建对话失败:', xhr);
                    showToast('创建对话失败，请稍后重试', 'danger');
                    
                    // 保存教材信息，以便用户手动创建
                    localStorage.setItem('selectedBookInfo', JSON.stringify({
                        id: bookId,
                        title: bookTitle
                    }));
                    
                    // 跳转到聊天页面
                    setTimeout(() => {
                        window.location.href = '/main/chat';
                    }, 1500);
                }
            });
        }
        
        // 添加新教材
        function submitNewBook() {
            const bookData = {
                knowId: parseInt($('#knowId').val(), 10),
                title: $('#bookTitle').val(),
                type: $('#bookType').val(),
                url: $('#bookUrl').val(),
                description: $('#bookDescription').val()
            };
            
            // 验证表单
            if (!bookData.title || !bookData.url) {
                showToast('错误', '请填写必填字段', 'error');
                return;
            }
            
            showLoading();
            
            $.ajax({
                url: '/api/v1/books',
                method: 'POST',
                headers: {
                    'Authorization': getAuthToken(),
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(bookData),
                success: function(response) {
                    hideLoading();
                    if (response.code === 200) {
                        $('#addBookModal').modal('hide');
                        $('#addBookForm')[0].reset();
                        showToast('成功', '教材添加成功');
                        loadBooks(); // 重新加载教材列表
                    } else {
                        showToast('错误', response.message || '添加失败', 'error');
                    }
                },
                error: function(xhr) {
                    hideLoading();
                    const errorMsg = xhr.responseJSON?.message || '添加教材失败，请重试';
                    showToast('错误', errorMsg, 'error');
                }
            });
        }
        
        // 初始化页面
        $(document).ready(function() {
            // 修复所有图像路径
            if (typeof window.fixAllImages === 'function') {
                window.fixAllImages();
            }
            
            // 加载教材列表
            loadBooks();
            
            // 绑定搜索事件
            $('#searchBtn').click(function() {
                const query = $('#searchInput').val().trim();
                searchBooks(query);
            });
            
            // 回车搜索
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    $('#searchBtn').click();
                }
            });
            
            // 绑定筛选事件
            $('.filter-item').click(function() {
                $('.filter-item').removeClass('active');
                $(this).addClass('active');
                const filter = $(this).data('filter');
                loadBooks(1, filter);
            });
            
            // 绑定添加教材事件
            $('#submitAddBook').click(function() {
                submitNewBook();
            });
            
            // 模态框中的选择按钮
            $('#selectBookBtn').click(function() {
                const bookId = $(this).data('book-id');
                const bookUrl = $(this).data('book-url');
                const bookTitle = $(this).data('book-title');
                const knowId = $(this).data('know-id');
                selectBook(bookId, bookUrl, bookTitle, knowId);
                $('#bookDetailsModal').modal('hide');
            });
            
            // 加载用户信息
            if (getAuthToken()) {
                $.ajax({
                    url: '/api/user/info',
                    headers: {
                        'Authorization': getAuthToken()
                    },
                    success: function(response) {
                        if (response.code === 200 && response.data) {
                            // 更新用户名
                            $('.username').text(response.data.username);
                            
                            // 更新头像，确保使用正确的路径
                            if (response.data.avatar) {
                                let avatarUrl = response.data.avatar;
                                
                                // 使用fixAvatarUrl处理路径（如果可用）
                                if (typeof window.fixAvatarUrl === 'function') {
                                    avatarUrl = window.fixAvatarUrl(avatarUrl);
                                } else {
                                    // 备用修复方案
                                    if (avatarUrl.includes('/main/avatars/')) {
                                        avatarUrl = avatarUrl.replace('/main/avatars/', '/uploads/avatars/');
                                    } else if (!avatarUrl.startsWith('/') && !avatarUrl.includes('://')) {
                                        // 确保简单文件名有正确前缀
                                        if (avatarUrl.match(/^avatars\/\d{14}_[a-f0-9]+\.[a-z]+$/)) {
                                            avatarUrl = '/uploads/' + avatarUrl;
                                        } else if (avatarUrl.match(/^\d{14}_[a-f0-9]+\.[a-z]+$/)) {
                                            avatarUrl = '/uploads/avatars/' + avatarUrl;
                                        }
                                    }
                                }
                                
                                // 确保路径完整，否则使用默认头像
                                if (!avatarUrl.startsWith('/') && !avatarUrl.includes('://')) {
                                    avatarUrl = '/uploads/avatars/' + avatarUrl;
                                }
                                
                                // 修复重复的avatars路径
                                if (avatarUrl.includes('/avatars/avatars/')) {
                                    avatarUrl = avatarUrl.replace('/avatars/avatars/', '/avatars/');
                                }
                                
                                // 设置头像路径
                                $('.avatar').attr('src', avatarUrl);
                            }
                        }
                    }
                });
            }
            
            // 退出登录
            $('#logout').click(function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                window.location.href = '/login';
            });
        });
    </script>
    
    <!-- 添加Toast容器 -->
    <div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>
</body>
</html>
