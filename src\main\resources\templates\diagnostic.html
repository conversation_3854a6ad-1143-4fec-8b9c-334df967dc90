<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Diagnostics</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Template Diagnostics</h1>
    
    <div class="section">
        <h2>Template Information</h2>
        <p><strong>Template Name:</strong> <span th:text="${templateName}">template-name</span></p>
        <p><strong>Timestamp:</strong> <span th:text="${timestamp}">timestamp</span></p>
    </div>
    
    <div class="section">
        <h2>Request Information</h2>
        <table>
            <tr>
                <th>Property</th>
                <th>Value</th>
            </tr>
            <tr th:each="info : ${requestInfo}">
                <td th:text="${info.key}">key</td>
                <td th:text="${info.value}">value</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>HTTP Headers</h2>
        <table>
            <tr>
                <th>Header</th>
                <th>Value</th>
            </tr>
            <tr th:each="header : ${headers}">
                <td th:text="${header.key}">key</td>
                <td th:text="${header.value}">value</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Spring Properties</h2>
        <table>
            <tr>
                <th>Property</th>
                <th>Value</th>
            </tr>
            <tr th:each="prop : ${springProperties}">
                <td th:text="${prop.key}">key</td>
                <td th:text="${prop.value}">value</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>System Properties</h2>
        <table>
            <tr>
                <th>Property</th>
                <th>Value</th>
            </tr>
            <tr th:each="prop : ${systemProperties}">
                <td th:text="${prop.key}">key</td>
                <td th:text="${prop.value}">value</td>
            </tr>
        </table>
    </div>
</body>
</html> 