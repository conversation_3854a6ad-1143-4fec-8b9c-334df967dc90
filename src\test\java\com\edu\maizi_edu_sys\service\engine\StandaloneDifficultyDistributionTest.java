package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class StandaloneDifficultyDistributionTest {

    @InjectMocks
    private GeneticSolver geneticSolver;

    @BeforeEach
    public void setup() {
        // Set essential private fields using reflection
        ReflectionTestUtils.setField(geneticSolver, "POPULATION_SIZE", 20);
        ReflectionTestUtils.setField(geneticSolver, "MAX_GENERATIONS", 10);
        ReflectionTestUtils.setField(geneticSolver, "MIN_GENERATIONS", 5);
        ReflectionTestUtils.setField(geneticSolver, "CROSSOVER_RATE", 0.8);
        ReflectionTestUtils.setField(geneticSolver, "MUTATION_RATE", 0.1);
        ReflectionTestUtils.setField(geneticSolver, "TOURNAMENT_SIZE", 3);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_SCORE", 0.4);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_QUALITY", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_DIFFICULTY_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_COGNITIVE_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "EARLY_TERMINATE_THRESHOLD", 0.95);
    }

    @Test
    @DisplayName("测试遗传算法难度分布与真实数据")
    public void testDifficultyDistributionWithRealData() {
        // Create test data mimicking real database values (difficulty range 0.1-0.5)
        List<Topic> testTopics = createRealisticTestTopics();
        
        // Print initial test topic information
        printTopicsInformation(testTopics);
        
        // Setup target scores and distributions
        int targetScore = 100;
        Map<String, Integer> typeScores = new HashMap<>();
        typeScores.put("单选题", 50);
        typeScores.put("多选题", 30);
        typeScores.put("判断题", 20);
        
        Map<String, Double> difficultyDistribution = new HashMap<>();
        difficultyDistribution.put("easy", 0.3);
        difficultyDistribution.put("medium", 0.5);
        difficultyDistribution.put("hard", 0.2);
        
        // Execute genetic algorithm
        List<Topic> result = geneticSolver.solve(
                testTopics,
                targetScore,
                typeScores,
                difficultyDistribution,
                Collections.emptyMap(),
                Collections.emptyMap(),
                Collections.emptyList()
        );
        
        // Verify results
        assertNotNull(result, "Result should not be null");
        assertFalse(result.isEmpty(), "Result should not be empty");
        
        // Analyze the total score
        int totalScore = result.stream().mapToInt(Topic::getScore).sum();
        System.out.println("Total score: " + totalScore + " (target: " + targetScore + ")");
        
        // Analyze difficulty distribution
        Map<String, List<Topic>> resultByDifficulty = analyzeDifficultyDistribution(result);
        
        // Print detailed distribution analysis
        System.out.println("\n难度分布结果分析:");
        difficultyDistribution.forEach((difficulty, targetPercent) -> {
            List<Topic> topicsInCategory = resultByDifficulty.get(difficulty);
            double actualPercent = result.isEmpty() ? 0 : (double) topicsInCategory.size() / result.size();
            System.out.printf("%s难度: 目标 %.1f%%, 实际 %.1f%% (%d题)\n", 
                    difficulty, targetPercent * 100, actualPercent * 100, topicsInCategory.size());
            
            // Print detailed topic information for this difficulty
            System.out.println("  题目详情:");
            topicsInCategory.forEach(topic -> {
                System.out.printf("  - 题目ID: %d, 类型: %s, 难度: %.1f, 分值: %d\n", 
                        topic.getId(), topic.getType(), topic.getDifficulty(), topic.getScore());
            });
        });
        
        // Verify the score distribution by type
        Map<String, Integer> actualTypeScores = result.stream()
                .collect(Collectors.groupingBy(Topic::getType, 
                        Collectors.summingInt(Topic::getScore)));
        
        System.out.println("\n题型分数分布:");
        typeScores.forEach((type, targetTypeScore) -> {
            Integer actualScore = actualTypeScores.getOrDefault(type, 0);
            System.out.printf("%s: 目标 %d, 实际 %d\n", type, targetTypeScore, actualScore);
        });
    }
    
    private void printTopicsInformation(List<Topic> topics) {
        System.out.println("\n============ 测试题目集信息 ============");
        System.out.println("总题目数量: " + topics.size());
        
        // 按难度统计
        Map<Double, Integer> difficultyCountMap = new HashMap<>();
        for (Topic topic : topics) {
            difficultyCountMap.merge(topic.getDifficulty(), 1, Integer::sum);
        }
        
        System.out.println("\n按难度值分布:");
        difficultyCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    double percent = (double) entry.getValue() / topics.size() * 100;
                    System.out.printf("难度 %.1f: %d题 (%.1f%%)\n", 
                            entry.getKey(), entry.getValue(), percent);
                });
        
        // 按难度类别统计
        Map<String, List<Topic>> topicsByCategory = analyzeDifficultyDistribution(topics);
        
        System.out.println("\n按难度类别分布:");
        topicsByCategory.forEach((category, topicsInCategory) -> {
            double percent = (double) topicsInCategory.size() / topics.size() * 100;
            System.out.printf("%s难度: %d题 (%.1f%%)\n", 
                    category, topicsInCategory.size(), percent);
        });
        
        // 按题型统计
        Map<String, Long> typeCountMap = topics.stream()
                .collect(Collectors.groupingBy(Topic::getType, Collectors.counting()));
        
        System.out.println("\n按题型分布:");
        typeCountMap.forEach((type, count) -> {
            double percent = (double) count / topics.size() * 100;
            System.out.printf("%s: %d题 (%.1f%%)\n", type, count, percent);
        });
        
        // 按题型+难度统计
        Map<String, Map<String, Long>> typeAndDifficultyMap = topics.stream()
                .collect(Collectors.groupingBy(
                        Topic::getType,
                        Collectors.groupingBy(
                                topic -> getDifficultyCategory(topic.getDifficulty()),
                                Collectors.counting()
                        )
                ));
        
        System.out.println("\n按题型+难度分布:");
        typeAndDifficultyMap.forEach((type, diffMap) -> {
            System.out.println(type + ":");
            diffMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey(Comparator.comparing(diff -> 
                            diff.equals("easy") ? 1 : diff.equals("medium") ? 2 : 3)))
                    .forEach(entry -> {
                        double percent = (double) entry.getValue() / 
                                typeCountMap.get(type) * 100;
                        System.out.printf("  - %s难度: %d题 (%.1f%%)\n", 
                                entry.getKey(), entry.getValue(), percent);
                    });
        });
        
        // 按照分值统计
        Map<Integer, Long> scoreCountMap = topics.stream()
                .collect(Collectors.groupingBy(Topic::getScore, Collectors.counting()));
        
        System.out.println("\n按分值分布:");
        scoreCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    double percent = (double) entry.getValue() / topics.size() * 100;
                    System.out.printf("%d分: %d题 (%.1f%%)\n", 
                            entry.getKey(), entry.getValue(), percent);
                });
        
        System.out.println("\n题目详细列表 (前20题):");
        topics.stream().limit(20).forEach(topic -> {
            System.out.printf("题目ID: %d, 类型: %s, 难度: %.1f (%s), 分值: %d\n", 
                    topic.getId(), topic.getType(), topic.getDifficulty(), 
                    getDifficultyCategory(topic.getDifficulty()), topic.getScore());
        });
        
        System.out.println("============ 测试题目集信息结束 ============\n");
    }
    
    private Map<String, List<Topic>> analyzeDifficultyDistribution(List<Topic> topics) {
        Map<String, List<Topic>> resultByDifficulty = new HashMap<>();
        resultByDifficulty.put("easy", new ArrayList<>());
        resultByDifficulty.put("medium", new ArrayList<>());
        resultByDifficulty.put("hard", new ArrayList<>());
        
        for (Topic topic : topics) {
            double difficulty = topic.getDifficulty();
            String category = getDifficultyCategory(difficulty);
            resultByDifficulty.get(category).add(topic);
        }
        
        return resultByDifficulty;
    }
    
    private List<Topic> createRealisticTestTopics() {
        List<Topic> topics = new ArrayList<>();
        String[] types = {"单选题", "多选题", "判断题"};
        double[] difficulties = {0.1, 0.2, 0.3, 0.4, 0.5};
        
        // Create 150 topics with realistic distribution
        for (int i = 1; i <= 150; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            
            // Distribute types evenly
            String type = types[i % 3];
            topic.setType(type);
            
            // Distribute difficulties realistically
            double difficulty = difficulties[i % 5];
            topic.setDifficulty(difficulty);
            
            // Set score based on type and difficulty
            int score;
            if (type.equals("单选题")) {
                score = 2 + (int)(difficulty * 2); // 2-3 points
            } else if (type.equals("多选题")) {
                score = 3 + (int)(difficulty * 3); // 3-5 points
            } else { // 判断题
                score = 1 + (int)(difficulty * 2); // 1-2 points
            }
            topic.setScore(score);
            
            topics.add(topic);
        }
        
        return topics;
    }
    
    // Helper method for difficulty categorization - must match GeneticSolver implementation
    private String getDifficultyCategory(double difficulty) {
        if (difficulty <= 0.2) return "easy";
        if (difficulty <= 0.4) return "medium";
        return "hard";
    }
} 