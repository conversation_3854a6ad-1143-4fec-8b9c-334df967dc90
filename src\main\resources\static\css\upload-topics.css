body {
    background-color: #f8f9fa;
}

.page-header h2 {
    font-weight: 300;
}

.editor-card .card-body, .preview-card .card-body {
    padding: 0; /* CodeMirror and preview will handle padding */
}

.CodeMirror {
    height: calc(100vh - 320px); /* Adjust based on your layout */
    min-height: 300px;
    border: none;
    border-radius: 0 0 0.25rem 0.25rem; /* Match card footer */
}

.editor-actions {
    padding: 0.75rem 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.preview-card .card-body {
    padding: 1rem;
}

.preview-container-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 320px); /* Match CodeMirror height */
    border: 1px dashed #ced4da;
    border-radius: .25rem;
    background-color: #fff;
}


#previewContainer {
    font-size: 0.95rem;
    line-height: 1.6;
    background-color: #fff;
    border-radius: .25rem;
    min-height: calc(100vh - 320px); /* Match CodeMirror height */
    max-height: calc(100vh - 320px);
    overflow-y: auto;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

#previewContainer h5 { /* Topic Title */
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: #343a40;
}

#previewContainer .options-list {
    list-style-type: none;
    padding-left: 0;
}

#previewContainer .options-list li {
    margin-bottom: 0.5rem;
    padding: 0.3rem 0.5rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

#previewContainer .topic-meta {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px dashed #ced4da;
}

#previewContainer .topic-meta strong {
    color: #495057;
}

#previewContainer .topic-parse {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: #e9f5ff;
    border-left: 3px solid #0d6efd;
    border-radius: 0.25rem;
}

#previewContainer .topic-parse p:last-child {
    margin-bottom: 0;
}

/* KaTeX specific styling */
#previewContainer .katex-display {
    margin: 1em 0 !important;
    overflow-x: auto;
    overflow-y: hidden;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

#previewContainer .katex {
    font-size: 1.1em;
}

/* Markdown generated elements */
#previewContainer p {
    margin-bottom: 0.5rem;
}
#previewContainer ul, #previewContainer ol {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
}
#previewContainer code {
    background-color: #e9ecef;
    padding: 0.1em 0.3em;
    border-radius: 3px;
    font-size: 90%;
}
#previewContainer pre code {
    display: block;
    padding: 0.5rem;
    overflow-x: auto;
}

.preview-controls .badge {
    font-size: 0.9em;
    vertical-align: middle;
}