2025-05-19 00:12:33.571 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 00:13:08.892 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='集合考点  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=195, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 00:13:08.892 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=195, questionCount=null, includeShortAnswer=true)], title=集合考点  专项练习, totalScore=99, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 00:13:08.923 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 250. Earmarked: 0, General Pool (unique): 250. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=10}
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 250 topics. IDs: [173743, 173744, 173745, 173746, 173747, 173748, 173749, 173750, 173751, 173752, 173753, 173754, 173755, 173756, 173757, 173758, 173759, 173760, 173761, 173762, 173763, 173764, 173765, 173766, 173767, 173768, 173769, 173770, 173771, 173772, 173773, 173774, 173775, 173776, 173777, 173778, 173779, 173780, 173781, 173782, 173783, 173784, 173785, 173786, 173787, 173788, 173789, 173790, 173791, 173792, 173793, 173794, 173795, 173796, 173797, 173798, 173799, 173800, 173801, 173802, 174936, 174937, 174938, 174939, 174941, 174942, 174944, 174945, 174947, 174948, 174950, 174951, 174953, 174954, 174956, 174958, 174959, 174960, 174961, 174962, 174963, 174964, 174965, 174966, 174967, 174968, 174969, 174970, 174971, 174972, 175051, 175052, 175053, 175054, 175055, 175056, 175057, 175058, 175059, 175060, 175061, 175062, 175063, 175064, 175065, 175066, 175067, 175068, 175069, 175070, 175071, 175072, 175073, 175074, 175075, 175076, 175077, 175078, 175079, 175080, 175081, 175082, 175083, 175084, 175085, 175086, 175087, 175088, 175089, 175090, 175091, 175092, 175093, 175094, 175095, 175096, 175097, 175098, 175099, 175100, 175101, 175102, 175103, 175104, 175105, 175106, 175107, 175108, 175109, 175110, 175111, 175112, 175113, 175114, 175115, 175116, 175117, 175118, 175119, 175120, 175123, 175124, 175125, 175126, 175127, 175128, 175129, 175130, 175131, 175132, 175133, 175134, 175135, 175136, 175137, 175138, 175139, 175140, 175141, 175142, 175144, 175145, 175146, 175147, 175148, 175149, 175150, 175151, 175152, 175153, 175154, 175155, 175156, 175157, 175158, 175160, 175161, 175162, 175163, 175164, 175183, 175184, 175186, 175187, 175189, 175190, 175192, 175194, 175198, 175203, 175205, 175209, 175240, 175242, 175244, 175246, 175249, 175250, 175253, 175254, 175255, 175257, 175258, 175260, 175294, 175296, 175298, 175299, 175301, 175303, 175304, 175305, 175306, 175307, 175308, 175309, 175310, 175312, 175313, 175997, 175999, 176000, 176002, 176004, 176006, 176008, 176009, 176011, 176012, 176064]
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 250 topics available for GA (input size was 250). MinReuseIntervalDays: null
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 250 topics remain.
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 250 candidate topics...
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 00:13:08.938 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 00:13:08.951 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6508349514563108
2025-05-19 00:13:08.965 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7116428571428572
2025-05-19 00:13:08.975 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.735972602739726
2025-05-19 00:13:08.988 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7530606060606062
2025-05-19 00:13:08.998 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 40: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.772
2025-05-19 00:13:09.006 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 49: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7902142857142858
2025-05-19 00:13:09.006 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 68ms. Best solution fitness: {:.4f}, Selected 0.7902142857142858 topics with total score: 56.
2025-05-19 00:13:09.006 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 56 topics.
2025-05-19 00:13:09.007 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 168, Target score: 99. Number of topics: 56
2025-05-19 00:13:09.007 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (168) is greater than target (99). Attempting to find a subset that sums to target score.
2025-05-19 00:13:09.007 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Successfully found a subset of 33 topics matching target score 99. Original list size: 56.
2025-05-19 00:13:09.007 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - After DP adjustment: 33 topics, attempting to match target score 99.
2025-05-19 00:13:09.007 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Actual score after DP: 99
2025-05-19 00:13:09.007 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=7, judgment=26}, 目标={SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 10 topics needed
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 10 topics needed
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 10 topics needed
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=33
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 33 topics.
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 33. Requested global counts (for warning reference): {SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 00:13:09.008 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 00:13:09.009 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=7, judgment=26}
2025-05-19 00:13:09.010 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=10, judgment=10, multipleChoice=10, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 00:13:09.013 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 7 topics of type 'singleChoice' (requested 10)
2025-05-19 00:13:09.013 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 10 topics of type 'judgment' (requested 10)
2025-05-19 00:13:09.013 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 33. Final result size: 17. Actual counts per type after enforcement: {singleChoice=7, judgment=10}
2025-05-19 00:13:09.013 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 99) after enforcing type counts...
2025-05-19 00:13:09.013 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 51, Target score: 99. Number of topics: 17
2025-05-19 00:13:09.013 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 17 topics, actual score: 51 (target score: 99)
2025-05-19 00:13:09.035 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 29. Title: 集合考点  专项练习
2025-05-19 00:13:09.066 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 17 topics.
2025-05-19 00:13:10.452 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 29
2025-05-19 00:13:10.456 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 29
2025-05-19 00:13:10.462 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 29
2025-05-19 00:13:10.589 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:13:10.626 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=29, pages=3, current=1, size=10, records=10
2025-05-19 00:13:10.938 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 29
2025-05-19 00:13:10.940 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 29
2025-05-19 00:13:10.941 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 29
2025-05-19 00:55:57.905 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 00:56:12.682 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:56:12.732 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=29, pages=3, current=1, size=10, records=10
2025-05-19 00:56:12.741 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:56:12.749 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=29, pages=3, current=1, size=10, records=10
2025-05-19 00:56:32.704 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='识记类  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=190, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 00:56:32.704 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=190, questionCount=null, includeShortAnswer=true)], title=识记类  专项练习, totalScore=59, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 00:56:32.747 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 857. Earmarked: 0, General Pool (unique): 857. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=5}
2025-05-19 00:56:32.774 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 857 topics. IDs: [172949, 172971, 172998, 173013, 173084, 173103, 173122, 173144, 173161, 173178, 173208, 173219, 173268, 173286, 173305, 173339, 173345, 173347, 173349, 173351, 173352, 173354, 173358, 173359, 173360, 173363, 173364, 173366, 173367, 173369, 173371, 173373, 173374, 173375, 173377, 173378, 173380, 173382, 173384, 173386, 173388, 173392, 173394, 173407, 173415, 173421, 173423, 174242, 174258, 174285, 174665, 174684, 174723, 174896, 174912, 174917, 174919, 174920, 174957, 174982, 174993, 175121, 175122, 175143, 175159, 175165, 175169, 175191, 175199, 175239, 175251, 175259, 175286, 175302, 175311, 175314, 175322, 175326, 175329, 175332, 175334, 175336, 175341, 175386, 175390, 175391, 175392, 175393, 175394, 175395, 175396, 175397, 175398, 175399, 175400, 175401, 175402, 175403, 175404, 175405, 175406, 175407, 175408, 175409, 175410, 175411, 175412, 175413, 175414, 175415, 175416, 175417, 175418, 175419, 175420, 175421, 175422, 175423, 175424, 175425, 175426, 175427, 175428, 175429, 175430, 175431, 175432, 175433, 175434, 175435, 175436, 175438, 175439, 175440, 175441, 175443, 175461, 175462, 175478, 175479, 175480, 175491, 175492, 175493, 175494, 175495, 175496, 175497, 175498, 175499, 175500, 175501, 175502, 175503, 175504, 175505, 175506, 175507, 175508, 175509, 175510, 175511, 175512, 175513, 175514, 175515, 175516, 175517, 175518, 175519, 175520, 175521, 175522, 175523, 175524, 175525, 175526, 175527, 175528, 175529, 175530, 175531, 175532, 175533, 175534, 175535, 175536, 175537, 175538, 175539, 175540, 175541, 175542, 175543, 175544, 175545, 175546, 175547, 175548, 175549, 175550, 175551, 175552, 175553, 175554, 175555, 175556, 175557, 175558, 175559, 175560, 175561, 175562, 175563, 175564, 175565, 175566, 175567, 175568, 175569, 175570, 175571, 175572, 175573, 175574, 175575, 175576, 175577, 175578, 175579, 175580, 175581, 175582, 175583, 175584, 175585, 175586, 175587, 175588, 175589, 175590, 175591, 175592, 175593, 175594, 175595, 175596, 175597, 175598, 175599, 175600, 175601, 175602, 175603, 175604, 175605, 175606, 175607, 175608, 175609, 175610, 175611, 175612, 175613, 175614, 175615, 175616, 175617, 175618, 175619, 175620, 175621, 175622, 175623, 175624, 175625, 175626, 175627, 175628, 175629, 175630, 175631, 175632, 175633, 175634, 175635, 175636, 175637, 175638, 175639, 175640, 175641, 175642, 175643, 175644, 175645, 175646, 175647, 175648, 175649, 175650, 175651, 175652, 175653, 175654, 175655, 175656, 175657, 175658, 175659, 175660, 175661, 175662, 175663, 175664, 175665, 175666, 175667, 175668, 175669, 175670, 175671, 175672, 175673, 175674, 175675, 175676, 175677, 175678, 175679, 175680, 175681, 175682, 175683, 175684, 175685, 175686, 175687, 175688, 175689, 175690, 175691, 175692, 175693, 175694, 175695, 175696, 175697, 175698, 175699, 175700, 175701, 175702, 175703, 175704, 175705, 175706, 175707, 175708, 175709, 175710, 175711, 175712, 175713, 175714, 175715, 175716, 175717, 175718, 175719, 175720, 175721, 175722, 175723, 175724, 175725, 175726, 175727, 175728, 175729, 175730, 175731, 175732, 175733, 175734, 175735, 175736, 175737, 175738, 175739, 175740, 175741, 175742, 175743, 175744, 175745, 175746, 175747, 175748, 175749, 175750, 175751, 175752, 175753, 175754, 175755, 175756, 175757, 175758, 175759, 175760, 175761, 175762, 175763, 175764, 175765, 175766, 175767, 175768, 175769, 175770, 175771, 175772, 175773, 175774, 175775, 175776, 175777, 175778, 175779, 175780, 175781, 175782, 175783, 175918, 175919, 175920, 175921, 175922, 175923, 175924, 175925, 175926, 175927, 175928, 175929, 175930, 175931, 175932, 175933, 175934, 175935, 175936, 175937, 175938, 175939, 175940, 175941, 175942, 175943, 175944, 175945, 175946, 175947, 175948, 175949, 175950, 175951, 175952, 175953, 175954, 175955, 175956, 175957, 175958, 175959, 175960, 175961, 175962, 175963, 175964, 175965, 175966, 175967, 175968, 175969, 175970, 175971, 175972, 175973, 175974, 175975, 175976, 175977, 175978, 175979, 175980, 175981, 175982, 175983, 175984, 175985, 175986, 175987, 175988, 175989, 175990, 175991, 175992, 175993, 175994, 175995, 175996, 175998, 176001, 176003, 176005, 176007, 176010, 176013, 176014, 176015, 176016, 176017, 176018, 176019, 176020, 176021, 176022, 176023, 176024, 176025, 176026, 176027, 176028, 176029, 176030, 176031, 176032, 176033, 176034, 176035, 176036, 176037, 176038, 176039, 176040, 176041, 176042, 176043, 176044, 176045, 176046, 176047, 176048, 176049, 176050, 176051, 176052, 176053, 176054, 176055, 176056, 176057, 176058, 176059, 176060, 176061, 176062, 176063, 176065, 176066, 176067, 176068, 176069, 176070, 176071, 176072, 176073, 176074, 176075, 176076, 176077, 176078, 176079, 176080, 176081, 176082, 176083, 176084, 176085, 176086, 176087, 176088, 176089, 176090, 176091, 176092, 176093, 176094, 176095, 176096, 176097, 176098, 176099, 176100, 176101, 176102, 176103, 176104, 176105, 176106, 176107, 176108, 176109, 176110, 176111, 176112, 176113, 176114, 176115, 176116, 176117, 176118, 176119, 176120, 176121, 176122, 176123, 176124, 176125, 176126, 176127, 176128, 176129, 176130, 176131, 176132, 176133, 176134, 176135, 176136, 176137, 176138, 176139, 176140, 176141, 176142, 176143, 176144, 176145, 176146, 176147, 176148, 176149, 176150, 176152, 176154, 176159, 176161, 176163, 176165, 176168, 176170, 176171, 176174, 176178, 176184, 176187, 176191, 176195, 176199, 176281, 176282, 176283, 176284, 176286, 176287, 176288, 176289, 176290, 176291, 176292, 176294, 176295, 176296, 176297, 176298, 176299, 176300, 176301, 176302, 176303, 176306, 176308, 176326, 176328, 176330, 176331, 176333, 176334, 176335, 176337, 176338, 176339, 176340, 176341, 176343, 176345, 176346, 176347, 176348, 176349, 176350, 176351, 176353, 176354, 176355, 176356, 176357, 176358, 176360, 176361, 176362, 176363, 176365, 176367, 176369, 176371, 176374, 176375, 176376, 176377, 176378, 176379, 176380, 176381, 176383, 176385, 176386, 176389, 176390, 176393, 176394, 176395, 176396, 176398, 176399, 176400, 176402, 176404, 176406, 176408, 176410, 176411, 176412, 176413, 176415, 176416, 176417, 176418, 176419, 176420, 176422, 176423, 176424, 176425, 176426, 176427, 176429, 176433, 176435, 176436, 176437, 176438, 176439, 176440, 176441, 176443, 176446, 176448, 176450, 176451, 176452, 176453, 176455, 176457, 182730, 182731, 182732, 182733, 182734, 182735, 182736, 182738, 182759, 182762, 182766, 182768, 182771, 182774, 182775, 182776, 182779, 182804, 182805, 182810, 182814, 182818, 182822, 182825, 182829, 182832, 182851, 182855, 182856, 182858, 182866, 182869, 182904, 182906, 182908, 182910, 182911, 182913, 182914, 182916, 182922, 182924, 182926, 182928, 182930, 182932, 182934, 182935, 182944, 182946, 182948, 182950, 182951, 182952, 182954, 182955, 182957, 182960, 182963, 182965, 182967, 182970, 182972, 182974, 182977, 182979, 182980, 182983, 182984, 182987]
2025-05-19 00:56:32.774 [http-nio-8081-exec-2] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 00:56:32.774 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 857 topics available for GA (input size was 857). MinReuseIntervalDays: null
2025-05-19 00:56:32.774 [http-nio-8081-exec-2] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 00:56:32.774 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 857 topics remain.
2025-05-19 00:56:32.774 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 857 candidate topics...
2025-05-19 00:56:32.775 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 00:56:32.775 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 00:56:32.802 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6767438928289993
2025-05-19 00:56:32.847 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6889410815173527
2025-05-19 00:56:32.889 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6933247863247863
2025-05-19 00:56:32.929 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6961816623821765
2025-05-19 00:56:32.970 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 40: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6978874458874459
2025-05-19 00:56:33.005 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 49: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6985635738831616
2025-05-19 00:56:33.005 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 230ms. Best solution fitness: {:.4f}, Selected 0.6985635738831616 topics with total score: 388.
2025-05-19 00:56:33.005 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 388 topics.
2025-05-19 00:56:33.006 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 1164, Target score: 59. Number of topics: 388
2025-05-19 00:56:33.006 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (1164) is greater than target (59). Attempting to find a subset that sums to target score.
2025-05-19 00:56:33.007 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - After DP adjustment: 388 topics, attempting to match target score 59.
2025-05-19 00:56:33.007 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Actual score after DP: 1164
2025-05-19 00:56:33.007 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=210, judgment=173, multipleChoice=5}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:56:33.008 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 00:56:33.008 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-19 00:56:33.008 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 5 topics needed
2025-05-19 00:56:33.008 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-19 00:56:33.008 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 00:56:33.008 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 00:56:33.009 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 1164 but target is 59, attempting to adjust scores...
2025-05-19 00:56:33.009 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 1164, Target score: 59. Number of topics: 388
2025-05-19 00:56:33.009 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (1164) is greater than target (59). Attempting to find a subset that sums to target score.
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=1164
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=388
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 388 topics.
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 388. Requested global counts (for warning reference): {SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 00:56:33.010 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 00:56:33.016 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=210, judgment=173, multipleChoice=5}
2025-05-19 00:56:33.016 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=5, judgment=5, multipleChoice=5, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 00:56:33.039 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题(singleChoice): 请求=5, 可用=210
- 判断题(judgment): 请求=5, 可用=173
- 多选题(multipleChoice): 请求=5, 可用=5
- FILL_IN_BLANKS(FILL_IN_BLANKS): 请求=3, 可用=0 [警告: 此题型在题库中完全不存在!]
- 简答题(shortAnswer): 请求=2, 可用=0 [警告: 此题型在题库中完全不存在!]
2025-05-19 00:56:33.044 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'singleChoice' (requested 5)
2025-05-19 00:56:33.044 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'multipleChoice' (requested 5)
2025-05-19 00:56:33.044 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'judgment' (requested 5)
2025-05-19 00:56:33.044 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 388. Final result size: 15. Actual counts per type after enforcement: {singleChoice=5, judgment=5, multipleChoice=5}
2025-05-19 00:56:33.044 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 59) after enforcing type counts...
2025-05-19 00:56:33.045 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 45, Target score: 59. Number of topics: 15
2025-05-19 00:56:33.045 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 15 topics, actual score: 45 (target score: 59)
2025-05-19 00:56:33.063 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 30. Title: 识记类  专项练习
2025-05-19 00:56:33.086 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 15 topics.
2025-05-19 00:56:34.628 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:56:34.642 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=30, pages=3, current=1, size=10, records=10
2025-05-19 00:56:35.770 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 30
2025-05-19 00:56:35.774 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 30
2025-05-19 00:56:35.781 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 30
2025-05-19 00:56:36.083 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 30
2025-05-19 00:56:36.084 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 30
2025-05-19 00:56:36.085 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 30
2025-05-19 00:57:13.277 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='识记类  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=190, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 00:57:13.277 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=190, questionCount=null, includeShortAnswer=true)], title=识记类  专项练习, totalScore=59, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 00:57:13.314 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 857. Earmarked: 0, General Pool (unique): 857. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=5}
2025-05-19 00:57:13.331 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 857 topics. IDs: [172949, 172971, 172998, 173013, 173084, 173103, 173122, 173144, 173161, 173178, 173208, 173219, 173268, 173286, 173305, 173339, 173345, 173347, 173349, 173351, 173352, 173354, 173358, 173359, 173360, 173363, 173364, 173366, 173367, 173369, 173371, 173373, 173374, 173375, 173377, 173378, 173380, 173382, 173384, 173386, 173388, 173392, 173394, 173407, 173415, 173421, 173423, 174242, 174258, 174285, 174665, 174684, 174723, 174896, 174912, 174917, 174919, 174920, 174957, 174982, 174993, 175121, 175122, 175143, 175159, 175165, 175169, 175191, 175199, 175239, 175251, 175259, 175286, 175302, 175311, 175314, 175322, 175326, 175329, 175332, 175334, 175336, 175341, 175386, 175390, 175391, 175392, 175393, 175394, 175395, 175396, 175397, 175398, 175399, 175400, 175401, 175402, 175403, 175404, 175405, 175406, 175407, 175408, 175409, 175410, 175411, 175412, 175413, 175414, 175415, 175416, 175417, 175418, 175419, 175420, 175421, 175422, 175423, 175424, 175425, 175426, 175427, 175428, 175429, 175430, 175431, 175432, 175433, 175434, 175435, 175436, 175438, 175439, 175440, 175441, 175443, 175461, 175462, 175478, 175479, 175480, 175491, 175492, 175493, 175494, 175495, 175496, 175497, 175498, 175499, 175500, 175501, 175502, 175503, 175504, 175505, 175506, 175507, 175508, 175509, 175510, 175511, 175512, 175513, 175514, 175515, 175516, 175517, 175518, 175519, 175520, 175521, 175522, 175523, 175524, 175525, 175526, 175527, 175528, 175529, 175530, 175531, 175532, 175533, 175534, 175535, 175536, 175537, 175538, 175539, 175540, 175541, 175542, 175543, 175544, 175545, 175546, 175547, 175548, 175549, 175550, 175551, 175552, 175553, 175554, 175555, 175556, 175557, 175558, 175559, 175560, 175561, 175562, 175563, 175564, 175565, 175566, 175567, 175568, 175569, 175570, 175571, 175572, 175573, 175574, 175575, 175576, 175577, 175578, 175579, 175580, 175581, 175582, 175583, 175584, 175585, 175586, 175587, 175588, 175589, 175590, 175591, 175592, 175593, 175594, 175595, 175596, 175597, 175598, 175599, 175600, 175601, 175602, 175603, 175604, 175605, 175606, 175607, 175608, 175609, 175610, 175611, 175612, 175613, 175614, 175615, 175616, 175617, 175618, 175619, 175620, 175621, 175622, 175623, 175624, 175625, 175626, 175627, 175628, 175629, 175630, 175631, 175632, 175633, 175634, 175635, 175636, 175637, 175638, 175639, 175640, 175641, 175642, 175643, 175644, 175645, 175646, 175647, 175648, 175649, 175650, 175651, 175652, 175653, 175654, 175655, 175656, 175657, 175658, 175659, 175660, 175661, 175662, 175663, 175664, 175665, 175666, 175667, 175668, 175669, 175670, 175671, 175672, 175673, 175674, 175675, 175676, 175677, 175678, 175679, 175680, 175681, 175682, 175683, 175684, 175685, 175686, 175687, 175688, 175689, 175690, 175691, 175692, 175693, 175694, 175695, 175696, 175697, 175698, 175699, 175700, 175701, 175702, 175703, 175704, 175705, 175706, 175707, 175708, 175709, 175710, 175711, 175712, 175713, 175714, 175715, 175716, 175717, 175718, 175719, 175720, 175721, 175722, 175723, 175724, 175725, 175726, 175727, 175728, 175729, 175730, 175731, 175732, 175733, 175734, 175735, 175736, 175737, 175738, 175739, 175740, 175741, 175742, 175743, 175744, 175745, 175746, 175747, 175748, 175749, 175750, 175751, 175752, 175753, 175754, 175755, 175756, 175757, 175758, 175759, 175760, 175761, 175762, 175763, 175764, 175765, 175766, 175767, 175768, 175769, 175770, 175771, 175772, 175773, 175774, 175775, 175776, 175777, 175778, 175779, 175780, 175781, 175782, 175783, 175918, 175919, 175920, 175921, 175922, 175923, 175924, 175925, 175926, 175927, 175928, 175929, 175930, 175931, 175932, 175933, 175934, 175935, 175936, 175937, 175938, 175939, 175940, 175941, 175942, 175943, 175944, 175945, 175946, 175947, 175948, 175949, 175950, 175951, 175952, 175953, 175954, 175955, 175956, 175957, 175958, 175959, 175960, 175961, 175962, 175963, 175964, 175965, 175966, 175967, 175968, 175969, 175970, 175971, 175972, 175973, 175974, 175975, 175976, 175977, 175978, 175979, 175980, 175981, 175982, 175983, 175984, 175985, 175986, 175987, 175988, 175989, 175990, 175991, 175992, 175993, 175994, 175995, 175996, 175998, 176001, 176003, 176005, 176007, 176010, 176013, 176014, 176015, 176016, 176017, 176018, 176019, 176020, 176021, 176022, 176023, 176024, 176025, 176026, 176027, 176028, 176029, 176030, 176031, 176032, 176033, 176034, 176035, 176036, 176037, 176038, 176039, 176040, 176041, 176042, 176043, 176044, 176045, 176046, 176047, 176048, 176049, 176050, 176051, 176052, 176053, 176054, 176055, 176056, 176057, 176058, 176059, 176060, 176061, 176062, 176063, 176065, 176066, 176067, 176068, 176069, 176070, 176071, 176072, 176073, 176074, 176075, 176076, 176077, 176078, 176079, 176080, 176081, 176082, 176083, 176084, 176085, 176086, 176087, 176088, 176089, 176090, 176091, 176092, 176093, 176094, 176095, 176096, 176097, 176098, 176099, 176100, 176101, 176102, 176103, 176104, 176105, 176106, 176107, 176108, 176109, 176110, 176111, 176112, 176113, 176114, 176115, 176116, 176117, 176118, 176119, 176120, 176121, 176122, 176123, 176124, 176125, 176126, 176127, 176128, 176129, 176130, 176131, 176132, 176133, 176134, 176135, 176136, 176137, 176138, 176139, 176140, 176141, 176142, 176143, 176144, 176145, 176146, 176147, 176148, 176149, 176150, 176152, 176154, 176159, 176161, 176163, 176165, 176168, 176170, 176171, 176174, 176178, 176184, 176187, 176191, 176195, 176199, 176281, 176282, 176283, 176284, 176286, 176287, 176288, 176289, 176290, 176291, 176292, 176294, 176295, 176296, 176297, 176298, 176299, 176300, 176301, 176302, 176303, 176306, 176308, 176326, 176328, 176330, 176331, 176333, 176334, 176335, 176337, 176338, 176339, 176340, 176341, 176343, 176345, 176346, 176347, 176348, 176349, 176350, 176351, 176353, 176354, 176355, 176356, 176357, 176358, 176360, 176361, 176362, 176363, 176365, 176367, 176369, 176371, 176374, 176375, 176376, 176377, 176378, 176379, 176380, 176381, 176383, 176385, 176386, 176389, 176390, 176393, 176394, 176395, 176396, 176398, 176399, 176400, 176402, 176404, 176406, 176408, 176410, 176411, 176412, 176413, 176415, 176416, 176417, 176418, 176419, 176420, 176422, 176423, 176424, 176425, 176426, 176427, 176429, 176433, 176435, 176436, 176437, 176438, 176439, 176440, 176441, 176443, 176446, 176448, 176450, 176451, 176452, 176453, 176455, 176457, 182730, 182731, 182732, 182733, 182734, 182735, 182736, 182738, 182759, 182762, 182766, 182768, 182771, 182774, 182775, 182776, 182779, 182804, 182805, 182810, 182814, 182818, 182822, 182825, 182829, 182832, 182851, 182855, 182856, 182858, 182866, 182869, 182904, 182906, 182908, 182910, 182911, 182913, 182914, 182916, 182922, 182924, 182926, 182928, 182930, 182932, 182934, 182935, 182944, 182946, 182948, 182950, 182951, 182952, 182954, 182955, 182957, 182960, 182963, 182965, 182967, 182970, 182972, 182974, 182977, 182979, 182980, 182983, 182984, 182987]
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 857 topics available for GA (input size was 857). MinReuseIntervalDays: null
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 857 topics remain.
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 857 candidate topics...
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 00:57:13.332 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 00:57:13.344 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6753486590038313
2025-05-19 00:57:13.388 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6841573398215733
2025-05-19 00:57:13.428 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6875609362389024
2025-05-19 00:57:13.468 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6902430213464696
2025-05-19 00:57:13.514 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 40: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.6903986928104575
2025-05-19 00:57:13.548 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 49: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.690705501618123
2025-05-19 00:57:13.548 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 216ms. Best solution fitness: {:.4f}, Selected 0.690705501618123 topics with total score: 412.
2025-05-19 00:57:13.548 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 412 topics.
2025-05-19 00:57:13.548 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 1236, Target score: 59. Number of topics: 412
2025-05-19 00:57:13.548 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (1236) is greater than target (59). Attempting to find a subset that sums to target score.
2025-05-19 00:57:13.549 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - After DP adjustment: 412 topics, attempting to match target score 59.
2025-05-19 00:57:13.549 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Actual score after DP: 1236
2025-05-19 00:57:13.549 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=225, judgment=182, multipleChoice=5}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 5 topics needed
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 1236 but target is 59, attempting to adjust scores...
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 1236, Target score: 59. Number of topics: 412
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (1236) is greater than target (59). Attempting to find a subset that sums to target score.
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=1236
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=412
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 412 topics.
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 412. Requested global counts (for warning reference): {SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 00:57:13.550 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 00:57:13.557 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=225, judgment=182, multipleChoice=5}
2025-05-19 00:57:13.557 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=5, judgment=5, multipleChoice=5, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 00:57:13.604 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题(singleChoice): 请求=5, 可用=225
- 判断题(judgment): 请求=5, 可用=182
- 多选题(multipleChoice): 请求=5, 可用=5
- FILL_IN_BLANKS(FILL_IN_BLANKS): 请求=3, 可用=0 [警告: 此题型在题库中完全不存在!]
- 简答题(shortAnswer): 请求=2, 可用=0 [警告: 此题型在题库中完全不存在!]
2025-05-19 00:57:13.617 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'singleChoice' (requested 5)
2025-05-19 00:57:13.618 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'multipleChoice' (requested 5)
2025-05-19 00:57:13.618 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'judgment' (requested 5)
2025-05-19 00:57:13.618 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 412. Final result size: 15. Actual counts per type after enforcement: {singleChoice=5, judgment=5, multipleChoice=5}
2025-05-19 00:57:13.618 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 59) after enforcing type counts...
2025-05-19 00:57:13.618 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 45, Target score: 59. Number of topics: 15
2025-05-19 00:57:13.619 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 15 topics, actual score: 45 (target score: 59)
2025-05-19 00:57:13.630 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 31. Title: 识记类  专项练习
2025-05-19 00:57:13.649 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 15 topics.
2025-05-19 00:57:15.170 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:57:15.180 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 00:57:39.602 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 00:57:39.605 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 00:57:39.607 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 00:57:40.931 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 00:57:40.934 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 00:57:40.935 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 00:58:34.390 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:58:34.390 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 00:58:34.395 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 00:58:34.395 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:13:32.836 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:13:32.836 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:13:32.839 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:13:32.840 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:13:36.973 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:13:36.973 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:13:36.993 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:13:36.998 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:13:37.001 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:13:37.001 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:13:37.040 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 31
2025-05-19 01:14:17.368 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:14:17.368 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:14:17.375 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:14:17.375 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 29
2025-05-19 01:14:17.378 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 29
2025-05-19 01:14:17.378 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 29
2025-05-19 01:14:17.855 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:14:17.855 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:14:17.863 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:14:17.864 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 29
2025-05-19 01:14:17.865 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 29
2025-05-19 01:14:17.866 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 29
2025-05-19 01:16:19.898 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:16:19.902 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:16:19.903 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:16:19.911 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:16:19.913 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:16:19.914 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:16:22.520 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:16:22.524 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:16:22.526 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:16:22.535 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:16:22.537 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:16:22.539 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:16:24.770 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:16:24.778 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:16:24.781 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:16:24.786 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:16:25.952 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:16:25.955 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:16:25.956 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:16:25.963 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 31
2025-05-19 01:16:25.965 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 31
2025-05-19 01:16:25.966 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 31
2025-05-19 01:16:36.783 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 30
2025-05-19 01:16:36.786 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 30
2025-05-19 01:16:36.787 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 30
2025-05-19 01:16:36.793 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 30
2025-05-19 01:16:36.795 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 30
2025-05-19 01:16:36.796 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 30
2025-05-19 01:16:52.493 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:16:52.498 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:16:52.500 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:16:52.503 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=31, pages=4, current=1, size=10, records=10
2025-05-19 01:16:56.646 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='集合考点  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=195, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 01:16:56.646 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=195, questionCount=null, includeShortAnswer=true)], title=集合考点  专项练习, totalScore=53, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 01:16:56.660 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 250. Earmarked: 0, General Pool (unique): 250. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 250 topics. IDs: [173743, 173744, 173745, 173746, 173747, 173748, 173749, 173750, 173751, 173752, 173753, 173754, 173755, 173756, 173757, 173758, 173759, 173760, 173761, 173762, 173763, 173764, 173765, 173766, 173767, 173768, 173769, 173770, 173771, 173772, 173773, 173774, 173775, 173776, 173777, 173778, 173779, 173780, 173781, 173782, 173783, 173784, 173785, 173786, 173787, 173788, 173789, 173790, 173791, 173792, 173793, 173794, 173795, 173796, 173797, 173798, 173799, 173800, 173801, 173802, 174936, 174937, 174938, 174939, 174941, 174942, 174944, 174945, 174947, 174948, 174950, 174951, 174953, 174954, 174956, 174958, 174959, 174960, 174961, 174962, 174963, 174964, 174965, 174966, 174967, 174968, 174969, 174970, 174971, 174972, 175051, 175052, 175053, 175054, 175055, 175056, 175057, 175058, 175059, 175060, 175061, 175062, 175063, 175064, 175065, 175066, 175067, 175068, 175069, 175070, 175071, 175072, 175073, 175074, 175075, 175076, 175077, 175078, 175079, 175080, 175081, 175082, 175083, 175084, 175085, 175086, 175087, 175088, 175089, 175090, 175091, 175092, 175093, 175094, 175095, 175096, 175097, 175098, 175099, 175100, 175101, 175102, 175103, 175104, 175105, 175106, 175107, 175108, 175109, 175110, 175111, 175112, 175113, 175114, 175115, 175116, 175117, 175118, 175119, 175120, 175123, 175124, 175125, 175126, 175127, 175128, 175129, 175130, 175131, 175132, 175133, 175134, 175135, 175136, 175137, 175138, 175139, 175140, 175141, 175142, 175144, 175145, 175146, 175147, 175148, 175149, 175150, 175151, 175152, 175153, 175154, 175155, 175156, 175157, 175158, 175160, 175161, 175162, 175163, 175164, 175183, 175184, 175186, 175187, 175189, 175190, 175192, 175194, 175198, 175203, 175205, 175209, 175240, 175242, 175244, 175246, 175249, 175250, 175253, 175254, 175255, 175257, 175258, 175260, 175294, 175296, 175298, 175299, 175301, 175303, 175304, 175305, 175306, 175307, 175308, 175309, 175310, 175312, 175313, 175997, 175999, 176000, 176002, 176004, 176006, 176008, 176009, 176011, 176012, 176064]
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 250 topics available for GA (input size was 250). MinReuseIntervalDays: null
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 250 topics remain.
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 250 candidate topics...
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 01:16:56.669 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 01:16:56.676 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7026442577030813
2025-05-19 01:16:56.700 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7377543859649123
2025-05-19 01:16:56.716 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7526439393939395
2025-05-19 01:16:56.729 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7675020080321286
2025-05-19 01:16:56.742 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 40: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7723333333333334
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 49: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7760598290598291
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 83ms. Best solution fitness: {:.4f}, Selected 0.7760598290598291 topics with total score: 78.
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 78 topics.
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 234, Target score: 53. Number of topics: 78
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (234) is greater than target (53). Attempting to find a subset that sums to target score.
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - After DP adjustment: 78 topics, attempting to match target score 53.
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Actual score after DP: 234
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=39, judgment=39}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 3 topics needed
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 234 but target is 53, attempting to adjust scores...
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 234, Target score: 53. Number of topics: 78
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (234) is greater than target (53). Attempting to find a subset that sums to target score.
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=234
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=78
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 78 topics.
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 78. Requested global counts (for warning reference): {SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 01:16:56.752 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 01:16:56.753 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=39, judgment=39}
2025-05-19 01:16:56.754 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=5, judgment=5, multipleChoice=3, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 01:16:56.758 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题(singleChoice): 请求=5, 可用=39
- 判断题(judgment): 请求=5, 可用=39
- 多选题(multipleChoice): 请求=3, 可用=0 [警告: 此题型在题库中完全不存在!]
- FILL_IN_BLANKS(FILL_IN_BLANKS): 请求=3, 可用=0 [警告: 此题型在题库中完全不存在!]
- 简答题(shortAnswer): 请求=2, 可用=0 [警告: 此题型在题库中完全不存在!]
2025-05-19 01:16:56.759 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'singleChoice' (requested 5)
2025-05-19 01:16:56.759 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'judgment' (requested 5)
2025-05-19 01:16:56.759 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 78. Final result size: 10. Actual counts per type after enforcement: {singleChoice=5, judgment=5}
2025-05-19 01:16:56.759 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 53) after enforcing type counts...
2025-05-19 01:16:56.759 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 30, Target score: 53. Number of topics: 10
2025-05-19 01:16:56.760 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 10 topics, actual score: 30 (target score: 53)
2025-05-19 01:16:56.767 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 32. Title: 集合考点  专项练习
2025-05-19 01:16:56.778 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 10 topics.
2025-05-19 01:16:58.072 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:16:58.074 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:16:58.074 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:16:58.383 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:16:58.385 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:16:58.385 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:00.503 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:00.507 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:00.508 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:00.925 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:00.926 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:00.927 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:02.913 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:02.915 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:02.916 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:03.242 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:03.244 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:03.244 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:03.759 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:03.760 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:03.761 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:04.080 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:04.082 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:04.082 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:04.567 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:04.568 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:04.568 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:04.876 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:04.877 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:04.877 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:05.004 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:05.005 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:05.005 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:05.336 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:05.337 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:05.337 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:07.910 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:07.912 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:07.913 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:08.248 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:08.249 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:08.249 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:13.499 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:17:13.514 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 01:17:14.882 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:14.885 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:14.886 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:14.892 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:14.894 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:14.895 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:14.899 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:14.900 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:14.901 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.338 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.341 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.342 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.348 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.350 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.351 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.357 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.359 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.359 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.789 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.792 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.792 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.800 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.803 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.803 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.808 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.810 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.811 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.966 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.968 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.969 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.975 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.978 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.978 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:17:19.983 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:17:19.985 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:17:19.985 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:18:23.481 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:18:23.482 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:18:23.497 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:18:23.498 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:18:23.501 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:18:23.501 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:18:24.364 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:18:24.364 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:18:24.378 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:18:24.378 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:18:24.380 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:18:24.380 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:29:30.357 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 01:30:49.986 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 01:36:12.461 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 01:37:41.754 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:37:41.754 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:37:41.808 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 01:37:41.808 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 01:37:44.973 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:37:44.973 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:37:45.000 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:37:45.007 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:37:45.011 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:37:45.013 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:37:45.456 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:37:45.456 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:37:45.460 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:37:45.461 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:37:45.464 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:37:45.465 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:44:48.375 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 01:44:54.065 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:44:54.112 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 01:44:54.120 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 01:44:54.134 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 01:44:56.560 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:44:56.561 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:44:56.588 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:44:56.599 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:44:56.604 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:44:56.607 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 01:44:56.918 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 开始加载SimHei字体文件...
2025-05-19 01:44:56.918 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - SimHei字体路径: file:/Users/<USER>/Application%20Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 01:44:56.928 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 成功加载SimHei字体用于PDF生成
2025-05-19 01:44:56.930 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 10 topics for paper id: 32
2025-05-19 01:44:56.936 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 10 topics in database (from 10 requested IDs) for paper id: 32
2025-05-19 01:44:56.937 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 10 ordered topics for paper id: 32
2025-05-19 11:50:29.395 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 11:51:28.975 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=196, questionCount=null, includeShortAnswer=true)], title=函数考点  专项练习, totalScore=99, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 11:51:29.020 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 551. Earmarked: 0, General Pool (unique): 551. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=10}
2025-05-19 11:51:29.041 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 551 topics. IDs: [173687, 173688, 173689, 173690, 173691, 173692, 173693, 173694, 173695, 173696, 173697, 173698, 173699, 173700, 173701, 173702, 173703, 173704, 173705, 173706, 173707, 173708, 173709, 173710, 173711, 173712, 173713, 173714, 173715, 173716, 173717, 173718, 173719, 173720, 173721, 173722, 173723, 173724, 173725, 173726, 173727, 173728, 173729, 173730, 173731, 173732, 173733, 173734, 173735, 173736, 173737, 173738, 173739, 173740, 173741, 173742, 174370, 174371, 174372, 174373, 174374, 174375, 174376, 174377, 174378, 174379, 174380, 174388, 174390, 174391, 174393, 174395, 174396, 174398, 174399, 174401, 174402, 174403, 174404, 174405, 174406, 174407, 174408, 174409, 174410, 174411, 174412, 174413, 174414, 174415, 174416, 174417, 174419, 174422, 174424, 174430, 174432, 174433, 174434, 174435, 174437, 174438, 174440, 174442, 174445, 174468, 174469, 174470, 174471, 174472, 174473, 174474, 174475, 174476, 174477, 174479, 174481, 174483, 174485, 174486, 174487, 174488, 174489, 174537, 174539, 174540, 174542, 174544, 174545, 174547, 174548, 174559, 174560, 174563, 174564, 174566, 174567, 174569, 174572, 174597, 174599, 174600, 174602, 174604, 174605, 174607, 174608, 174610, 174613, 174614, 174617, 174618, 174621, 174623, 174625, 176175, 176177, 176179, 176180, 176182, 176183, 176185, 176186, 176188, 176190, 176193, 176194, 176196, 176198, 176201, 176202, 176203, 176204, 176205, 176206, 176208, 176209, 176211, 176212, 176213, 176214, 176215, 176216, 176217, 176218, 176219, 176221, 176222, 176224, 176225, 176227, 176228, 176230, 176238, 176240, 176280, 176285, 176293, 176713, 176714, 176715, 176716, 176717, 176718, 176719, 176720, 176721, 176722, 176723, 176724, 176725, 176726, 176727, 176728, 176729, 176730, 176731, 176732, 176733, 176734, 176735, 176736, 176737, 176738, 176739, 176740, 176741, 176742, 176743, 176744, 176745, 176746, 176747, 176748, 176749, 176750, 176751, 176752, 176753, 176754, 176755, 176756, 176757, 176758, 176759, 176760, 176761, 176762, 176763, 176764, 176765, 176766, 176767, 176768, 176769, 176770, 176771, 176772, 176773, 176774, 176775, 176776, 176777, 176778, 176779, 176780, 176781, 176782, 176783, 176784, 176785, 176786, 176787, 176788, 176789, 176790, 176791, 176792, 176793, 176794, 176795, 176796, 176797, 176798, 176799, 176800, 176801, 176802, 176803, 176804, 176805, 176806, 176807, 176808, 176809, 176810, 176811, 176812, 176813, 176814, 176815, 176816, 176817, 176818, 176819, 176820, 176821, 176822, 176823, 176824, 176825, 176826, 176827, 176828, 176829, 176830, 176831, 176832, 176833, 176834, 176835, 176836, 176837, 176838, 176839, 176840, 176841, 176842, 176843, 176844, 177299, 177301, 177303, 177304, 177306, 177308, 177310, 177312, 177319, 177320, 177321, 177322, 177323, 177324, 177325, 177326, 177327, 177328, 177329, 177330, 177331, 177332, 177333, 177334, 177335, 177336, 177337, 177338, 177339, 177340, 177341, 177342, 177343, 177344, 177345, 177346, 177348, 177384, 177387, 177391, 177394, 177396, 177399, 177402, 177404, 177450, 177454, 177456, 177458, 177461, 177463, 177466, 177471, 177472, 177479, 177483, 177487, 177489, 177493, 177497, 177502, 177505, 177507, 177538, 177539, 177540, 177541, 177543, 177544, 177545, 177546, 177547, 177548, 177549, 177563, 177565, 177566, 177568, 177569, 177571, 177572, 177574, 177575, 177577, 177622, 177624, 177627, 177629, 177632, 177634, 177636, 177638, 177641, 177643, 177649, 177659, 177678, 177679, 177681, 177813, 177847, 177849, 177852, 177854, 177857, 177859, 177862, 177865, 177934, 177937, 177940, 177945, 177948, 177952, 177954, 177957, 177961, 177965, 177968, 177970, 177973, 177977, 177979, 177982, 177985, 177988, 177990, 177992, 177994, 177995, 177998, 178001, 178003, 178005, 178008, 178011, 178014, 178016, 178194, 178195, 178196, 178197, 178198, 178199, 178200, 178201, 178202, 178204, 178236, 178237, 178239, 178243, 178244, 178245, 178246, 178248, 178249, 178250, 178251, 178252, 178255, 178256, 178257, 178258, 178259, 178260, 178261, 178262, 178263, 178264, 178269, 178270, 178271, 178272, 178273, 178274, 178275, 178276, 178278, 178279, 178280, 178281, 178282, 178283, 178284, 178285, 178286, 178288, 178289, 178290, 178291, 178292, 178293, 178294, 178295, 178296, 178297, 178298, 178299, 178301, 178303, 178304, 178305, 178306, 178307, 178308, 178309, 178310, 178311, 178312, 178314, 178315, 178316, 178317, 178318, 178319, 178320, 178322, 178323, 181219, 181220]
2025-05-19 11:51:29.041 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 11:51:29.041 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 551 topics available for GA (input size was 551). MinReuseIntervalDays: null
2025-05-19 11:51:29.042 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 11:51:29.042 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 551 topics remain.
2025-05-19 11:51:29.042 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 551 candidate topics...
2025-05-19 11:51:29.042 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 11:51:29.042 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 11:51:29.067 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.75948
2025-05-19 11:51:29.091 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.773
2025-05-19 11:51:29.113 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7781751152073733
2025-05-19 11:51:29.135 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Current Best Fitness = {:.4f}, Overall Best Fitness = {:.4f}, Solution Score = 0.7806792452830189
2025-05-19 11:51:29.149 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 37. Overall Best Fitness: {:.4f}. Generations without improvement: 0.7806792452830189.
2025-05-19 11:51:29.149 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 107ms. Best solution fitness: {:.4f}, Selected 0.7806792452830189 topics with total score: 212.
2025-05-19 11:51:29.149 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 212 topics.
2025-05-19 11:51:29.150 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 636, Target score: 99. Number of topics: 212
2025-05-19 11:51:29.150 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (636) is greater than target (99). Attempting to find a subset that sums to target score.
2025-05-19 11:51:29.151 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Successfully found a subset of 33 topics matching target score 99. Original list size: 212.
2025-05-19 11:51:29.151 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - After DP adjustment: 33 topics, attempting to match target score 99.
2025-05-19 11:51:29.151 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Actual score after DP: 99
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=17, judgment=16}, 目标={SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 10 topics needed
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 10 topics needed
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 10 topics needed
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=33
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 33 topics.
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 60, Target score: 99. Number of topics: 20
