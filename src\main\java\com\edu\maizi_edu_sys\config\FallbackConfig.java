package com.edu.maizi_edu_sys.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * 这个配置类提供了应急的Redis配置，当主要的Redis配置失败时会生效
 */
@Configuration
@Slf4j
public class FallbackConfig {

    /**
     * 应急的RedisConnectionFactory，用于当主要Redis服务不可用时
     * 注意：这个工厂不会真正连接到Redis，所以依赖它的组件应该有对应的异常处理机制
     */
    @Bean
    @ConditionalOnMissingBean(RedisConnectionFactory.class)
    public RedisConnectionFactory fallbackRedisConnectionFactory() {
        log.warn("Using fallback Redis Connection Factory. Redis operations will not work correctly!");
        JedisConnectionFactory factory = new JedisConnectionFactory();
        factory.afterPropertiesSet();
        return factory;
    }

    /**
     * 应急的RedisTemplate，仅在主要的RedisTemplate无法创建时使用
     */
    @Bean(name = "redisTemplate")
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, byte[]> fallbackRedisTemplate(RedisConnectionFactory connectionFactory) {
        log.warn("Using fallback RedisTemplate. Redis operations will likely fail!");
        RedisTemplate<String, byte[]> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new JdkSerializationRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }
} 