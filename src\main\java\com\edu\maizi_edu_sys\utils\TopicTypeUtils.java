package com.edu.maizi_edu_sys.utils;

import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 题型标准化工具类
 * 用于统一处理前后端的题型格式
 */
@Slf4j
public class TopicTypeUtils {

    /**
     * 标准化题型，将各种格式的题型转换为标准格式
     * 标准格式使用驼峰命名: singleChoice, multipleChoice, judgment, fillBlank, shortAnswer, subjective
     * 
     * @param topicType 原始题型
     * @return 标准化后的题型
     */
    public static String normalize(String topicType) {
        if (StringUtils.isBlank(topicType)) {
            return "unknown";
        }
        
        // 转换为小写并移除所有空格
        String type = topicType.toLowerCase().trim().replaceAll("\\s+", "");
        
        // 处理常见的题型名称变体
        switch (type) {
            // 单选题
            case "单选":
            case "单选题":
            case "choice":
            case "singlechoice":
            case "single":
            case "single_choice":
            case "singlechoicequestion":
                return "singleChoice";
                
            // 多选题
            case "多选":
            case "多选题":
            case "multiplechoice":
            case "multiple":
            case "multiple_choice":
            case "multiplechoicequestion":
                return "multipleChoice";
                
            // 判断题
            case "判断":
            case "判断题":
            case "judge":
            case "judgment":
            case "tf":
            case "truefalse":
            case "true_false":
            case "judgement":
            case "judgment_question":
                return "judgment";
                
            // 填空题
            case "填空":
            case "填空题":
            case "fill":
            case "fillblank":
            case "fill_blank":
            case "fillinblanks":
            case "fill_in_blanks":
            case "fillblanks":
                return "fillBlank";
                
            // 简答题
            case "简答":
            case "简答题":
            case "shortanswer":
            case "short_answer":
            case "shortanswerquestion":
            case "short":
                return "shortAnswer";
                
            // 主观题
            case "主观":
            case "主观题":
            case "subjective":
            case "essay":
            case "论述题":
            case "论述":
                return "subjective";
                
            // 组合题
            case "组合题":
            case "复合题":
            case "complex":
            case "组合":
            case "复合":
            case "group":
            case "groupquestion":
            case "group_question":
                return "groupQuestion";
                
            // 默认保持原样
            default:
                if (type.contains("single") && type.contains("choice")) {
                    return "singleChoice";
                } else if (type.contains("multiple") && type.contains("choice")) {
                    return "multipleChoice";
                } else if (type.contains("judge") || type.contains("judgment")) {
                    return "judgment";
                } else if (type.contains("fill") && type.contains("blank")) {
                    return "fillBlank";
                } else if (type.contains("short") && type.contains("answer")) {
                    return "shortAnswer";
                } else if (type.contains("subjective") || type.contains("essay")) {
                    return "subjective";
                } else if (type.contains("group") || type.contains("complex")) {
                    return "groupQuestion";
                }
                
                log.warn("未能识别的题型: {}, 保持原样", topicType);
                return type;
        }
    }
    
    /**
     * 将标准化的题型转换为显示名称
     * 
     * @param normalizedType 标准化的题型
     * @return 显示名称
     */
    public static String getDisplayName(String normalizedType) {
        if (StringUtils.isBlank(normalizedType)) {
            return "未知题型";
        }
        
        switch (normalizedType) {
            case "singleChoice": return "单选题";
            case "multipleChoice": return "多选题";
            case "judgment": return "判断题";
            case "fillBlank": return "填空题";
            case "shortAnswer": return "简答题";
            case "subjective": return "主观题";
            case "groupQuestion": return "组合题";
            default: return normalizedType;
        }
    }
    
    /**
     * 将前端传来的题型转换为数据库存储的题型格式
     * 
     * @param frontendType 前端的题型格式（可能是大写下划线格式）
     * @return 数据库存储的题型格式（驼峰格式）
     */
    public static String fromFrontendType(String frontendType) {
        if (StringUtils.isBlank(frontendType)) {
            return "unknown";
        }
        
        // 处理前端传来的大写下划线格式
        switch (frontendType.toUpperCase()) {
            case "SINGLE_CHOICE": return "singleChoice";
            case "MULTIPLE_CHOICE": return "multipleChoice";
            case "JUDGMENT": return "judgment";
            case "FILL_IN_BLANKS": 
            case "FILL_BLANK": return "fillBlank";
            case "SHORT_ANSWER": return "shortAnswer";
            case "SUBJECTIVE": return "subjective";
            case "GROUP_QUESTION": return "groupQuestion";
            default: return normalize(frontendType);
        }
    }
    
    /**
     * 将数据库存储的题型格式转换为前端展示的题型格式
     * 
     * @param dbType 数据库存储的题型格式（驼峰格式）
     * @return 前端使用的题型格式（大写下划线格式）
     */
    public static String toFrontendType(String dbType) {
        String normalized = normalize(dbType);
        
        switch (normalized) {
            case "singleChoice": return "SINGLE_CHOICE";
            case "multipleChoice": return "MULTIPLE_CHOICE";
            case "judgment": return "JUDGMENT";
            case "fillBlank": return "FILL_IN_BLANKS";
            case "shortAnswer": return "SHORT_ANSWER";
            case "subjective": return "SUBJECTIVE";
            case "groupQuestion": return "GROUP_QUESTION";
            default: return dbType.toUpperCase();
        }
    }
} 