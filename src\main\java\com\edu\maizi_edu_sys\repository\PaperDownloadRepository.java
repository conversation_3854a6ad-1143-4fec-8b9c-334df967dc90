package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.PaperDownload;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 试卷下载记录数据访问层
 */
@Repository
public interface PaperDownloadRepository extends JpaRepository<PaperDownload, Long> {
    
    /**
     * 按试卷ID查询下载记录
     * @param paperId 试卷ID
     * @return 下载记录列表
     */
    List<PaperDownload> findByPaperIdOrderByDownloadTimeDesc(Long paperId);
    
    /**
     * 按用户ID查询下载记录
     * @param userId 用户ID
     * @return 下载记录列表
     */
    List<PaperDownload> findByUserIdOrderByDownloadTimeDesc(Long userId);
    
    /**
     * 按试卷ID和用户ID查询下载记录
     * @param paperId 试卷ID
     * @param userId 用户ID
     * @return 下载记录列表
     */
    List<PaperDownload> findByPaperIdAndUserIdOrderByDownloadTimeDesc(Long paperId, Long userId);
} 