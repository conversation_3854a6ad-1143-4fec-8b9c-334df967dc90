package com.edu.maizi_edu_sys.config;

import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.repository.PaperRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据初始化组件
 * 检查试卷表是否有数据，如果没有则添加示例数据
 */
@Component
@Slf4j
public class DataInitializer implements ApplicationRunner {

    private final PaperRepository paperRepository;
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public DataInitializer(PaperRepository paperRepository, JdbcTemplate jdbcTemplate) {
        this.paperRepository = paperRepository;
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 首先检查数据库中是否存在paper表
        checkDatabaseStructure();
        
        // 检查数据库中的试卷数量
        Long paperCount = paperRepository.selectCount(null);
        log.info("当前数据库中试卷数量: {}", paperCount);

        // 如果没有试卷，添加一些示例数据
        if (paperCount == 0) {
            log.info("数据库中没有试卷数据，添加示例数据...");
            
            // 创建3个示例试卷
            for (int i = 1; i <= 3; i++) {
                Paper paper = new Paper();
                paper.setTitle("示例试卷 " + i);
                paper.setType(i % 3); // 试卷类型：0/1/2
                paper.setTotalScore(100);
                paper.setDifficulty(3.5); // 中等难度
                paper.setContent("1,2,3,4,5"); // 示例题目ID列表
                paper.setCreateTime(LocalDateTime.now());
                paper.setUpdateTime(LocalDateTime.now());
                paper.setIsDeleted(false);
                
                paperRepository.insert(paper);
                log.info("添加示例试卷: {}", paper.getTitle());
            }
            
            // 再次检查数量
            paperCount = paperRepository.selectCount(null);
            log.info("添加示例数据后，试卷数量: {}", paperCount);
        }
    }
    
    /**
     * 检查数据库结构，确保paper表存在且结构正确
     */
    private void checkDatabaseStructure() {
        try {
            // 检查papers表是否存在
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'papers'"
            );
            
            if (tables.isEmpty()) {
                log.error("数据库中不存在paper表！尝试创建表...");
                // 自动创建表（简单示例，实际应用中可能需要更复杂的DDL）
                jdbcTemplate.execute(
                    "CREATE TABLE paper (" +
                    "id BIGINT PRIMARY KEY AUTO_INCREMENT, " +
                    "title VARCHAR(255) NOT NULL, " +
                    "type INT, " +
                    "knowledge_id INT, " +
                    "knowledge_name VARCHAR(255), " +
                    "total_score INT NOT NULL, " +
                    "actual_total_score INT, " +
                    "difficulty DOUBLE, " +
                    "create_time DATETIME NOT NULL, " +
                    "update_time DATETIME, " +
                    "is_deleted BOOLEAN NOT NULL DEFAULT FALSE, " +
                    "content TEXT, " +
                    "config TEXT" +
                    ")"
                );
                log.info("papers表创建成功");
            } else {
                log.info("数据库中已存在paper表");
                
                // 检查表结构
                List<Map<String, Object>> columns = jdbcTemplate.queryForList(
                    "SELECT column_name, data_type FROM information_schema.columns " + 
                    "WHERE table_schema = DATABASE() AND table_name = 'papers'"
                );
                
                log.info("paper表结构:");
                for (Map<String, Object> column : columns) {
                    log.info("  - {}: {}", column.get("column_name"), column.get("data_type"));
                }
                
                // 验证关键列是否存在
                boolean hasIsDeleted = columns.stream()
                    .anyMatch(col -> "is_deleted".equalsIgnoreCase(String.valueOf(col.get("column_name"))));
                    
                if (!hasIsDeleted) {
                    log.error("paper表缺少is_deleted列，这可能导致查询问题");
                }
            }
            
            // 检查现有数据
            List<Map<String, Object>> paperData = jdbcTemplate.queryForList("SELECT * FROM papers LIMIT 5");
            log.info("paper表示例数据: {}", paperData);
            
        } catch (Exception e) {
            log.error("检查数据库结构时出错: {}", e.getMessage(), e);
        }
    }
} 