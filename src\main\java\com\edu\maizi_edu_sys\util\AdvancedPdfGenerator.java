package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

/**
 * 先进的PDF生成器
 * 使用Flying Saucer + iText支持HTML转PDF
 * 支持数学公式渲染和现代HTML/CSS
 */
@Slf4j
public class AdvancedPdfGenerator {

    /**
     * 将HTML转换为PDF
     * @param html HTML内容
     * @return PDF资源
     */
    public static Resource convertHtmlToPdf(String html) {
        log.info("开始使用先进PDF生成器转换HTML到PDF");

        try {
            // 1. 处理数学公式
            String processedHtml = processMathFormulas(html);
            log.debug("数学公式处理完成");

            // 2. 创建完整的XHTML文档
            String completeHtml = createCompleteHtmlDocument(processedHtml);
            log.debug("完整XHTML文档创建完成，长度: {}", completeHtml.length());

            // 3. 使用Flying Saucer转换为PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(completeHtml);
            renderer.layout();
            renderer.createPDF(outputStream);

            byte[] pdfBytes = outputStream.toByteArray();
            log.info("PDF生成成功，大小: {} bytes", pdfBytes.length);

            return new ByteArrayResource(pdfBytes);

        } catch (Exception e) {
            log.error("PDF生成失败: {}", e.getMessage(), e);
            return createErrorPdf("PDF生成失败: " + e.getMessage());
        }
    }

    /**
     * 清理和优化HTML
     */
    private static String cleanAndOptimizeHtml(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "<div>内容为空</div>";
        }

        try {
            // 简单的HTML清理，移除可能导致问题的元素
            String cleanHtml = html
                .replaceAll("<script[^>]*>.*?</script>", "") // 移除script标签
                .replaceAll("<style[^>]*type=[\"']text/javascript[\"'][^>]*>.*?</style>", "") // 移除JS样式
                .replaceAll("<img([^>]*?)(?<!alt=[\"'][^\"']*[\"'])([^>]*?)>", "<img$1 alt=\"图片\"$2>"); // 添加alt属性

            return cleanHtml;

        } catch (Exception e) {
            log.warn("HTML清理失败，使用原始HTML: {}", e.getMessage());
            return html;
        }
    }

    /**
     * 处理数学公式
     * 将LaTeX公式转换为可渲染的格式
     */
    private static String processMathFormulas(String html) {
        try {
            if (html == null || html.trim().isEmpty()) {
                return html;
            }

            String processedHtml = html;

            // 处理块级数学公式 $$...$$ (必须先处理，避免与行内公式冲突)
            processedHtml = processedHtml.replaceAll("\\$\\$([^$]+?)\\$\\$",
                "<div class=\"math-block\">$1</div>");

            // 处理行内数学公式 $...$
            processedHtml = processedHtml.replaceAll("\\$([^$]+?)\\$",
                "<span class=\"math-inline\">$1</span>");

            return processedHtml;

        } catch (Exception e) {
            log.warn("数学公式处理失败: {}", e.getMessage());
            return html;
        }
    }

    /**
     * 创建完整的XHTML文档（Flying Saucer兼容）
     */
    private static String createCompleteHtmlDocument(String content) {
        StringBuilder html = new StringBuilder();

        // 使用Flying Saucer兼容的XHTML格式
        html.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        html.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n");
        html.append("<html xmlns=\"http://www.w3.org/1999/xhtml\">\n");
        html.append("<head>\n");
        html.append("    <title>试卷</title>\n");
        html.append("    <style type=\"text/css\">\n");
        html.append(getAdvancedCss());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        // 确保内容符合XHTML标准
        String cleanContent = cleanHtmlForXhtml(content);
        html.append(cleanContent);
        html.append("\n</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 清理HTML内容以符合HTML5标准
     */
    private static String cleanHtmlForXhtml(String content) {
        if (content == null) {
            return "";
        }

        // 简单的HTML清理，只处理最基本的问题
        String cleaned = content
                // 移除可能导致问题的特殊字符
                .replaceAll("&(?!amp;|lt;|gt;|quot;|#\\d+;|#x[0-9a-fA-F]+;)", "&amp;")
                // 确保基本的HTML结构正确
                .replaceAll("<br\\s*/?>", "<br/>")
                .replaceAll("<hr\\s*/?>", "<hr/>");

        return cleaned;
    }

    /**
     * 获取先进的CSS样式
     */
    private static String getAdvancedCss() {
        return "@page { size: A4; margin: 2cm; }\n" +
                "body { font-family: 'SimSun', 'Microsoft YaHei', serif; font-size: 14px; line-height: 1.6; color: #333; background-color: white; margin: 0; padding: 0; }\n" +
                "h1 { text-align: center; font-size: 24px; margin-bottom: 20px; color: #000; font-weight: bold; border-bottom: 2px solid #000; padding-bottom: 10px; }\n" +
                "h2 { font-size: 18px; margin-top: 30px; margin-bottom: 15px; color: #000; font-weight: bold; border-left: 4px solid #007cba; padding-left: 10px; }\n" +
                ".question { margin-bottom: 25px; page-break-inside: avoid; padding: 15px; border: 1px solid #e0e0e0; background-color: #fafafa; }\n" +
                ".question-title { font-weight: bold; margin-bottom: 12px; line-height: 1.8; color: #000; font-size: 15px; }\n" +
                ".options { margin-left: 25px; margin-bottom: 15px; margin-top: 10px; }\n" +
                ".option-item { margin-bottom: 8px; line-height: 1.6; padding: 3px 0; color: #333; }\n" +
                ".answer-line { margin-top: 15px; margin-bottom: 15px; font-weight: bold; border-bottom: 1px solid #ccc; padding-bottom: 5px; }\n" +
                ".answer-area { margin-top: 15px; margin-bottom: 15px; font-weight: bold; color: #000; }\n" +
                ".group-question { margin-top: 10px; margin-bottom: 10px; }\n" +
                ".group-note { font-style: italic; color: #666; margin-bottom: 10px; }\n" +
                ".subjective-area { margin-top: 15px; margin-bottom: 15px; font-weight: bold; color: #000; }\n" +
                ".math-inline { font-family: 'Times New Roman', serif; font-style: italic; color: #000; }\n" +
                ".math-block { font-family: 'Times New Roman', serif; font-style: italic; text-align: center; margin: 15px 0; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; }\n" +
                "@media print { body { font-size: 12px; background-color: white !important; } .question { border: none !important; background-color: white !important; } }";
    }

    /**
     * 创建错误PDF
     */
    private static Resource createErrorPdf(String errorMessage) {
        try {
            String errorHtml = createCompleteHtmlDocument(
                "<div style=\"text-align: center; color: red; font-size: 18px; margin-top: 50px;\">" +
                "<h2>PDF生成失败</h2>" +
                "<p>" + errorMessage + "</p>" +
                "</div>"
            );

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(errorHtml);
            renderer.layout();
            renderer.createPDF(outputStream);

            return new ByteArrayResource(outputStream.toByteArray());

        } catch (Exception e) {
            log.error("创建错误PDF失败: {}", e.getMessage());
            // 返回最基本的错误信息
            String basicError = "PDF生成失败: " + errorMessage;
            return new ByteArrayResource(basicError.getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 调试方法：处理HTML并返回完整的XHTML文档
     */
    public static String debugProcessHtml(String html) {
        try {
            // 1. 处理数学公式
            String processedHtml = processMathFormulas(html);
            log.debug("数学公式处理完成");

            // 2. 创建完整的HTML文档
            String completeHtml = createCompleteHtmlDocument(processedHtml);
            log.debug("完整HTML文档创建完成，长度: {}", completeHtml.length());

            return completeHtml;

        } catch (Exception e) {
            log.error("HTML处理失败: {}", e.getMessage(), e);
            return "<html><body><h1>HTML处理失败: " + e.getMessage() + "</h1></body></html>";
        }
    }
}
