package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComprehensivePaperOverviewDTO {
    // Basic Paper Details (can be mapped from Paper entity or PaperDto)
    private Long id;
    private String title;
    private Integer totalScore; // Actual total score of the generated paper
    private Integer questionCount; // Actual total number of questions
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Integer creatorId;
    private String creatorName; // May need to fetch this
    // Add other relevant paper metadata fields from your Paper entity/DTO

    // Original Generation Configuration (Parsed from Paper.config)
    private List<KpConfigInOverviewDTO> originalKpConfigs;

    // Detailed list of topics in the paper
    private List<TopicInOverviewDTO> topicsInPaper;

    // Summary Statistics (Calculated in the service)
    private Map<String, Integer> actualTopicTypeCounts; // e.g., {"singleChoice": 10, "multipleChoice": 5}
    private Map<String, Double> actualDifficultyDistribution; // e.g., {"easy": 0.3, "medium": 0.5}
    // Potentially other stats like actual cognitive level distribution if tracked
} 