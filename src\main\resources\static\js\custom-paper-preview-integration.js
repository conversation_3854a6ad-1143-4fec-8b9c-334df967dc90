/**
 * 自由组卷实时预览集成脚本
 * 将实时预览功能集成到自由组卷模态框中
 */

class CustomPaperPreviewIntegration {
    constructor() {
        this.previewInstance = null;
        this.isInitialized = false;
        this.modalElement = null;
    }

    /**
     * 初始化集成功能
     */
    init() {
        if (this.isInitialized) return;

        // 适配组卷界面的模态框
        this.modalElement = document.getElementById('paperGenerationModal');
        if (!this.modalElement) {
            console.warn('试卷生成模态框未找到，跳过实时预览集成');
            return;
        }

        // 监听模态框显示事件
        $(this.modalElement).on('shown.bs.modal', () => {
            console.log('试卷生成模态框已显示，初始化预览');
            setTimeout(() => {
                this.initializePreview();
            }, 200); // 延迟确保DOM完全渲染
        });

        // 监听模态框隐藏事件
        $(this.modalElement).on('hidden.bs.modal', () => {
            this.cleanupPreview();
        });

        this.bindFormEvents();
        this.isInitialized = true;

        // 组卷界面实时预览集成已初始化
    }

    /**
     * 初始化预览功能
     */
    initializePreview() {
        if (this.previewInstance) {
            // 如果已经初始化，直接更新预览
            setTimeout(() => {
                this.previewInstance.updatePreview();
            }, 100);
            return;
        }

        // 检查预览容器是否存在
        const previewContainer = document.getElementById('realTimePreviewContainer');
        if (!previewContainer) {
            console.warn('实时预览容器未找到');
            return;
        }

        // 创建预览实例
        this.previewInstance = new PaperRealTimePreview();

        // 重写获取知识点配置的方法，适配组卷界面
        this.previewInstance.getKnowledgePointConfigs = function() {
            const configs = [];
            const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs') || [];

            knowledgeConfigs.forEach(config => {
                // 确保数据类型正确，符合后端 KnowledgePointConfigRequest 的要求
                const knowledgeId = parseInt(config.knowledgeId);
                const questionCount = parseInt(config.questionCount) || 0;
                const includeShortAnswer = Boolean(config.includeShortAnswer);

                // 验证必需字段
                if (!isNaN(knowledgeId) && knowledgeId > 0) {
                    configs.push({
                        knowledgeId: knowledgeId,        // Long - 必需，不能为null
                        questionCount: questionCount,    // Integer - 必需，≥0
                        includeShortAnswer: includeShortAnswer  // Boolean - 必需，不能为null
                    });
                } else {
                    console.warn('跳过无效的知识点配置:', config);
                }
            });

            console.log('获取知识点配置:', configs);
            return configs;
        };

        // 重写获取题型数量配置的方法
        this.previewInstance.getTypeCountMap = function() {
            const typeCountMap = {};

            const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
            const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
            const judgment = parseInt($('#judgmentCount').val()) || 0;
            const fillBlank = parseInt($('#fillCount').val()) || 0;
            const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

            if (singleChoice > 0) typeCountMap['SINGLE_CHOICE'] = singleChoice;
            if (multipleChoice > 0) typeCountMap['MULTIPLE_CHOICE'] = multipleChoice;
            if (judgment > 0) typeCountMap['JUDGE'] = judgment;
            if (fillBlank > 0) typeCountMap['FILL'] = fillBlank;
            if (shortAnswer > 0) typeCountMap['SHORT'] = shortAnswer;

            console.log('获取题型数量配置:', typeCountMap);
            return typeCountMap;
        };

        // 重写获取题型分值配置的方法
        this.previewInstance.getTypeScoreMap = function() {
            const typeScoreMap = {};

            // 确保分值为整数，符合后端 Map<String, Integer> 的要求
            const singleChoiceScore = parseInt($('#singleChoiceScore').val()) || 0;
            const multipleChoiceScore = parseInt($('#multipleChoiceScore').val()) || 0;
            const judgmentScore = parseInt($('#judgmentScore').val()) || 0;
            const fillBlankScore = parseInt($('#fillScore').val()) || 0;
            const shortAnswerScore = parseInt($('#shortAnswerScore').val()) || 0;

            if (singleChoiceScore > 0) typeScoreMap['SINGLE_CHOICE'] = singleChoiceScore;
            if (multipleChoiceScore > 0) typeScoreMap['MULTIPLE_CHOICE'] = multipleChoiceScore;
            if (judgmentScore > 0) typeScoreMap['JUDGE'] = judgmentScore;
            if (fillBlankScore > 0) typeScoreMap['FILL'] = fillBlankScore;
            if (shortAnswerScore > 0) typeScoreMap['SHORT'] = shortAnswerScore;

            console.log('获取题型分值配置:', typeScoreMap);
            return typeScoreMap;
        };

        // 初始化预览容器
        this.previewInstance.init('realTimePreviewContainer', {
            debounceDelay: 800,
            previewLimit: 2, // 每种题型预览2道题
            autoUpdate: true,
            showStats: true,
            showWarnings: true
        });

        // 重写获取配置的方法以适配自由组卷表单
        this.overridePreviewMethods();

        // 触发初始预览
        setTimeout(() => {
            this.previewInstance.triggerPreviewUpdate();
        }, 500);
    }

    /**
     * 重写预览实例的方法以适配自由组卷表单
     */
    overridePreviewMethods() {
        if (!this.previewInstance) return;

        // 重写获取知识点配置的方法
        this.previewInstance.getKnowledgePointConfigs = () => {
            return this.getKnowledgePointConfigs();
        };

        // 重写获取题型数量配置的方法
        this.previewInstance.getTypeCountMap = () => {
            return this.getTypeCountMap();
        };

        // 重写获取题型分值配置的方法
        this.previewInstance.getTypeScoreMap = () => {
            return this.getTypeScoreMap();
        };

        // 重写获取试卷标题的方法
        this.previewInstance.getPaperTitle = () => {
            return this.getPaperTitle();
        };
    }

    /**
     * 绑定表单事件
     */
    bindFormEvents() {
        // 监听组卷界面的题型数量和分值变化
        $(document).on('change input',
            '#singleChoiceCount, #singleChoiceScore, ' +
            '#multipleChoiceCount, #multipleChoiceScore, ' +
            '#judgmentCount, #judgmentScore, ' +
            '#fillCount, #fillScore, ' +
            '#shortAnswerCount, #shortAnswerScore',
            () => {
                console.log('检测到题型配置变化');
                this.triggerPreviewUpdate();
            }
        );

        // 监听试卷标题变化
        $(document).on('change input', '#paperTitle', () => {
            console.log('检测到试卷标题变化');
            this.triggerPreviewUpdate();
        });

        // 监听知识点配置变化
        $(document).on('change input', '.knowledge-question-count', () => {
            console.log('检测到知识点题量变化');
            this.triggerPreviewUpdate();
        });

        $(document).on('change', '.include-short-answer', () => {
            console.log('检测到简答题设置变化');
            this.triggerPreviewUpdate();
        });

        // 监听知识点配置容器变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    console.log('检测到知识点配置容器变化');
                    setTimeout(() => {
                        this.triggerPreviewUpdate();
                    }, 500);
                }
            });
        });

        // 观察知识点配置容器
        const knowledgeContainer = document.getElementById('knowledgePointsConfigContainer');
        if (knowledgeContainer) {
            observer.observe(knowledgeContainer, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * 触发预览更新
     */
    triggerPreviewUpdate() {
        if (this.previewInstance && this.previewInstance.triggerPreviewUpdate) {
            this.previewInstance.triggerPreviewUpdate();
        }
    }

    /**
     * 获取知识点配置
     */
    getKnowledgePointConfigs() {
        const configs = [];
        const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs') || [];

        console.log('🔍 [预览] 原始知识点配置数据:', knowledgeConfigs);
        console.log('🔍 [预览] 知识点配置数组长度:', knowledgeConfigs.length);

        if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
            console.warn('⚠️ [预览] 未找到知识点配置数据');
            return configs;
        }

        knowledgeConfigs.forEach((config, index) => {
            console.log(`🔍 [预览] 处理知识点配置 ${index + 1}:`, config);

            // 确保数据类型正确，符合后端 KnowledgePointConfigRequest 的要求
            const knowledgeId = parseInt(config.knowledgeId);
            const questionCount = parseInt(config.questionCount) || 0;
            const includeShortAnswer = Boolean(config.includeShortAnswer);

            console.log(`🔍 [预览] 解析结果: knowledgeId=${knowledgeId}, questionCount=${questionCount}, includeShortAnswer=${includeShortAnswer}`);

            // 验证必需字段和业务逻辑
            if (!isNaN(knowledgeId) && knowledgeId > 0 && questionCount > 0) {
                const configItem = {
                    knowledgeId: knowledgeId,        // Long - 必需，不能为null
                    questionCount: questionCount,    // Integer - 必需，≥0
                    includeShortAnswer: includeShortAnswer  // Boolean - 必需，不能为null
                };
                configs.push(configItem);
                console.log(`✅ [预览] 添加有效配置:`, configItem);
            } else {
                console.warn(`⚠️ [预览] 跳过无效的知识点配置:`, config);
            }
        });

        console.log('📋 [预览] 最终知识点配置:', configs);
        return configs;
    }

    /**
     * 获取题型数量配置
     */
    getTypeCountMap() {
        const typeCountMap = {};

        // 组卷界面的题型映射
        const typeMapping = {
            'singleChoiceCount': 'SINGLE_CHOICE',
            'multipleChoiceCount': 'MULTIPLE_CHOICE',
            'judgmentCount': 'JUDGE',
            'fillCount': 'FILL',
            'shortAnswerCount': 'SHORT'
        };

        console.log('🔍 [预览] 开始获取题型数量配置...');

        Object.entries(typeMapping).forEach(([inputId, typeKey]) => {
            const input = document.getElementById(inputId);
            if (input) {
                const count = parseInt(input.value) || 0;
                console.log(`🔍 [预览] ${inputId} (${typeKey}): ${count}`);
                if (count > 0) {
                    typeCountMap[typeKey] = count;
                }
            } else {
                console.warn(`⚠️ [预览] 未找到输入框: ${inputId}`);
            }
        });

        console.log('📋 [预览] 最终题型数量配置:', typeCountMap);
        return typeCountMap;
    }

    /**
     * 获取题型分值配置
     */
    getTypeScoreMap() {
        const typeScoreMap = {};

        // 组卷界面的题型映射
        const typeMapping = {
            'singleChoiceScore': 'SINGLE_CHOICE',
            'multipleChoiceScore': 'MULTIPLE_CHOICE',
            'judgmentScore': 'JUDGE',
            'fillScore': 'FILL',
            'shortAnswerScore': 'SHORT'
        };

        Object.entries(typeMapping).forEach(([inputId, typeKey]) => {
            const input = document.getElementById(inputId);
            if (input) {
                // 确保分值为整数，符合后端 Map<String, Integer> 的要求
                const score = parseInt(input.value) || 0;
                if (score > 0) {
                    typeScoreMap[typeKey] = score;
                }
            }
        });

        console.log('获取题型分值配置:', typeScoreMap);
        return typeScoreMap;
    }

    /**
     * 获取试卷标题
     */
    getPaperTitle() {
        const titleInput = document.getElementById('paperTitle');
        return titleInput ? titleInput.value.trim() : '试卷预览';
    }

    /**
     * 清理预览功能
     */
    cleanupPreview() {
        // 预览实例保持不变，只是停止自动更新
        if (this.previewInstance) {
            this.previewInstance.setOptions({ autoUpdate: false });
        }
    }

    /**
     * 获取当前预览数据
     */
    getCurrentPreviewData() {
        return this.previewInstance ? this.previewInstance.getCurrentPreviewData() : null;
    }

    /**
     * 手动刷新预览
     */
    refreshPreview() {
        if (this.previewInstance) {
            this.previewInstance.updatePreview();
        }
    }

    /**
     * 设置预览选项
     */
    setPreviewOptions(options) {
        if (this.previewInstance) {
            this.previewInstance.setOptions(options);
        }
    }

    /**
     * 获取预览实例
     */
    getPreviewInstance() {
        return this.previewInstance;
    }
}

// 创建全局实例
window.customPaperPreviewIntegration = new CustomPaperPreviewIntegration();

// 页面加载完成后初始化
$(document).ready(function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(() => {
        if (window.PaperRealTimePreview) {
            window.customPaperPreviewIntegration.init();
            // 实时预览集成已初始化

            // 暴露全局预览实例
            window.realTimePreview = window.customPaperPreviewIntegration.getPreviewInstance();
            if (window.realTimePreview) {
                console.log('✅ 全局预览实例已暴露');
            }
        } else {
            console.warn('PaperRealTimePreview 类未找到，无法初始化实时预览集成');
        }
    }, 1000);
});

// 监听模态框显示事件，确保预览实例在模态框打开时可用
$(document).on('shown.bs.modal', '#paperGenerationModal', function() {
    console.log('试卷生成模态框已显示，检查预览实例...');

    // 如果预览实例还没有创建，立即创建
    if (!window.realTimePreview && window.customPaperPreviewIntegration) {
        const previewInstance = window.customPaperPreviewIntegration.getPreviewInstance();
        if (previewInstance) {
            window.realTimePreview = previewInstance;
            console.log('✅ 全局预览实例已在模态框显示时创建');
        } else {
            console.log('⚠️ 预览实例尚未创建，尝试重新初始化...');
            // 尝试重新初始化
            setTimeout(() => {
                if (window.PaperRealTimePreview && !window.customPaperPreviewIntegration.getPreviewInstance()) {
                    window.customPaperPreviewIntegration.initializePreview();
                    window.realTimePreview = window.customPaperPreviewIntegration.getPreviewInstance();
                    if (window.realTimePreview) {
                        console.log('✅ 预览实例重新初始化成功');
                    }
                }
            }, 500);
        }
    }
});

// 导出供其他脚本使用
window.CustomPaperPreviewIntegration = CustomPaperPreviewIntegration;
