-- 创建试卷配置表
CREATE TABLE IF NOT EXISTS paper_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    description VARCHAR(500) COMMENT '配置描述',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    title_template VARCHAR(200) COMMENT '试卷标题模板',
    paper_type VARCHAR(20) DEFAULT 'standard' COMMENT '试卷类型',
    paper_count INT DEFAULT 1 COMMENT '生成套数',

    -- 题型配置
    single_choice_count INT DEFAULT 0 COMMENT '单选题数量',
    single_choice_score INT DEFAULT 0 COMMENT '单选题分值',
    multiple_choice_count INT DEFAULT 0 COMMENT '多选题数量',
    multiple_choice_score INT DEFAULT 0 COMMENT '多选题分值',
    judgment_count INT DEFAULT 0 COMMENT '判断题数量',
    judgment_score INT DEFAULT 0 COMMENT '判断题分值',
    fill_count INT DEFAULT 0 COMMENT '填空题数量',
    fill_score INT DEFAULT 0 COMMENT '填空题分值',
    short_answer_count INT DEFAULT 0 COMMENT '简答题数量',
    short_answer_score INT DEFAULT 0 COMMENT '简答题分值',

    -- 配置数据（JSON格式）
    difficulty_distribution TEXT COMMENT '难度分布配置（JSON）',
    knowledge_point_configs TEXT COMMENT '知识点配置（JSON）',

    -- 配置属性
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认配置',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否为公共配置',
    usage_count INT DEFAULT 0 COMMENT '使用次数',

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',

    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_config_name (config_name),
    INDEX idx_is_public (is_public),
    INDEX idx_is_default (is_default),
    INDEX idx_usage_count (usage_count),
    INDEX idx_last_used_at (last_used_at),
    INDEX idx_created_at (created_at),

    -- 复合索引
    INDEX idx_user_config_name (user_id, config_name),
    INDEX idx_user_default (user_id, is_default),
    INDEX idx_user_usage (user_id, usage_count DESC),
    INDEX idx_user_recent (user_id, last_used_at DESC),

    -- 约束
    CONSTRAINT uk_user_config_name UNIQUE (user_id, config_name),
    CONSTRAINT chk_paper_count CHECK (paper_count >= 1 AND paper_count <= 10),
    CONSTRAINT chk_question_counts CHECK (
        single_choice_count >= 0 AND
        multiple_choice_count >= 0 AND
        judgment_count >= 0 AND
        fill_count >= 0 AND
        short_answer_count >= 0
    ),
    CONSTRAINT chk_question_scores CHECK (
        single_choice_score >= 0 AND
        multiple_choice_score >= 0 AND
        judgment_score >= 0 AND
        fill_score >= 0 AND
        short_answer_score >= 0
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='试卷配置表';

-- 插入一些示例公共配置
INSERT INTO paper_configs (
    config_name, description, user_id, title_template, paper_type,
    single_choice_count, single_choice_score,
    multiple_choice_count, multiple_choice_score,
    judgment_count, judgment_score,
    fill_count, fill_score,
    short_answer_count, short_answer_score,
    difficulty_distribution, knowledge_point_configs,
    is_public, usage_count
) VALUES
(
    '标准考试配置',
    '适用于大多数考试场景的标准配置，包含各种题型的均衡分布',
    1,
    '{{科目}}期末考试试卷',
    'standard',
    10, 2,  -- 单选题：10题，每题2分
    5, 4,   -- 多选题：5题，每题4分
    10, 1,  -- 判断题：10题，每题1分
    5, 4,   -- 填空题：5题，每题4分
    3, 10,  -- 简答题：3题，每题10分
    '{"easy": 0.3, "medium": 0.5, "hard": 0.2}',
    '[]',
    TRUE,
    0
),
(
    '快速练习配置',
    '适用于日常练习的轻量级配置，题目数量较少',
    1,
    '{{科目}}练习题',
    'standard',
    5, 2,   -- 单选题：5题，每题2分
    3, 3,   -- 多选题：3题，每题3分
    5, 1,   -- 判断题：5题，每题1分
    2, 5,   -- 填空题：2题，每题5分
    1, 10,  -- 简答题：1题，每题10分
    '{"easy": 0.4, "medium": 0.4, "hard": 0.2}',
    '[]',
    TRUE,
    0
),
(
    '基础知识测试',
    '主要测试基础知识掌握情况，以选择题和判断题为主',
    1,
    '{{科目}}基础知识测试',
    'standard',
    15, 2,  -- 单选题：15题，每题2分
    10, 3,  -- 多选题：10题，每题3分
    15, 1,  -- 判断题：15题，每题1分
    5, 3,   -- 填空题：5题，每题3分
    0, 0,   -- 简答题：0题
    '{"easy": 0.5, "medium": 0.3, "hard": 0.2}',
    '[]',
    TRUE,
    0
),
(
    '综合能力考查',
    '注重综合能力考查，简答题和填空题比重较大',
    1,
    '{{科目}}综合能力测试',
    'standard',
    8, 2,   -- 单选题：8题，每题2分
    4, 4,   -- 多选题：4题，每题4分
    6, 1,   -- 判断题：6题，每题1分
    8, 5,   -- 填空题：8题，每题5分
    5, 12,  -- 简答题：5题，每题12分
    '{"easy": 0.2, "medium": 0.5, "hard": 0.3}',
    '[]',
    TRUE,
    0
);

-- 创建触发器：确保每个用户只有一个默认配置
DELIMITER $$

CREATE TRIGGER tr_paper_configs_default_unique
    BEFORE UPDATE ON paper_configs
    FOR EACH ROW
BEGIN
    -- 如果设置为默认配置，则清除该用户的其他默认配置
    IF NEW.is_default = TRUE AND OLD.is_default = FALSE THEN
        UPDATE paper_configs
        SET is_default = FALSE
        WHERE user_id = NEW.user_id AND id != NEW.id AND is_default = TRUE;
    END IF;
END$$

CREATE TRIGGER tr_paper_configs_default_unique_insert
    BEFORE INSERT ON paper_configs
    FOR EACH ROW
BEGIN
    -- 如果插入的是默认配置，则清除该用户的其他默认配置
    IF NEW.is_default = TRUE THEN
        UPDATE paper_configs
        SET is_default = FALSE
        WHERE user_id = NEW.user_id AND is_default = TRUE;
    END IF;
END$$

DELIMITER ;

-- 添加外键约束（如果user表存在）
-- ALTER TABLE paper_configs
-- ADD CONSTRAINT fk_paper_configs_user_id
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
