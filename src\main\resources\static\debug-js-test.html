<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript调试测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1>JavaScript调试测试</h1>
        <div class="alert alert-info">
            <h4>测试结果：</h4>
            <div id="testResults"></div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h3>基础测试</h3>
                <button class="btn btn-primary" onclick="testBasicJS()">测试基础JavaScript</button>
                <button class="btn btn-success" onclick="testjQuery()">测试jQuery</button>
            </div>
            <div class="col-md-6">
                <h3>知识点测试</h3>
                <button class="btn btn-warning" onclick="testKnowledgePoints()">测试知识点功能</button>
                <button class="btn btn-info" onclick="testPaperGeneration()">测试试卷生成</button>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>控制台日志</h3>
            <div id="consoleLog" class="border p-3" style="height: 300px; overflow-y: auto; background: #f8f9fa;"></div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const logContainer = document.getElementById('consoleLog');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logEntry = document.createElement('div');
            logEntry.textContent = args.join(' ');
            logEntry.style.marginBottom = '5px';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        };

        function addResult(test, status, message) {
            const results = document.getElementById('testResults');
            const result = document.createElement('div');
            result.className = `alert alert-${status === 'success' ? 'success' : 'danger'} py-2`;
            result.innerHTML = `<strong>${test}:</strong> ${message}`;
            results.appendChild(result);
        }

        function testBasicJS() {
            console.log('🔍 测试基础JavaScript...');
            try {
                const test = 'Hello World';
                console.log('基础JavaScript正常工作');
                addResult('基础JavaScript', 'success', '正常工作');
            } catch (error) {
                console.error('基础JavaScript错误:', error);
                addResult('基础JavaScript', 'error', error.message);
            }
        }

        function testjQuery() {
            console.log('🔍 测试jQuery...');
            try {
                if (typeof $ !== 'undefined') {
                    console.log('jQuery版本:', $.fn.jquery);
                    $('body').append('<div id="jqueryTest" style="display:none;">jQuery测试</div>');
                    $('#jqueryTest').remove();
                    addResult('jQuery', 'success', `版本 ${$.fn.jquery} 正常工作`);
                } else {
                    throw new Error('jQuery未定义');
                }
            } catch (error) {
                console.error('jQuery错误:', error);
                addResult('jQuery', 'error', error.message);
            }
        }

        function testKnowledgePoints() {
            console.log('🔍 测试知识点功能...');
            try {
                // 模拟知识点选择
                if (typeof Map !== 'undefined') {
                    const selectedKnowledgePoints = new Map();
                    selectedKnowledgePoints.set('1', {
                        name: '测试知识点',
                        topicCount: '10题',
                        isFree: true
                    });
                    console.log('知识点Map创建成功:', selectedKnowledgePoints.size);
                    addResult('知识点功能', 'success', '基础功能正常');
                } else {
                    throw new Error('Map不支持');
                }
            } catch (error) {
                console.error('知识点功能错误:', error);
                addResult('知识点功能', 'error', error.message);
            }
        }

        function testPaperGeneration() {
            console.log('🔍 测试试卷生成功能...');
            try {
                // 模拟试卷生成配置
                const config = {
                    singleChoice: 10,
                    multipleChoice: 5,
                    judgment: 5,
                    fillBlank: 5,
                    shortAnswer: 2
                };
                
                const totalQuestions = Object.values(config).reduce((sum, count) => sum + count, 0);
                console.log('试卷配置:', config);
                console.log('总题数:', totalQuestions);
                
                if (totalQuestions > 0) {
                    addResult('试卷生成', 'success', `配置正常，总题数: ${totalQuestions}`);
                } else {
                    throw new Error('题型配置无效');
                }
            } catch (error) {
                console.error('试卷生成错误:', error);
                addResult('试卷生成', 'error', error.message);
            }
        }

        // 页面加载完成后自动运行基础测试
        $(document).ready(function() {
            console.log('📋 页面加载完成，开始自动测试...');
            setTimeout(() => {
                testBasicJS();
                testjQuery();
            }, 1000);
        });
    </script>
</body>
</html>
