2025-05-19 19:18:13.966 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-19 19:18:13.967 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-19 19:18:13.967 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-19 19:18:21.039 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-19 19:18:22.890 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-19 19:20:04.464 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:04.565 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:04.893 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Type 'FILL_IN_BLANKS' (requested 3) has NO available questions in the database.
2025-05-19 19:20:04.982 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: CRITICAL ISSUE - Some requested topic types have ZERO available questions in the database. Check the knowledge point's question bank.
2025-05-19 19:20:05.074 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:05.074 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Discrepancy for type 'FILL_IN_BLANKS' (requested key 'FILL_IN_BLANKS'). Requested: 3, Actual: 0. 知识点中可能没有足够此类型的题目.
2025-05-19 19:20:05.080 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:05.081 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 题库中可用题目不足。
请求总题目数：20，实际可用题目数：17。
这可能是因为选择的知识点中没有足够的题目。
具体题型统计：
- 单选题: 请求5/实际5
- 多选题: 请求5/实际5
- 判断题: 请求5/实际5
- FILL_IN_BLANKS: 请求3/实际0 (不足3)
- 简答题: 请求2/实际2
2025-05-19 19:20:05.081 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:05.081 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 请注意：FILL_IN_BLANKS数量不足，请求3题，实际仅有0题。(全局配置)
2025-05-19 19:20:05.207 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:05.207 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:20:17.213 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-19 19:20:17.477 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-19 19:20:17.568 [http-nio-8081-exec-2] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-19 19:29:33.772 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 19:29:33.773 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 19:29:33.773 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 19:29:41.318 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-19 19:29:43.703 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-19 19:30:08.116 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:30:08.222 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:30:08.716 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Type 'FILL_IN_BLANKS' (requested 3) has NO available questions in the database.
2025-05-19 19:30:08.833 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: CRITICAL ISSUE - Some requested topic types have ZERO available questions in the database. Check the knowledge point's question bank.
2025-05-19 19:30:08.910 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:30:08.910 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Discrepancy for type 'FILL_IN_BLANKS' (requested key 'FILL_IN_BLANKS'). Requested: 3, Actual: 0. 知识点中可能没有足够此类型的题目.
2025-05-19 19:30:08.916 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:30:08.916 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 题库中可用题目不足。
请求总题目数：18，实际可用题目数：15。
这可能是因为选择的知识点中没有足够的题目。
具体题型统计：
- 单选题: 请求5/实际5
- 多选题: 请求3/实际3
- 判断题: 请求5/实际5
- FILL_IN_BLANKS: 请求3/实际0 (不足3)
- 简答题: 请求2/实际2
2025-05-19 19:30:08.917 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:30:08.917 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 请注意：FILL_IN_BLANKS数量不足，请求3题，实际仅有0题。(全局配置)
2025-05-19 19:30:08.974 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:30:08.975 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 19:37:16.402 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 19:37:16.403 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 19:37:16.403 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 19:37:23.709 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-19 19:37:25.674 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-19 19:45:21.436 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 40 not found for deletion.
2025-05-19 19:45:58.702 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 39 not found for deletion.
2025-05-19 19:45:58.716 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 39 not found for deletion.
2025-05-19 19:45:58.730 [http-nio-8081-exec-3] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 39 not found for deletion.
2025-05-19 19:45:58.742 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 39 not found for deletion.
2025-05-19 19:45:58.754 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 39 not found for deletion.
2025-05-19 19:46:13.985 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 38 not found for deletion.
2025-05-19 19:46:33.355 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-19 19:46:33.514 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-19 19:46:33.550 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-19 20:13:12.415 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 20:13:12.416 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 20:13:12.416 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 20:13:20.544 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-19 20:13:22.752 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-19 20:38:51.469 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 20:38:51.517 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 20:38:51.682 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Type 'FILL_IN_BLANKS' (requested 3) has NO available questions in the database.
2025-05-19 20:38:51.726 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Type 'shortAnswer' (requested 2) has NO available questions in the database.
2025-05-19 20:38:51.727 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: CRITICAL ISSUE - Some requested topic types have ZERO available questions in the database. Check the knowledge point's question bank.
2025-05-19 20:38:51.766 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 20:38:51.766 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Discrepancy for type 'FILL_IN_BLANKS' (requested key 'FILL_IN_BLANKS'). Requested: 3, Actual: 0. 知识点中可能没有足够此类型的题目.
2025-05-19 20:38:51.766 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Discrepancy for type 'shortAnswer' (requested key 'SHORT_ANSWER'). Requested: 2, Actual: 0. 知识点中可能没有足够此类型的题目.
2025-05-19 20:38:51.770 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 20:38:51.771 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 题库中可用题目不足。
请求总题目数：18，实际可用题目数：13。
这可能是因为选择的知识点中没有足够的题目。
具体题型统计：
- 单选题: 请求5/实际5
- 多选题: 请求3/实际3
- 判断题: 请求5/实际5
- FILL_IN_BLANKS: 请求3/实际0 (不足3)
- 简答题: 请求2/实际0 (不足2)
2025-05-19 20:38:51.771 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 20:38:51.772 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 请注意：FILL_IN_BLANKS数量不足，请求3题，实际仅有0题。(全局配置)
2025-05-19 20:38:51.772 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 请注意：简答题数量不足，请求2题，实际仅有0题。(全局配置)
2025-05-19 20:38:51.821 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 20:38:51.821 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未识别题型: 'FILL_IN_BLANKS'. 将保持原样使用。
2025-05-19 21:19:08.821 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 21:19:08.822 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 21:19:08.822 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 21:19:08.822 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 21:19:17.683 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-19 21:19:20.045 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-19 21:31:15.091 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:31:15.103 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:31:30.898 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:31:30.906 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:31:38.233 [http-nio-8081-exec-2] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 50 not found or is deleted.
2025-05-19 21:31:38.243 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 50 not found or is deleted.
2025-05-19 21:44:27.822 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:44:27.831 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:45:46.068 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:45:46.078 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:52:38.588 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:52:38.599 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:54:44.711 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 21:54:44.720 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 22:06:17.033 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 22:06:17.034 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 22:06:17.034 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 22:06:17.034 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-19 22:06:25.181 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-19 22:06:27.731 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-19 22:06:48.160 [http-nio-8081-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-05-19 22:06:50.099 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
2025-05-19 22:06:50.108 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - Paper with id 51 not found or is deleted.
