/**
 * 对话状态管理模块
 */
const ChatState = {
    // 主界面状态
    current: {
        bookUrl: '',
        knowId: '',
        title: '新对话'
    },
    
    // 新建对话状态
    newChat: {
        bookUrl: '',
        knowId: '',
        title: '新建对话'
    },
    
    // 历史记录
    histories: [],
    
    init() {
        this.loadFromStorage();
        this.bindEvents();
        this.updateUI();
        console.log('ChatState初始化完成', this.current, this.newChat);
    },
    
    saveBookUrl(url, context = 'current') {
        console.log(`保存书籍URL: ${url}, 上下文: ${context}`);
        if (context === 'new') {
            this.newChat.bookUrl = url;
        } else {
            this.current.bookUrl = url;
        }
        this.saveToStorage();
        this.updateUI(context);
    },
    
    saveToStorage() {
        localStorage.setItem('currentChat', JSON.stringify(this.current));
        localStorage.setItem('newChat', JSON.stringify(this.newChat));
        console.log('状态已保存到存储', { current: this.current, newChat: this.newChat });
    },
    
    loadFromStorage() {
        try {
            // 加载主界面状态
            const savedCurrent = localStorage.getItem('currentChat');
            if (savedCurrent) {
                this.current = {...this.current, ...JSON.parse(savedCurrent)};
            }
            
            // 加载新建对话状态
            const savedNew = localStorage.getItem('newChat');
            if (savedNew) {
                this.newChat = {...this.newChat, ...JSON.parse(savedNew)};
            }
            
            console.log('从存储加载状态', { current: this.current, newChat: this.newChat });
        } catch (e) {
            console.error('加载保存的对话状态失败:', e);
        }
    },
    
    updateUI(context = 'both') {
        console.log(`更新UI, 上下文: ${context}`);
        
        // 根据上下文更新UI
        if (context === 'current' || context === 'both') {
            $('#currentBookUrl').val(this.current.bookUrl);
            if (this.current.knowId) {
                $('#knowId').val(this.current.knowId);
            }
            if (this.current.title) {
                $('#currentChatTitle').text(this.current.title);
            }
        }
        
        if (context === 'new' || context === 'both') {
            $('#newBookUrl').val(this.newChat.bookUrl);
            // 如果有新建对话特定的knowId输入框，也更新它
            const $newKnowId = $('#newKnowId');
            if ($newKnowId.length && this.newChat.knowId) {
                $newKnowId.val(this.newChat.knowId);
            }
        }
    },
    
    bindEvents() {
        // 监听主界面输入变化
        $('#currentBookUrl').on('input change', (e) => {
            this.current.bookUrl = e.target.value;
            this.saveToStorage();
            console.log('主界面书籍URL更新:', this.current.bookUrl);
        });

        $('#knowId').on('input change', (e) => {
            this.current.knowId = e.target.value;
            this.saveToStorage();
        });

        // 监听新建对话输入变化
        $('#newBookUrl').on('input change', (e) => {
            this.newChat.bookUrl = e.target.value;
            this.saveToStorage();
            console.log('新建对话书籍URL更新:', this.newChat.bookUrl);
        });

        // 如果有新建对话特定的knowId输入框，也监听它
        const $newKnowId = $('#newKnowId');
        if ($newKnowId.length) {
            $newKnowId.on('input change', (e) => {
                this.newChat.knowId = e.target.value;
                this.saveToStorage();
            });
        }

        // 定期保存状态（作为备份机制）
        setInterval(() => {
            this.saveToStorage();
        }, 30000); // 每30秒保存一次
    },

    reset(context = 'both') {
        console.log(`重置状态, 上下文: ${context}`);
        
        if (context === 'current' || context === 'both') {
            this.current = {
                bookUrl: '',
                knowId: '',
                title: '新对话'
            };
        }
        
        if (context === 'new' || context === 'both') {
            this.newChat = {
                bookUrl: '',
                knowId: '',
                title: '新建对话'
            };
        }
        
        this.saveToStorage();
        this.updateUI(context);
    },

    // 获取指定上下文的状态
    getState(context = 'current') {
        return context === 'new' ? this.newChat : this.current;
    }
};

// 页面加载完成后初始化状态管理
document.addEventListener('DOMContentLoaded', () => {
    ChatState.init();
}); 