package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping("/question")
@Slf4j
public class QuestionController {

    @GetMapping("/create")
    public String createPage(HttpServletRequest request, HttpServletResponse response) {
        // Redirect to chat page since question/create template has been deleted
        log.info("Redirecting GET request from /question/create to /main/chat");
        return "redirect:/main/chat";
    }
    
    @PostMapping("/create")
    public String createPagePost(@RequestParam(value = "token", required = false) String formToken,
                              HttpServletRequest request) {
        // Redirect to chat page since question/create template has been deleted
        log.info("Redirecting POST request from /question/create to /main/chat");
        return "redirect:/main/chat";
    }
    
    @GetMapping("/bank")
    public String bankPage() {
        // Redirect to the new topic bank page
        log.info("Redirecting GET request from /question/bank to /topics/bank");
        return "redirect:/topics/bank";
    }
} 