/**
 * 环境检查脚本
 * 
 * 这个脚本检查常见的环境问题并提供解决方案
 */

(function() {
    console.log('环境检查脚本已加载');
    
    // 检查是否在开发环境中运行
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    
    // 监控控制台错误
    const originalConsoleError = console.error;
    console.error = function(...args) {
        // 调用原始 error 方法
        originalConsoleError.apply(console, args);
        
        // 检查是否为连接被拒绝错误
        const errorMessage = args.join(' ');
        if (/failed to fetch|networkerror|connection refused|连接被拒绝/.test(errorMessage.toLowerCase())) {
            checkBackendStatus();
        }
    };
    
    // 检查后端状态
    function checkBackendStatus() {
        if (!isLocalhost) return; // 只在本地环境检查
        
        const statusElement = document.createElement('div');
        statusElement.className = 'environment-warning';
        statusElement.innerHTML = `
            <div class="warning-header">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <h3>环境问题: 后端服务可能未启动</h3>
                <button class="close-btn">×</button>
            </div>
            <div class="warning-content">
                <p>无法连接到后端服务，请检查:</p>
                <ol>
                    <li>后端服务是否已经启动？</li>
                    <li>是否出现 Maven 错误？请确保 Maven 已安装并添加到系统 PATH</li>
                    <li>检查控制台是否有其他错误信息</li>
                </ol>
                <div class="solution">
                    <h4>解决方案:</h4>
                    <p>如果看到 <code>mvn: command not found</code> 或类似错误:</p>
                    <ol>
                        <li>确保已安装 Maven 并添加到 PATH 环境变量</li>
                        <li>尝试使用完整路径运行，例如: <code>C:\\path\\to\\maven\\bin\\mvn spring-boot:run</code></li>
                        <li>或者使用 IDE 的内置工具启动应用</li>
                    </ol>
                </div>
                <p class="dismiss-note">该消息只在本地开发环境显示。点击右上角 × 按钮关闭。</p>
            </div>
        `;
        
        document.body.appendChild(statusElement);
        
        // 添加关闭按钮事件
        const closeBtn = statusElement.querySelector('.close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                statusElement.remove();
            });
        }
        
        // 添加样式
        addEnvironmentWarningStyles();
    }
    
    // 添加样式
    function addEnvironmentWarningStyles() {
        if (document.getElementById('environment-warning-styles')) return;
        
        const styleElement = document.createElement('style');
        styleElement.id = 'environment-warning-styles';
        styleElement.textContent = `
            .environment-warning {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 400px;
                max-width: 90vw;
                background-color: #fff3cd;
                border: 1px solid #ffeeba;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            }
            
            .warning-header {
                display: flex;
                align-items: center;
                padding: 10px 15px;
                background-color: #ffeeba;
                color: #856404;
                border-bottom: 1px solid #ffeeba;
            }
            
            .warning-header h3 {
                margin: 0 0 0 10px;
                font-size: 16px;
                flex: 1;
            }
            
            .warning-header .close-btn {
                background: transparent;
                border: none;
                color: #856404;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                height: 24px;
                width: 24px;
                line-height: 24px;
                text-align: center;
            }
            
            .warning-content {
                padding: 15px;
                color: #333;
                font-size: 14px;
                max-height: 70vh;
                overflow-y: auto;
            }
            
            .warning-content p, .warning-content ol {
                margin: 0 0 10px;
            }
            
            .warning-content ol {
                padding-left: 20px;
            }
            
            .warning-content li {
                margin-bottom: 5px;
            }
            
            .solution {
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
            }
            
            .solution h4 {
                margin: 0 0 8px;
                font-size: 14px;
                font-weight: 600;
            }
            
            .solution code {
                background-color: #e9ecef;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
                font-size: 12px;
            }
            
            .dismiss-note {
                font-size: 12px;
                color: #6c757d;
                margin-top: 15px;
                text-align: center;
            }
            
            @media (max-width: 576px) {
                .environment-warning {
                    width: 90vw;
                    top: 10px;
                    right: 5%;
                }
            }
        `;
        
        document.head.appendChild(styleElement);
    }
    
    // 检查API访问错误
    function monitorApiErrors() {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .catch(error => {
                    if (error.message.includes('Failed to fetch') && isLocalhost) {
                        console.error('API访问失败:', error);
                        checkBackendStatus();
                    }
                    return Promise.reject(error);
                });
        };
    }
    
    // 在文档加载完成后执行检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', monitorApiErrors);
    } else {
        monitorApiErrors();
    }
    
    console.log('环境检查脚本初始化完成');
})(); 