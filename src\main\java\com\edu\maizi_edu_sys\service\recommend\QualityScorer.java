package com.edu.maizi_edu_sys.service.recommend;

import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Service
public class QualityScorer {

    @Value("${algorithm.quality.usage-weight:0.6}") // 从配置文件读取，提供默认值
    private double weightUsage;

    @Value("${algorithm.quality.freshness-weight:0.4}") // 从配置文件读取，提供默认值
    private double weightFreshness;

    private final TopicEnhancementDataMapper enhancementDataMapper;

    public QualityScorer(TopicEnhancementDataMapper enhancementDataMapper) {
        this.enhancementDataMapper = enhancementDataMapper;
    }

    /**
     * 计算题目的质量评分
     * QualityScore = weightUsage * (1 / (1 + usageCount)) + weightFreshness * 新鲜度系数
     * @param enhancementData 题目的增强数据
     * @return 计算得到的质量分
     */
    public BigDecimal calculateQualityScore(TopicEnhancementData enhancementData) {
        if (enhancementData == null) {
            return BigDecimal.ZERO; // 或者根据业务返回null或特定值
        }

        // 计算使用次数组件 - 使用次数越少，质量评分越高
        BigDecimal usageComponent = BigDecimal.ZERO;
        if (enhancementData.getUsageCount() != null) {
            double usageValue = 1.0 / (1.0 + enhancementData.getUsageCount());
            usageComponent = BigDecimal.valueOf(usageValue).multiply(BigDecimal.valueOf(weightUsage));
        } else {
            // 如果没有使用记录，给予最高分
            usageComponent = BigDecimal.valueOf(weightUsage);
        }

        // 计算新鲜度系数 (基于创建时间的新近程度)
        BigDecimal freshnessComponent = BigDecimal.valueOf(weightFreshness);
        // 如果需要基于时间计算新鲜度，可以使用createTime字段

        BigDecimal totalScore = usageComponent.add(freshnessComponent);
        return totalScore.setScale(4, RoundingMode.HALF_UP); // 保留4位小数
    }

    /**
     * Score a paper based on the quality of its topics
     * @param topics List of topics in the paper
     * @return Quality score of the paper
     */
    public double scorePaper(List<Topic> topics) {
        if (topics == null || topics.isEmpty()) {
            return 0.0;
        }
        
        double totalScore = 0.0;
        int count = 0;
        
        for (Topic topic : topics) {
            TopicEnhancementData data = enhancementDataMapper.selectById(topic.getId());
            if (data != null) {
                // 计算题目的质量分数
                double qualityScore = calculateQualityScore(data).doubleValue();
                totalScore += qualityScore;
                count++;
            }
        }
        
        return count > 0 ? totalScore / count : 0.5; // Default to medium quality if no data
    }
} 