package com.edu.maizi_edu_sys.dto;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperWithTopicsDTO {
    private Long id;
    private String title;
    private Integer type;
    private Integer knowledgeId;
    private String knowledgeName;
    private Integer totalScore;
    private Integer actualTotalScore;
    private Double difficulty;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String content; // Retain if raw content JSON is needed
    private String config;  // Retain if raw config JSON is needed
    private List<Topic> topics; // The list of Topic entities
} 