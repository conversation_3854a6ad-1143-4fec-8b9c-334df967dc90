/*  下拉菜单显示问题修复样式 */

/* 增强下载菜单的基本样式 */
.enhanced-download-menu {
    min-width: 320px !important;
    max-width: 400px !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    padding: 0 !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    border-radius: 8px !important;
    z-index: 1070 !important; /*  提高层级，确保在页码栏之上 */
}

/* 确保下拉菜单在视口内显示 */
.dropdown-menu.show {
    transform: none !important;
    position: absolute !important;
    will-change: transform !important;
}

/* 修复下拉菜单定位问题 */
.btn-group .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 1050 !important;
    display: none !important;
    float: left !important;
    min-width: 320px !important;
    padding: 0 !important;
    margin: 2px 0 0 !important;
    font-size: 0.875rem !important;
    color: #212529 !important;
    text-align: left !important;
    list-style: none !important;
    background-color: #fff !important;
    background-clip: padding-box !important;
    border: 1px solid rgba(0,0,0,.15) !important;
    border-radius: 0.375rem !important;
}

.btn-group .dropdown-menu.show {
    display: block !important;
}

/* 修复下拉菜单内容区域 */
.enhanced-download-menu .px-3 {
    padding: 1rem !important;
}

/* 版本选择器样式 */
.enhanced-download-menu .version-selector {
    width: 100% !important;
    margin-bottom: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    color: #495057 !important;
    background-color: #fff !important;
    background-image: none !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
}

/* 版本描述样式 */
.enhanced-download-menu .version-description {
    display: block !important;
    margin-bottom: 0.75rem !important;
    padding: 0.5rem !important;
    background-color: #f8f9fa !important;
    border-radius: 4px !important;
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
    color: #6c757d !important;
}

/* 下拉菜单项样式 */
.enhanced-download-menu .dropdown-item {
    padding: 0.75rem 1rem !important;
    display: flex !important;
    align-items: center !important;
    border: none !important;
    background: none !important;
    width: 100% !important;
    text-align: left !important;
    transition: background-color 0.15s ease-in-out !important;
    color: #212529 !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    cursor: pointer !important;
}

.enhanced-download-menu .dropdown-item:hover,
.enhanced-download-menu .dropdown-item:focus {
    background-color: #f8f9fa !important;
    color: #16181b !important;
    text-decoration: none !important;
}

.enhanced-download-menu .dropdown-item.text-danger:hover {
    background-color: #f5c6cb !important;
    color: #721c24 !important;
}

.enhanced-download-menu .dropdown-item i {
    margin-right: 0.75rem !important;
    width: 16px !important;
    text-align: center !important;
    flex-shrink: 0 !important;
}

/* 分隔线样式 */
.enhanced-download-menu .dropdown-divider {
    margin: 0.5rem 0 !important;
    border-top: 1px solid #e9ecef !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* 标题样式 */
.enhanced-download-menu .dropdown-header {
    padding: 0.75rem 1rem 0.5rem !important;
    margin-bottom: 0 !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #6c757d !important;
    text-transform: none !important;
    white-space: nowrap !important;
}

/* 确保下拉菜单不会被遮挡 */
.btn-group.dropup .dropdown-menu,
.btn-group.dropdown .dropdown-menu {
    z-index: 1070 !important; /*  提高层级 */
}

/* 修复下拉菜单在表格中的显示问题 */
.table .btn-group .dropdown-menu {
    position: absolute !important;
    z-index: 1050 !important;
}

/* 历史试卷表格中的下拉菜单特殊处理 */
#paperHistoryTable .btn-group .dropdown-menu,
#paperHistoryTableMobile .btn-group .dropdown-menu {
    position: fixed !important;
    z-index: 1080 !important; /*  最高层级，确保在所有元素之上 */
    max-height: 70vh !important;
    overflow-y: auto !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .enhanced-download-menu {
        min-width: 280px !important;
        max-width: 90vw !important;
        max-height: 70vh !important;
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 1080 !important; /*  移动端也使用最高层级 */
    }

    /* 移动端下拉菜单项增大点击区域 */
    .enhanced-download-menu .dropdown-item {
        padding: 1rem !important;
        font-size: 0.9rem !important;
    }

    /* 移动端版本选择器 */
    .enhanced-download-menu .version-selector {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.9rem !important;
    }
}

/* 小屏幕设备适配 */
@media (max-width: 576px) {
    .enhanced-download-menu {
        min-width: 260px !important;
        max-width: 95vw !important;
        max-height: 80vh !important;
    }
}

/* 滚动条样式 */
.enhanced-download-menu::-webkit-scrollbar {
    width: 6px;
}

.enhanced-download-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.enhanced-download-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.enhanced-download-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox 滚动条样式 */
.enhanced-download-menu {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 修复Bootstrap下拉菜单的定位问题 */
.dropdown-menu[data-bs-popper] {
    position: absolute !important;
    inset: 0px auto auto 0px !important;
    margin: 0px !important;
    transform: translate(0px, 38px) !important;
}

/* 确保下拉菜单内容可见 */
.dropdown-menu.show {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* 修复下拉菜单在容器边缘的显示问题 */
.dropdown {
    position: relative !important;
}

.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 1000 !important;
    display: none !important;
    float: left !important;
    min-width: 10rem !important;
    padding: 0.5rem 0 !important;
    margin: 0.125rem 0 0 !important;
    font-size: 0.875rem !important;
    color: #212529 !important;
    text-align: left !important;
    list-style: none !important;
    background-color: #fff !important;
    background-clip: padding-box !important;
    border: 1px solid rgba(0,0,0,.15) !important;
    border-radius: 0.375rem !important;
}

/* 强制显示下拉菜单 */
.dropdown-menu.show {
    display: block !important;
}

/* 修复表格行中的下拉菜单 */
.table-responsive .dropdown-menu {
    position: fixed !important;
    z-index: 1050 !important;
}

/* 确保下拉菜单不被父容器裁剪 */
.card-body {
    overflow: visible !important;
}

.table-responsive {
    overflow: visible !important;
}

/* 调试用：高亮显示下拉菜单 */
.enhanced-download-menu.debug {
    border: 2px solid red !important;
    background-color: yellow !important;
}

/* 确保下拉菜单按钮可点击 */
.dropdown-toggle {
    cursor: pointer !important;
}

.dropdown-toggle::after {
    display: inline-block !important;
    margin-left: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-right: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-left: 0.3em solid transparent !important;
}

/*  确保页码栏不会遮挡下拉菜单 */
.card-footer {
    z-index: 1000 !important;
    position: relative !important;
}

#paperHistoryPagination,
#paperHistoryPaginationMobile {
    z-index: 1000 !important;
    position: relative !important;
}

/*  确保下拉菜单在页码栏之上 */
.dropdown-menu.enhanced-download-menu {
    z-index: 1080 !important;
    position: fixed !important;
}

/*  修复历史试卷区域的层级问题 */
.card {
    position: relative !important;
    z-index: auto !important;
}

.card-body {
    position: relative !important;
    z-index: auto !important;
}

/*  特殊处理：当下拉菜单显示时，降低其他元素的层级 */
body.dropdown-open .card-footer {
    z-index: 999 !important;
}

body.dropdown-open #paperHistoryPagination,
body.dropdown-open #paperHistoryPaginationMobile {
    z-index: 999 !important;
}

/*  确保下拉菜单在所有Bootstrap组件之上 */
.dropdown-menu {
    z-index: 1080 !important;
}

.dropdown-menu.show {
    z-index: 1080 !important;
}
