# 智能化组卷系统遗传算法参数配置指南

---

## **1. 概述**  
本文档详细说明智能化组卷系统中遗传算法（Genetic Algorithm, GA）的核心参数及其配置方法

---

## **2. 参数配置方法**  
### **2.1 配置文件路径**  
参数通过后端配置文件（`application.yml`）管理，路径如下：  
```yaml  
algorithm:  
  genetic:  
    population-size: 100  
    max-generations: 200  
    min-generations: 30  
    crossover-rate: 0.8  
    mutation-rate: 0.1  
    tournament-size: 5  
    fitness-weights:  
      score: 0.4  
      difficulty-distribution: 0.2  
      cognitive-level-distribution: 0.1  
      knowledge-coverage: 0.1  
      # 其他权重...  
```

### **2.2 配置生效**  
修改配置文件后，需**重启后端服务**以加载新参数。

---

## **3. 核心参数详解**  
### **3.1 进化控制参数**  

| 参数名            | 默认值 | 含义                               | 影响与调整建议                                               |
| ----------------- | ------ | ---------------------------------- | ------------------------------------------------------------ |
| `population-size` | 100    | 种群大小（每代候选试卷方案数量）   | **增大**：提高解质量，但增加计算时间（建议范围：50-200）；**减小**：降低质量，但缩短响应时间。 |
| `max-generations` | 200    | 最大进化代数                       | **增大**：延长搜索时间，可能找到更优解；**减小**：可能导致早熟收敛。建议监控适应度曲线动态调整。 |
| `min-generations` | 30     | 最小进化代数（强制迭代次数）       | 保证基础搜索广度，建议保持默认或微调（如30-50）。            |
| `crossover-rate`  | 0.8    | 交叉概率（0.0-1.0）                | 推荐0.7-0.9，过高可能导致基因组合不稳定，过低降低搜索效率。  |
| `mutation-rate`   | 0.1    | 单基因位变异概率（0.0-1.0）        | 推荐0.05-0.2，过低易陷入局部最优，过高接近随机搜索。         |
| `tournament-size` | 5      | 锦标赛选择策略中每次竞争的个体数量 | **增大**：加速收敛但降低多样性；**减小**：维持多样性但减慢收敛。推荐范围3-7。 |

---

### **3.2 适应度函数权重**  
适应度函数通过加权多目标优化试卷质量，权重表示各目标的相对重要性。  

| 权重参数名                                     | 默认值 | 目标描述                                               | 调整建议                                                     |
| ---------------------------------------------- | ------ | ------------------------------------------------------ | ------------------------------------------------------------ |
| `fitness-weights.score`                        | 0.4    | 总分匹配度（实际总分与目标总分的接近程度）             | 若总分优先级最高，可提高至0.5-0.6；若允许一定偏差，可降低至0.3。 |
| `fitness-weights.difficulty-distribution`      | 0.2    | 难度分布匹配度（实际难度占比与目标的吻合度）           | 若需严格遵循难度分布，可提高至0.3；若题库难度分布不均，可略微降低。 |
| `fitness-weights.cognitive-level-distribution` | 0.1    | 认知层次分布匹配度（需依赖题目元数据中的认知层次标签） | 若需强化认知层次覆盖，可提高至0.2-0.3；若无相关数据，可设为0。 |
| `fitness-weights.knowledge-coverage`           | 0.1    | 知识点覆盖度（覆盖目标知识点比例）                     | 若知识点覆盖为硬性要求，可提高至0.2-0.3；若题库覆盖不足，需结合业务逻辑调整。 |

---

## **4. 调整策略与注意事项**  
### **4.1 性能与质量平衡策略**  
1. **优先调整低开销参数**：  
   - **适应度权重**：调整后仅影响计算方向，不影响单次计算耗时。  
   - **交叉率/变异率**：在合理范围内（如±0.1）调整，对性能影响较小。  

2. **谨慎调整高开销参数**：  
   - **种群大小（`population-size`）**：每增加50，计算时间可能增长30%-50%。  
   - **最大代数（`max-generations`）**：与总耗时线性相关，建议通过日志观察收敛情况动态优化。  

3. **硬件资源适配**：  
   - 高配置服务器可适当增加`population-size`（如150-200）和`max-generations`（如300）。  
   - 低配置环境建议优先保证`population-size`≥50，`max-generations`≥100。  

### **4.2 常见问题与解决**  
| 问题现象                   | 可能原因             | 解决方案                                                     |
| -------------------------- | -------------------- | ------------------------------------------------------------ |
| 组卷时间过长               | 种群过大或代数过多   | 逐步降低`population-size`和`max-generations`，观察质量变化。 |
| 试卷总分偏差较大           | 适应度权重分配不合理 | 提高`fitness-weights.score`权重，或启用`DPAdjuster`后处理。  |
| 题型/难度分布不匹配        | 对应权重过低         | 提高`fitness-weights.difficulty-distribution`及相关题型分布权重。 |
| 算法早熟收敛（适应度停滞） | 变异率过低或种群过小 | 提高`mutation-rate`至0.15-0.2，或增加`population-size`。     |

---

## **5. 附录**  
### **5.1 示例配置文件片段**  
```yaml  
# 高性能场景配置（适用于大型题库）  
algorithm:  
  genetic:  
    population-size: 150  
    max-generations: 250  
    crossover-rate: 0.85  
    mutation-rate: 0.15  
    fitness-weights:  
      score: 0.5  
      difficulty-distribution: 0.25  
```

### **5.2 关键日志信息说明**  
- **每代适应度输出**：  
  ```  
  Generation 50 | Best Fitness: 0.92 | Avg Fitness: 0.76  
  ```
  若多代`Best Fitness`无显著提升，可考虑提前终止。  

- **动态规划调整日志**：  
  ```  
  DPAdjuster: Adjusted score from 105 to 100 by removing 3 topics.  
  ```
  表明通过移除题目精确匹配总分。  
