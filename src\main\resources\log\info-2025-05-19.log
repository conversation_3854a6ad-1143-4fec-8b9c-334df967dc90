2025-05-19 19:18:11.138 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 18584 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-19 19:18:11.142 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 19:18:13.055 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 19:18:13.060 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 19:18:13.690 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 608 ms. Found 3 JPA repository interfaces.
2025-05-19 19:18:13.707 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 19:18:13.709 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 19:18:13.750 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:18:13.752 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:18:13.753 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:18:13.753 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-05-19 19:18:15.667 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 19:18:15.684 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 19:18:15.685 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 19:18:15.685 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 19:18:15.979 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 19:18:15.980 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4759 ms
2025-05-19 19:18:16.940 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 19:18:17.293 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 19:18:18.649 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 19:18:18.675 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 19:18:18.675 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 19:18:18.675 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 19:18:21.028 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-19 19:18:21.267 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 19:18:22.449 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 19:18:23.391 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 19:18:23.423 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-19 19:18:24.125 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 19:18:24.195 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 19:18:24.226 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 19:18:24.229 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 19:18:24.229 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@89ff043]]
2025-05-19 19:18:24.229 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 19:18:24.245 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 13.852 seconds (JVM running for 14.842)
2025-05-19 19:18:24.266 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 19:18:24.269 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 19:18:24.269 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 19:18:24.269 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 19:18:24.269 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 19:18:24.269 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 19:18:24.269 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 19:18:24.270 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 19:18:24.274 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 19:18:24.382 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 38
2025-05-19 19:19:49.108 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYzMDI1NCwiZXhwIjoxNzQ3NzE2NjU0fQ.2BA94j89S-a3YFMgYcklox1Z1cxyPzWTe_sufoe8zvZBYagI7KhdGmBtiXPnrLqMh_-DzOFnmH_kw5eLQFbHKQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 19:19:49.123 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 19:19:49.124 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 19:19:49.127 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-05-19 19:19:49.172 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:19:53.363 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:20:02.799 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 19:20:02.895 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 生理学  专项练习
2025-05-19 19:20:02.913 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='生理学  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=61, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 19:20:04.463 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 844. Requested global counts (for warning reference): {SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 19:20:04.463 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 19:20:04.463 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 19:20:04.464 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 19:20:04.464 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 19:20:04.464 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 19:20:04.464 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 19:20:04.565 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=543, judgment=117, multipleChoice=177, shortAnswer=7}
2025-05-19 19:20:04.565 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=5, judgment=5, multipleChoice=5, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 19:20:04.981 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题: 请求5/实际543
- 判断题: 请求5/实际117
- 多选题: 请求5/实际177
- FILL_IN_BLANKS: 请求3/实际0 [警告: 此题型在题库中完全不存在!]
- 简答题: 请求2/实际7
2025-05-19 19:20:05.071 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'singleChoice' (requested 5)
2025-05-19 19:20:05.071 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'multipleChoice' (requested 5)
2025-05-19 19:20:05.071 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'judgment' (requested 5)
2025-05-19 19:20:05.071 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 2 topics of type 'shortAnswer' (requested 2)
2025-05-19 19:20:05.073 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 844. Final result size: 17. Actual counts per type after enforcement: {singleChoice=5, judgment=5, multipleChoice=5, shortAnswer=2}
2025-05-19 19:20:05.074 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 59) after enforcing type counts...
2025-05-19 19:20:05.076 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 17 topics, actual score: 51 (target score: 59)
2025-05-19 19:20:05.206 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 39. Title: 生理学  专项练习
2025-05-19 19:20:05.358 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 17 topics.
2025-05-19 19:20:06.775 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/39, Token: exists
2025-05-19 19:20:06.780 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 39
2025-05-19 19:20:06.787 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 39
2025-05-19 19:20:06.793 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 39
2025-05-19 19:20:06.809 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 39
2025-05-19 19:20:06.928 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:20:06.938 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:20:06.939 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:20:07.361 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=1, size=10, records=10
2025-05-19 19:20:16.948 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/39, Token: exists
2025-05-19 19:20:17.135 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simhei.ttf
2025-05-19 19:20:17.208 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf
2025-05-19 19:20:17.215 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 19:20:17.238 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 39
2025-05-19 19:20:17.245 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 39
2025-05-19 19:20:17.247 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 39
2025-05-19 19:20:17.419 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 39
2025-05-19 19:20:17.468 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/39, Token: exists
2025-05-19 19:20:17.475 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simhei.ttf
2025-05-19 19:20:17.475 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf
2025-05-19 19:20:17.477 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 19:20:17.479 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 39
2025-05-19 19:20:17.485 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 39
2025-05-19 19:20:17.486 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 39
2025-05-19 19:20:17.553 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 39
2025-05-19 19:20:17.561 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/39, Token: exists
2025-05-19 19:20:17.567 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simhei.ttf
2025-05-19 19:20:17.568 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf
2025-05-19 19:20:17.568 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 19:20:17.570 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 39
2025-05-19 19:20:17.574 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 39
2025-05-19 19:20:17.576 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 39
2025-05-19 19:20:17.620 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 39
2025-05-19 19:20:33.321 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:20:35.285 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:20:35.295 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 39
2025-05-19 19:20:35.299 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 39
2025-05-19 19:20:35.300 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 39
2025-05-19 19:20:35.310 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:20:35.320 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 17 topics for paper id: 39
2025-05-19 19:20:35.324 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 17 topics in database (from 17 requested IDs) for paper id: 39
2025-05-19 19:20:35.325 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 17 ordered topics for paper id: 39
2025-05-19 19:21:11.643 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:29:16.909 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 19:29:16.909 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@89ff043]]
2025-05-19 19:29:16.909 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 19:29:18.183 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 19:29:18.186 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 19:29:18.200 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 19:29:31.175 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 24552 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-19 19:29:31.180 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 19:29:32.910 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 19:29:32.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 19:29:33.509 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 580 ms. Found 3 JPA repository interfaces.
2025-05-19 19:29:33.524 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 19:29:33.525 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 19:29:33.572 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:29:33.574 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:29:33.575 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:29:33.575 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-05-19 19:29:35.669 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 19:29:35.690 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 19:29:35.691 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 19:29:35.691 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 19:29:35.970 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 19:29:35.971 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4701 ms
2025-05-19 19:29:36.989 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 19:29:37.357 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 19:29:38.812 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 19:29:38.830 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 19:29:38.831 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 19:29:38.831 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 19:29:41.304 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-19 19:29:41.639 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 19:29:43.153 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 19:29:44.310 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 19:29:44.349 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-19 19:29:45.126 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 19:29:45.226 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 19:29:45.255 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 19:29:45.258 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 19:29:45.258 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7621c3f7]]
2025-05-19 19:29:45.258 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 19:29:45.275 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 14.925 seconds (JVM running for 15.944)
2025-05-19 19:29:45.295 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 19:29:45.298 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 19:29:45.298 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 19:29:45.298 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 19:29:45.298 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 19:29:45.298 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 19:29:45.299 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 19:29:45.304 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 19:29:45.426 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 39
2025-05-19 19:29:50.620 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYzMDI1NCwiZXhwIjoxNzQ3NzE2NjU0fQ.2BA94j89S-a3YFMgYcklox1Z1cxyPzWTe_sufoe8zvZBYagI7KhdGmBtiXPnrLqMh_-DzOFnmH_kw5eLQFbHKQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 19:29:50.637 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 19:29:50.638 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 19:29:50.640 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-19 19:29:51.550 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 19:29:51.591 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:29:51.592 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:29:51.773 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 19:29:51.778 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:29:51.787 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:29:51.797 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 19:29:51.892 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 19:29:51.948 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:29:52.307 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:29:52.380 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=1, size=10, records=10
2025-05-19 19:29:52.401 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:29:52.405 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:29:52.406 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:29:52.434 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=1, size=10, records=10
2025-05-19 19:29:53.924 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:29:56.428 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 19:29:56.446 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [61]
2025-05-19 19:29:56.453 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=1084, judgment=217, multipleChoice=329, shortAnswer=13}, total available topics: 1643
2025-05-19 19:30:06.613 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 19:30:06.669 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 生理学  专项练习
2025-05-19 19:30:06.682 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='生理学  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=61, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 19:30:08.114 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 798. Requested global counts (for warning reference): {SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 19:30:08.115 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 19:30:08.116 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 19:30:08.116 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 19:30:08.116 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 19:30:08.116 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 19:30:08.117 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 19:30:08.221 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=499, judgment=113, multipleChoice=177, shortAnswer=9}
2025-05-19 19:30:08.222 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=5, judgment=5, multipleChoice=3, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 19:30:08.833 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题: 请求5/实际499
- 判断题: 请求5/实际113
- 多选题: 请求3/实际177
- FILL_IN_BLANKS: 请求3/实际0 [警告: 此题型在题库中完全不存在!]
- 简答题: 请求2/实际9
2025-05-19 19:30:08.908 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'singleChoice' (requested 5)
2025-05-19 19:30:08.908 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 3 topics of type 'multipleChoice' (requested 3)
2025-05-19 19:30:08.908 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'judgment' (requested 5)
2025-05-19 19:30:08.908 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 2 topics of type 'shortAnswer' (requested 2)
2025-05-19 19:30:08.910 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 798. Final result size: 15. Actual counts per type after enforcement: {singleChoice=5, judgment=5, multipleChoice=3, shortAnswer=2}
2025-05-19 19:30:08.911 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 53) after enforcing type counts...
2025-05-19 19:30:08.912 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 15 topics, actual score: 45 (target score: 53)
2025-05-19 19:30:08.974 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 40. Title: 生理学  专项练习
2025-05-19 19:30:09.057 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 15 topics.
2025-05-19 19:30:10.616 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:30:10.619 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:30:10.619 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:30:10.627 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:30:11.027 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/40, Token: exists
2025-05-19 19:30:11.031 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 40
2025-05-19 19:30:11.034 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 40
2025-05-19 19:30:11.040 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 40
2025-05-19 19:30:11.050 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 40
2025-05-19 19:30:15.433 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:30:16.616 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/40, Token: exists
2025-05-19 19:30:16.622 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 40
2025-05-19 19:30:16.625 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 40
2025-05-19 19:30:16.627 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 40
2025-05-19 19:30:16.640 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/40, Token: exists
2025-05-19 19:30:16.645 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 40
2025-05-19 19:30:16.652 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 40
2025-05-19 19:30:16.653 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 40
2025-05-19 19:30:18.691 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/40, Token: exists
2025-05-19 19:30:18.692 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 40
2025-05-19 19:30:18.696 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 15 topics for paper id: 40
2025-05-19 19:30:18.701 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 15 topics in database (from 15 requested IDs) for paper id: 40
2025-05-19 19:30:18.703 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 15 ordered topics for paper id: 40
2025-05-19 19:30:25.726 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 19:30:25.729 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 19:30:25.734 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 19:30:25.741 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 19:30:25.775 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:30:25.778 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:30:25.779 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:30:25.787 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:30:25.790 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:30:25.800 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:30:25.803 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:30:25.806 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:30:25.806 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:30:25.819 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:30:26.068 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:30:30.136 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:30:32.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 19:30:32.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [190]
2025-05-19 19:30:32.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=453, judgment=397, multipleChoice=7}, total available topics: 857
2025-05-19 19:37:00.730 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 19:37:00.731 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7621c3f7]]
2025-05-19 19:37:00.731 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 19:37:01.785 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 19:37:01.789 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 19:37:01.805 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 19:37:13.867 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 24484 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-19 19:37:13.872 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 19:37:15.561 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 19:37:15.564 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 19:37:16.115 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 535 ms. Found 3 JPA repository interfaces.
2025-05-19 19:37:16.135 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 19:37:16.137 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 19:37:16.186 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:37:16.189 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:37:16.190 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 19:37:16.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-05-19 19:37:18.337 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 19:37:18.354 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 19:37:18.355 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 19:37:18.355 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 19:37:18.633 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 19:37:18.634 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4684 ms
2025-05-19 19:37:19.633 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 19:37:19.996 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 19:37:21.334 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 19:37:21.355 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 19:37:21.355 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 19:37:21.355 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 19:37:23.697 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-19 19:37:23.933 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 19:37:25.207 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 19:37:26.129 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 19:37:26.157 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-19 19:37:26.812 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 19:37:26.884 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 19:37:26.919 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 19:37:26.923 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 19:37:26.924 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4e9d203e]]
2025-05-19 19:37:26.924 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 19:37:26.944 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 13.786 seconds (JVM running for 14.803)
2025-05-19 19:37:26.974 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 19:37:26.977 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 19:37:26.977 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 19:37:26.978 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 19:37:26.979 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 19:37:26.979 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 19:37:26.979 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 19:37:26.979 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 19:37:26.983 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 19:37:27.116 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 40
2025-05-19 19:37:35.994 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYzMDI1NCwiZXhwIjoxNzQ3NzE2NjU0fQ.2BA94j89S-a3YFMgYcklox1Z1cxyPzWTe_sufoe8zvZBYagI7KhdGmBtiXPnrLqMh_-DzOFnmH_kw5eLQFbHKQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 19:37:36.009 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 19:37:36.009 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 19:37:36.012 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-05-19 19:37:37.316 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 19:37:37.388 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:37:37.388 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:37:37.559 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 19:37:37.559 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:37:37.572 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:37:37.573 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 19:37:37.703 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 19:37:37.769 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:37:37.841 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:37:37.863 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:37:37.868 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:37:37.868 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:37:37.891 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:37:38.356 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:37:41.918 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:37:43.767 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 19:37:43.787 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[62]}
2025-05-19 19:37:43.787 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [62]
2025-05-19 19:37:43.797 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=487, judgment=106}, total available topics: 593
2025-05-19 19:44:11.806 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 19:44:11.811 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[61]}
2025-05-19 19:44:11.812 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [61]
2025-05-19 19:44:11.817 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=1084, judgment=217, multipleChoice=329, shortAnswer=13}, total available topics: 1643
2025-05-19 19:44:17.777 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 19:44:17.781 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 19:44:17.788 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 19:44:17.798 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 19:44:17.861 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:44:17.862 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:44:17.865 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:44:17.865 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:44:17.879 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:44:17.887 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:44:17.895 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:44:17.900 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:44:17.901 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:44:17.920 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=40, pages=4, current=1, size=10, records=10
2025-05-19 19:44:18.398 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:44:19.631 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:44:20.273 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:44:22.315 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 19:44:25.036 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 19:44:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[61]}
2025-05-19 19:44:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [61]
2025-05-19 19:44:25.046 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=1084, judgment=217, multipleChoice=329, shortAnswer=13}, total available topics: 1643
2025-05-19 19:45:09.195 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 19:45:09.199 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[59]}
2025-05-19 19:45:09.199 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [59]
2025-05-19 19:45:09.204 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=949, judgment=209, multipleChoice=292, shortAnswer=2}, total available topics: 1452
2025-05-19 19:45:21.385 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/40, Token: exists
2025-05-19 19:45:21.419 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 40
2025-05-19 19:45:21.431 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/40, Token: exists
2025-05-19 19:45:21.442 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:45:21.444 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:45:21.445 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:45:21.459 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=1, size=10, records=10
2025-05-19 19:45:28.701 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:45:28.706 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=1, size=10
2025-05-19 19:45:28.706 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=1, size=10
2025-05-19 19:45:28.719 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=2, size=10, records=10
2025-05-19 19:45:29.181 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:45:29.184 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=2, size=10
2025-05-19 19:45:29.184 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=2, size=10
2025-05-19 19:45:29.200 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=3, size=10, records=10
2025-05-19 19:45:29.605 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:45:29.607 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:45:29.607 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:45:29.618 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=39, pages=4, current=1, size=10, records=10
2025-05-19 19:45:58.676 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:45:58.687 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 39
2025-05-19 19:45:58.695 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:45:58.708 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:45:58.708 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:45:58.712 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:45:58.712 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:45:58.722 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:45:58.728 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=38, pages=4, current=1, size=10, records=10
2025-05-19 19:45:58.735 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:45:58.748 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/39, Token: exists
2025-05-19 19:46:13.942 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.943 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.944 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.945 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.949 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.949 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.962 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 38
2025-05-19 19:46:13.969 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 38
2025-05-19 19:46:13.970 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 38
2025-05-19 19:46:13.972 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 38
2025-05-19 19:46:13.973 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 38
2025-05-19 19:46:13.975 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 38
2025-05-19 19:46:13.976 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/38, Token: exists
2025-05-19 19:46:13.993 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:46:13.996 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:46:13.997 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:46:14.021 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:46:14.027 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:46:14.031 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:46:14.031 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:46:14.045 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:46:14.050 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:46:14.055 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:46:14.055 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:46:14.068 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:46:14.075 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:46:14.078 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:46:14.079 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:46:14.091 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:46:14.097 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:46:14.103 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:46:14.103 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:46:14.123 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:46:14.127 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:46:14.130 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:46:14.132 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:46:14.144 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:46:33.146 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/33, Token: exists
2025-05-19 19:46:33.300 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simhei.ttf
2025-05-19 19:46:33.350 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf
2025-05-19 19:46:33.356 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 19:46:33.373 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 5 topics for paper id: 33
2025-05-19 19:46:33.377 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 5 topics in database (from 5 requested IDs) for paper id: 33
2025-05-19 19:46:33.377 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 5 ordered topics for paper id: 33
2025-05-19 19:46:33.477 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 33
2025-05-19 19:46:33.508 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/33, Token: exists
2025-05-19 19:46:33.513 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simhei.ttf
2025-05-19 19:46:33.513 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf
2025-05-19 19:46:33.514 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 19:46:33.514 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 5 topics for paper id: 33
2025-05-19 19:46:33.518 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 5 topics in database (from 5 requested IDs) for paper id: 33
2025-05-19 19:46:33.518 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 5 ordered topics for paper id: 33
2025-05-19 19:46:33.535 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 33
2025-05-19 19:46:33.543 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/33, Token: exists
2025-05-19 19:46:33.548 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simhei.ttf
2025-05-19 19:46:33.549 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf
2025-05-19 19:46:33.550 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 19:46:33.551 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 5 topics for paper id: 33
2025-05-19 19:46:33.555 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 5 topics in database (from 5 requested IDs) for paper id: 33
2025-05-19 19:46:33.555 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 5 ordered topics for paper id: 33
2025-05-19 19:46:33.576 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 33
2025-05-19 19:50:12.488 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 19:50:12.489 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:50:12.490 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:50:12.496 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 19:50:12.496 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:50:12.496 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:50:12.500 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 19:50:12.506 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 19:50:12.508 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:50:12.509 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 19:50:12.515 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:50:12.519 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:50:12.519 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:50:12.531 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=37, pages=4, current=1, size=10, records=10
2025-05-19 19:50:13.009 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 19:50:16.622 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/37, Token: exists
2025-05-19 19:50:16.623 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/37, Token: exists
2025-05-19 19:50:16.642 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 37
2025-05-19 19:50:16.646 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Soft deleted paper with id: 37
2025-05-19 19:50:16.962 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:50:16.964 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:50:16.964 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:50:16.976 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 19:50:16.980 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:50:16.984 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:50:16.984 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:50:16.994 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 19:50:20.531 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/36, Token: exists
2025-05-19 19:50:20.537 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 19 topics for paper id: 36
2025-05-19 19:50:20.546 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 19 topics in database (from 19 requested IDs) for paper id: 36
2025-05-19 19:50:20.556 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 19 ordered topics for paper id: 36
2025-05-19 19:50:20.570 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/36, Token: exists
2025-05-19 19:50:20.573 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 19 topics for paper id: 36
2025-05-19 19:50:20.580 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 19 topics in database (from 19 requested IDs) for paper id: 36
2025-05-19 19:50:20.582 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 19 ordered topics for paper id: 36
2025-05-19 19:50:20.588 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/36, Token: exists
2025-05-19 19:50:20.594 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 19 topics for paper id: 36
2025-05-19 19:50:20.599 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 19 topics in database (from 19 requested IDs) for paper id: 36
2025-05-19 19:50:20.602 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 19 ordered topics for paper id: 36
2025-05-19 19:50:20.608 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/36, Token: exists
2025-05-19 19:50:20.613 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 19 topics for paper id: 36
2025-05-19 19:50:20.620 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 19 topics in database (from 19 requested IDs) for paper id: 36
2025-05-19 19:50:20.623 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 19 ordered topics for paper id: 36
2025-05-19 19:50:27.085 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 19:50:27.088 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 19:50:27.088 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 19:50:27.101 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:12:52.125 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 20:12:52.125 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4e9d203e]]
2025-05-19 20:12:52.125 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 20:12:53.332 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 20:12:53.336 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 20:12:53.350 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 20:13:09.826 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 23324 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-19 20:13:09.831 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 20:13:11.573 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 20:13:11.577 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 20:13:12.134 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 539 ms. Found 3 JPA repository interfaces.
2025-05-19 20:13:12.149 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 20:13:12.151 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 20:13:12.200 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 20:13:12.201 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 20:13:12.203 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 20:13:12.203 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
2025-05-19 20:13:14.275 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 20:13:14.292 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 20:13:14.293 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 20:13:14.293 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 20:13:14.565 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 20:13:14.565 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4647 ms
2025-05-19 20:13:15.646 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 20:13:16.030 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 20:13:17.952 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 20:13:17.986 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 20:13:17.987 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 20:13:17.987 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 20:13:20.531 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-19 20:13:20.958 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 20:13:22.204 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 20:13:23.297 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 20:13:23.328 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-19 20:13:24.166 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 20:13:24.257 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 20:13:24.300 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 20:13:24.303 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 20:13:24.304 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@30d2f9ea]]
2025-05-19 20:13:24.305 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 20:13:24.326 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 15.233 seconds (JVM running for 16.251)
2025-05-19 20:13:24.358 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 20:13:24.362 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 20:13:24.362 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 20:13:24.362 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 20:13:24.362 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 20:13:24.362 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 20:13:24.363 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 20:13:24.368 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 20:13:24.498 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 40
2025-05-19 20:13:30.333 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYzMDI1NCwiZXhwIjoxNzQ3NzE2NjU0fQ.2BA94j89S-a3YFMgYcklox1Z1cxyPzWTe_sufoe8zvZBYagI7KhdGmBtiXPnrLqMh_-DzOFnmH_kw5eLQFbHKQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 20:13:30.347 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 20:13:30.348 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 20:13:30.349 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-19 20:13:31.357 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 20:13:31.418 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:13:31.418 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:13:31.585 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 20:13:31.586 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:13:31.602 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 20:13:31.605 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:13:31.723 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 20:13:31.817 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:13:32.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 20:13:32.153 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:13:32.169 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:13:32.174 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:13:32.175 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:13:32.201 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:13:33.031 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 20:13:37.036 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:13:43.179 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:13:43.696 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:13:44.027 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:13:51.476 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:15:04.192 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:15:06.572 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:21:45.332 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 20:21:45.349 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[190]}
2025-05-19 20:21:45.349 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [190]
2025-05-19 20:21:45.357 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=453, judgment=397, multipleChoice=7}, total available topics: 857
2025-05-19 20:23:29.438 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 20:24:28.473 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:25:59.799 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 20:25:59.804 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 20:25:59.809 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 20:25:59.817 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 20:25:59.913 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:25:59.917 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:25:59.917 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:25:59.920 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:25:59.938 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:25:59.949 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:25:59.958 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:25:59.969 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:25:59.969 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:25:59.988 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:26:00.408 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 20:29:37.272 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 20:29:37.278 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 20:29:37.283 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 20:29:37.290 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 20:29:37.476 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:29:37.477 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:29:37.481 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:29:37.481 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:29:37.498 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:29:37.499 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:29:37.507 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:29:37.511 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:29:37.512 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:29:37.525 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:29:38.209 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 20:29:38.540 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 20:29:39.917 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:29:40.681 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:29:41.361 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:29:42.432 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:29:43.098 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:29:43.456 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:30:03.904 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 20:30:03.906 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[190, 191, 192, 193, 194]}
2025-05-19 20:30:03.907 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 5 knowledge points: [190, 191, 192, 193, 194]
2025-05-19 20:30:03.916 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=1114, fillBlank=112, judgment=913, multipleChoice=44, shortAnswer=62, groupQuestion=486}, total available topics: 2731
2025-05-19 20:34:03.451 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:34:03.455 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=1, size=10
2025-05-19 20:34:03.455 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=1, size=10
2025-05-19 20:34:03.468 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=2, size=10, records=10
2025-05-19 20:34:04.054 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:34:04.058 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=1, size=10
2025-05-19 20:34:04.058 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=1, size=10
2025-05-19 20:34:04.072 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=2, size=10, records=10
2025-05-19 20:34:04.344 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 20:34:04.347 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 20:34:04.347 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 20:34:04.357 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=36, pages=4, current=1, size=10, records=10
2025-05-19 20:38:46.801 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 20:38:46.805 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[190]}
2025-05-19 20:38:46.805 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [190]
2025-05-19 20:38:46.811 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=453, judgment=397, multipleChoice=7}, total available topics: 857
2025-05-19 20:38:50.768 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 20:38:50.817 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 识记类  专项练习
2025-05-19 20:38:50.829 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='识记类  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=190, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 20:38:51.468 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 416. Requested global counts (for warning reference): {SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 20:38:51.468 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 20:38:51.469 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 20:38:51.469 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 20:38:51.469 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 20:38:51.469 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 20:38:51.469 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 20:38:51.517 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=232, judgment=179, multipleChoice=5}
2025-05-19 20:38:51.517 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=5, judgment=5, multipleChoice=3, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 20:38:51.727 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题: 请求5/实际232
- 判断题: 请求5/实际179
- 多选题: 请求3/实际5
- FILL_IN_BLANKS: 请求3/实际0 [警告: 此题型在题库中完全不存在!]
- 简答题: 请求2/实际0 [警告: 此题型在题库中完全不存在!]
2025-05-19 20:38:51.763 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'singleChoice' (requested 5)
2025-05-19 20:38:51.763 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 3 topics of type 'multipleChoice' (requested 3)
2025-05-19 20:38:51.763 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 5 topics of type 'judgment' (requested 5)
2025-05-19 20:38:51.765 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 416. Final result size: 13. Actual counts per type after enforcement: {singleChoice=5, judgment=5, multipleChoice=3}
2025-05-19 20:38:51.766 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 53) after enforcing type counts...
2025-05-19 20:38:51.768 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 13 topics, actual score: 39 (target score: 53)
2025-05-19 20:38:51.820 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 41. Title: 识记类  专项练习
2025-05-19 20:38:51.899 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 13 topics.
2025-05-19 20:38:53.153 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/41, Token: exists
2025-05-19 20:38:53.154 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 41
2025-05-19 20:38:53.167 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 13 topics for paper id: 41
2025-05-19 20:38:53.174 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 13 topics in database (from 13 requested IDs) for paper id: 41
2025-05-19 20:38:53.185 [http-nio-8081-exec-8] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 13 ordered topics for paper id: 41
2025-05-19 20:39:04.608 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 20:39:06.935 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 20:39:08.732 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 20:39:08.734 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[190]}
2025-05-19 20:39:08.734 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [190]
2025-05-19 20:39:08.738 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=453, judgment=397, multipleChoice=7}, total available topics: 857
2025-05-19 20:44:21.385 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/availability, Token: exists
2025-05-19 20:44:21.389 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic availability API called with request: {knowledgePointIds=[190]}
2025-05-19 20:44:21.389 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Fetching topic type availability for 1 knowledge points: [190]
2025-05-19 20:44:21.393 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Topic type availability result: {singleChoice=453, judgment=397, multipleChoice=7}, total available topics: 857
2025-05-19 20:57:54.569 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 20:58:50.789 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 20:58:54.137 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:58:54.270 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:58:54.309 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:58:54.491 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 20:58:55.249 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 20:58:55.901 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 21:15:11.176 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 21:15:11.176 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@30d2f9ea]]
2025-05-19 21:15:11.176 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 21:15:12.165 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 21:15:12.168 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 21:15:12.175 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 21:19:04.567 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 17584 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-19 21:19:04.574 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 21:19:06.975 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 21:19:06.983 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 21:19:08.421 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1402 ms. Found 4 JPA repository interfaces.
2025-05-19 21:19:08.450 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 21:19:08.453 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 21:19:08.515 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 21:19:08.517 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 21:19:08.518 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 21:19:08.520 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 21:19:08.521 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 Redis repository interfaces.
2025-05-19 21:19:11.237 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 21:19:11.260 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 21:19:11.261 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 21:19:11.261 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 21:19:11.592 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 21:19:11.593 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6869 ms
2025-05-19 21:19:12.880 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 21:19:13.310 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 21:19:15.103 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 21:19:15.127 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 21:19:15.127 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 21:19:15.127 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 21:19:17.654 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-19 21:19:17.962 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 21:19:19.414 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 21:19:20.525 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 21:19:20.554 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-19 21:19:21.314 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 21:19:21.399 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 21:19:21.427 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 21:19:21.429 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 21:19:21.431 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@498f1f63]]
2025-05-19 21:19:21.431 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 21:19:21.447 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 18.503 seconds (JVM running for 19.876)
2025-05-19 21:19:21.527 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表创建成功
2025-05-19 21:19:21.530 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: []
2025-05-19 21:19:21.641 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 51
2025-05-19 21:19:26.077 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 21:19:26.077 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 21:19:26.079 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-19 21:19:27.324 [http-nio-8081-exec-3] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYzMDI1NCwiZXhwIjoxNzQ3NzE2NjU0fQ.2BA94j89S-a3YFMgYcklox1Z1cxyPzWTe_sufoe8zvZBYagI7KhdGmBtiXPnrLqMh_-DzOFnmH_kw5eLQFbHKQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 21:19:27.324 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:19:27.327 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:19:27.327 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:19:27.508 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:19:27.509 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:19:27.519 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:19:27.519 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:19:27.633 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:19:27.664 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:19:27.965 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:19:28.040 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:19:28.064 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:19:28.069 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:19:28.070 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:19:28.103 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:29:53.158 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:29:53.164 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:29:53.170 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:29:53.178 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:29:53.202 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:29:53.203 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:29:53.206 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:29:53.207 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:29:53.215 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:29:53.224 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:29:53.235 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:29:53.239 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:29:53.239 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:29:53.258 [http-nio-8081-exec-7] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:29:53.652 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:30:25.775 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:30:25.781 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:30:25.787 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:30:25.794 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:30:25.836 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:30:25.837 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:30:25.841 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:30:25.841 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:30:25.854 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:30:25.859 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:30:25.867 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:30:25.870 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:30:25.870 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:30:25.897 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:30:26.395 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:30:42.660 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:30:42.663 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:30:42.669 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:30:42.676 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:30:42.700 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:30:42.703 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:30:42.704 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:30:42.705 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:30:42.719 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:30:42.727 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:30:42.734 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:30:42.738 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:30:42.739 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:30:42.757 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:30:43.145 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:30:47.365 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:30:47.654 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 21:30:47.661 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=, filter=all, page=1, limit=30
2025-05-19 21:31:05.137 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:31:05.144 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:31:05.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:31:05.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:31:05.201 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:31:05.203 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:31:05.206 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:31:05.206 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:31:05.224 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:31:05.233 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:31:05.240 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:31:05.245 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:31:05.245 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:31:05.262 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:31:05.777 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:31:15.081 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:31:15.096 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:31:30.892 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:31:30.902 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:31:38.226 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/50, Token: exists
2025-05-19 21:31:38.236 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/50, Token: exists
2025-05-19 21:43:20.800 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:43:20.805 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:43:20.811 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:43:20.819 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:43:20.909 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:43:20.913 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=1
2025-05-19 21:43:20.913 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=1
2025-05-19 21:43:20.926 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=47, current=1, size=1, records=1
2025-05-19 21:43:20.926 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:43:20.927 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:43:20.932 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:43:20.932 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:43:20.935 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:43:20.947 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:43:20.953 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:43:20.959 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:43:20.959 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:43:20.976 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:43:21.572 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:43:31.744 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:43:32.002 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 21:43:32.005 [http-nio-8081-exec-4] INFO  c.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=, filter=all, page=1, limit=30
2025-05-19 21:43:32.237 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:43:32.499 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 21:43:32.503 [http-nio-8081-exec-7] INFO  c.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=, filter=all, page=1, limit=30
2025-05-19 21:43:54.400 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:43:54.401 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:43:54.402 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:43:54.402 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:43:54.407 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:43:54.407 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=1
2025-05-19 21:43:54.407 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=1
2025-05-19 21:43:54.407 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:43:54.407 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:43:54.412 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:43:54.413 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:43:54.423 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:43:54.423 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=47, current=1, size=1, records=1
2025-05-19 21:43:54.427 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:43:54.433 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:43:54.437 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:43:54.437 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:43:54.453 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:43:54.738 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:44:27.816 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:44:27.826 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:44:29.618 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/undefined, Token: exists
2025-05-19 21:44:43.217 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:44:43.563 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-19 21:44:43.565 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=, filter=all, page=1, limit=30
2025-05-19 21:45:46.060 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:45:46.073 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:45:47.413 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/undefined, Token: exists
2025-05-19 21:52:35.781 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:52:35.786 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:52:35.790 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:52:35.796 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:52:35.829 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:52:35.832 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:52:35.834 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:52:35.834 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=1
2025-05-19 21:52:35.835 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=1
2025-05-19 21:52:35.836 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:52:35.836 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:52:35.847 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=47, current=1, size=1, records=1
2025-05-19 21:52:35.849 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:52:35.852 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:52:35.857 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:52:35.861 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:52:35.862 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:52:35.874 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:52:36.152 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:52:38.583 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:52:38.591 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:52:39.621 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/undefined, Token: exists
2025-05-19 21:52:44.331 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:52:44.334 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:52:44.334 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:52:44.348 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:54:42.545 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 21:54:42.551 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 21:54:42.554 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 21:54:42.560 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 21:54:42.576 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:54:42.577 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:54:42.578 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:54:42.578 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:54:42.584 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 21:54:42.590 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:54:42.593 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 21:54:42.596 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 21:54:42.596 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 21:54:42.608 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 21:54:42.839 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 21:54:44.707 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:54:44.714 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 21:54:45.757 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/undefined, Token: exists
2025-05-19 22:04:18.094 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 22:04:18.100 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 22:04:18.104 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 22:04:18.110 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 22:04:18.136 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:04:18.138 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 22:04:18.141 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 22:04:18.141 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 22:04:18.149 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 22:04:18.153 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 22:04:18.157 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:04:18.159 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 22:04:18.159 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 22:04:18.171 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 22:04:18.406 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 22:05:59.598 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 22:05:59.598 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@498f1f63]]
2025-05-19 22:05:59.599 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 22:06:00.925 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 22:06:00.929 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 22:06:00.944 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 22:06:14.381 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 17608 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-19 22:06:14.386 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 22:06:16.137 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 22:06:16.141 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 22:06:16.765 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 604 ms. Found 4 JPA repository interfaces.
2025-05-19 22:06:16.781 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 22:06:16.783 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 22:06:16.825 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 22:06:16.827 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 22:06:16.828 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 22:06:16.830 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 22:06:16.830 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-05-19 22:06:18.806 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 22:06:18.822 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 22:06:18.823 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 22:06:18.823 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 22:06:19.147 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 22:06:19.147 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4685 ms
2025-05-19 22:06:20.355 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 22:06:20.735 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 22:06:22.389 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 22:06:22.416 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 22:06:22.416 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 22:06:22.416 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 22:06:25.161 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-19 22:06:25.519 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 22:06:27.183 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 22:06:28.316 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 22:06:28.363 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-19 22:06:29.224 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 22:06:29.299 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 22:06:29.330 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 22:06:29.332 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 22:06:29.332 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@30d944d8]]
2025-05-19 22:06:29.332 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 22:06:29.346 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 15.722 seconds (JVM running for 16.775)
2025-05-19 22:06:29.368 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 22:06:29.371 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 22:06:29.371 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 22:06:29.371 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 22:06:29.371 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 22:06:29.372 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: tinyint
2025-05-19 22:06:29.373 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 22:06:29.373 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 22:06:29.375 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: []
2025-05-19 22:06:29.484 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 51
2025-05-19 22:06:44.909 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYzMDI1NCwiZXhwIjoxNzQ3NzE2NjU0fQ.2BA94j89S-a3YFMgYcklox1Z1cxyPzWTe_sufoe8zvZBYagI7KhdGmBtiXPnrLqMh_-DzOFnmH_kw5eLQFbHKQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 22:06:44.921 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 22:06:44.921 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 22:06:44.922 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-19 22:06:45.813 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 22:06:45.992 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 22:06:46.000 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 22:06:46.136 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 22:06:46.257 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 22:06:46.264 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 22:06:46.273 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 22:06:46.286 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 22:06:46.310 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/history, Token: exists
2025-05-19 22:06:46.320 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 22:06:46.325 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 22:06:46.566 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/32, Token: exists
2025-05-19 22:06:46.658 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/messages/32, Token: exists
2025-05-19 22:06:47.838 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 22:06:47.841 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 22:06:47.845 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 22:06:47.850 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 22:06:47.868 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:06:47.869 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 22:06:47.883 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 22:06:47.895 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 22:06:47.900 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 22:06:48.066 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 22:06:48.067 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 22:06:48.096 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:06:48.102 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 22:06:48.102 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 22:06:48.133 [http-nio-8081-exec-5] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 22:06:50.093 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 22:06:50.103 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/51, Token: exists
2025-05-19 22:07:14.471 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:07:14.474 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=1, size=10
2025-05-19 22:07:14.474 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=1, size=10
2025-05-19 22:07:14.488 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=2, size=10, records=10
2025-05-19 22:07:14.997 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:07:15.000 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=3, size=10
2025-05-19 22:07:15.000 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=3, size=10
2025-05-19 22:07:15.017 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=4, size=10, records=10
2025-05-19 22:07:15.546 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 22:07:15.549 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 22:07:15.549 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 22:07:15.568 [http-nio-8081-exec-6] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=47, pages=5, current=1, size=10, records=10
2025-05-19 22:11:24.228 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 22:11:24.228 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@30d944d8]]
2025-05-19 22:11:24.229 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 22:11:25.111 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 22:11:25.114 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 22:11:25.132 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
