<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <!-- 自由组卷模态框 -->
<div th:fragment="modal" class="modal fade" id="customPaperModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 95%;">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-puzzle-piece mr-2"></i>自由组卷
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- 左侧配置区域 -->
                    <div class="col-lg-7">
                        <div class="configuration-panel">
                <form id="customPaperForm">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customPaperTitle">试卷标题</label>
                                <input type="text" class="form-control" id="customPaperTitle" required placeholder="例如：高一数学期末测试">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customPaperType">试卷版本</label>
                                <select class="form-control" id="customPaperType">
                                    <option value="regular" selected>学生版 - 只有题目（适合考试）</option>
                                    <option value="teacher">教师版 - 只有答案和解析（适合批改）</option>
                                    <option value="standard">标准版 - 题目+答案+解析（适合学习）</option>
                                </select>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="customPaperTypeDescription">学生版只包含题目，适合考试使用</span>
                                    <a href="#" class="ml-2" onclick="$('#versionHelpModal').modal('show'); return false;">
                                        <i class="fas fa-question-circle"></i> 版本说明
                                    </a>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- 难度分布设置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie mr-2"></i>难度分布
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="easyPercentage">简单题比例 (%)</label>
                                        <input type="number" class="form-control difficulty-input" id="easyPercentage" min="0" max="100" value="30">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="mediumPercentage">中等题比例 (%)</label>
                                        <input type="number" class="form-control difficulty-input" id="mediumPercentage" min="0" max="100" value="50">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="hardPercentage">困难题比例 (%)</label>
                                        <input type="number" class="form-control difficulty-input" id="hardPercentage" min="0" max="100" value="20">
                                    </div>
                                </div>
                            </div>
                            <div class="progress mt-2" style="height: 30px;">
                                <div class="progress-bar bg-success" id="easyProgressBar" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">简单 30%</div>
                                <div class="progress-bar bg-warning" id="mediumProgressBar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">中等 50%</div>
                                <div class="progress-bar bg-danger" id="hardProgressBar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">困难 20%</div>
                            </div>
                            <div id="difficultyError" class="text-danger mt-2" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- 题型配置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-list-ol mr-2"></i>题型配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>题型</th>
                                            <th width="150">数量</th>
                                            <th width="150">每题分值</th>
                                            <th width="150">总分</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>单选题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customSingleChoiceCount" min="0" value="10">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customSingleChoiceScore" min="0" value="3">
                                            </td>
                                            <td class="type-total-score">30</td>
                                        </tr>
                                        <tr>
                                            <td>多选题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customMultipleChoiceCount" min="0" value="5">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customMultipleChoiceScore" min="0" value="4">
                                            </td>
                                            <td class="type-total-score">20</td>
                                        </tr>
                                        <tr>
                                            <td>判断题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customJudgmentCount" min="0" value="5">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customJudgmentScore" min="0" value="2">
                                            </td>
                                            <td class="type-total-score">10</td>
                                        </tr>
                                        <tr>
                                            <td>填空题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customFillCount" min="0" value="3">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customFillScore" min="0" value="3">
                                            </td>
                                            <td class="type-total-score">9</td>
                                        </tr>
                                        <tr>
                                            <td>简答题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customShortAnswerCount" min="0" value="2">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customShortAnswerScore" min="0" value="10">
                                            </td>
                                            <td class="type-total-score">20</td>
                                        </tr>
                                        <tr>
                                            <td>主观题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customSubjectiveCount" min="0" value="1">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customSubjectiveScore" min="0" value="15">
                                            </td>
                                            <td class="type-total-score">15</td>
                                        </tr>
                                    </tbody>
                                    <tfoot class="bg-light">
                                        <tr>
                                            <th colspan="3" class="text-right">总分：</th>
                                            <th id="customTotalScore">104</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 知识点配置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-brain mr-2"></i>知识点配置
                            </h5>
                            <button type="button" class="btn btn-primary btn-sm" id="addKnowledgePointBtn">
                                <i class="fas fa-plus mr-1"></i>添加知识点
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                <strong>配置说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>为每个知识点设置基础题量，系统会根据上方题型设置智能分配</li>
                                    <li>可以为每个知识点单独开启简答题，并设置精确数量</li>
                                    <li><strong>简答题数量不计入知识点总体量，是额外增加的</strong></li>
                                </ul>
                            </div>

                            <!-- 知识点列表容器 -->
                            <div id="knowledgePointsContainer" class="mb-3">
                                <!-- 知识点项将通过JavaScript动态添加 -->
                                <div class="text-center text-muted py-5" id="noKnowledgePointsMessage">
                                    <i class="fas fa-brain fa-3x mb-3"></i>
                                    <p>暂无知识点，请点击"添加知识点"按钮添加</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                        </div>
                    </div>

                    <!-- 右侧实时预览区域 -->
                    <div class="col-lg-5">
                        <div class="preview-panel">
                            <div id="realTimePreviewContainer">
                                <!-- 实时预览内容将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="generateCustomPaperBtn">
                    <i class="fas fa-magic mr-1"></i>生成试卷
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 知识点选择模态框 -->
<div class="modal fade" id="knowledgePointSelectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-brain mr-2"></i>选择知识点
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- 增强的搜索区域 -->
                <div class="search-section mb-4">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-search text-primary"></i>
                                    </span>
                                </div>
                                <input type="text" class="form-control" id="knowledgePointSearchInput"
                                       placeholder="输入知识点名称进行搜索..." autocomplete="off">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="clearSearchCustom"
                                            style="display: none;" title="清空搜索">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-info btn-sm" id="showSearchTips">
                                    <i class="fas fa-question-circle mr-1"></i>搜索帮助
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" id="selectAllVisible">
                                    <i class="fas fa-check-double mr-1"></i>全选可见
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索提示和统计信息 -->
                    <div class="search-info mt-3">
                        <!-- 搜索提示 -->
                        <div class="alert alert-info py-2" id="searchTipsAlert" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-lightbulb mr-2"></i>
                                <div>
                                    <strong>搜索技巧：</strong>
                                    <ul class="mb-0 mt-1">
                                        <li>支持知识点名称的模糊匹配</li>
                                        <li>可以搜索知识点分类名称</li>
                                        <li>支持拼音首字母搜索（如：sx 搜索数学）</li>
                                        <li>使用空格分隔多个关键词进行组合搜索</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索结果统计 -->
                        <div class="search-stats" id="searchStats" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-info">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    找到 <span id="searchResultCountCustom">0</span> 个匹配的知识点
                                </small>
                                <small class="text-muted">
                                    已选择 <span id="selectedCountInSearch">0</span> 个
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="knowledge-point-list">
                    <div class="text-center py-4" id="loadingKnowledgePoints">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载知识点...</p>
                    </div>
                    <div id="knowledgePointsList" class="list-group">
                        <!-- 知识点将通过JavaScript动态加载 -->
                    </div>
                    <!-- 搜索无结果提示 -->
                    <div class="text-center py-5 text-muted" id="noSearchResultsCustom" style="display: none;">
                        <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                        <h5 class="text-muted">未找到匹配的知识点</h5>
                        <p class="mb-3">请尝试以下操作：</p>
                        <ul class="list-unstyled text-left d-inline-block">
                            <li><i class="fas fa-check text-success mr-2"></i>检查关键词拼写</li>
                            <li><i class="fas fa-check text-success mr-2"></i>尝试更简短的关键词</li>
                            <li><i class="fas fa-check text-success mr-2"></i>使用知识点分类名称搜索</li>
                            <li><i class="fas fa-check text-success mr-2"></i>清空搜索条件查看所有知识点</li>
                        </ul>
                        <button class="btn btn-outline-primary btn-sm mt-2" onclick="$('#knowledgePointSearchInput').val('').trigger('input')">
                            <i class="fas fa-redo mr-1"></i>清空搜索
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="confirmKnowledgePointSelection">
                    <i class="fas fa-check mr-1"></i>确认选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 知识点项模板 -->
<template id="knowledgePointItemTemplate">
    <div class="card mb-3 knowledge-point-item" data-id="{id}">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="card-title mb-0">{name}</h5>
                <button type="button" class="btn btn-sm btn-danger remove-knowledge-point">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
            <div class="row">
                <!-- 基础题量配置 -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>基础题量</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <button class="btn btn-outline-secondary decrease-count" type="button">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                            <input type="number" class="form-control knowledge-question-count text-center" min="0" value="5">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary increase-count" type="button">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <small class="text-muted">系统会根据题型设置智能分配</small>
                    </div>
                </div>

                <!-- 简答题配置 -->
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark py-2">
                            <h6 class="mb-0">
                                <i class="fas fa-edit"></i> 简答题（独立）
                            </h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="custom-control custom-switch mb-2">
                                <input type="checkbox" class="custom-control-input include-short-answer" id="includeShortAnswer{id}">
                                <label class="custom-control-label" for="includeShortAnswer{id}">启用简答题</label>
                            </div>
                            <div class="short-answer-config" id="shortAnswerConfig{id}" style="display: none;">
                                <label class="form-label">数量（额外）</label>
                                <div class="input-group input-group-sm">
                                    <div class="input-group-prepend">
                                        <button class="btn btn-outline-secondary decrease-short-answer" type="button">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                    <input type="number" class="form-control short-answer-count text-center" min="0" value="1">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary increase-short-answer" type="button">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    不计入基础题量
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 知识点总计显示 -->
            <div class="border-top pt-3 mt-3">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="font-weight-bold">该知识点总计：</span>
                    <span class="badge badge-primary badge-lg knowledge-point-total">5</span>
                </div>
            </div>
        </div>
    </div>
</template>
</body>
</html>
