<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 API测试页面</h1>

    <div class="test-section">
        <h3>认证测试</h3>
        <button class="test-button" onclick="testAuth()">测试当前用户认证</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>配置管理API测试</h3>
        <button class="test-button" onclick="testConfigStatistics()">测试统计信息</button>
        <button class="test-button" onclick="testConfigList()">测试配置列表</button>
        <button class="test-button" onclick="testConfigListWithPublic()">测试配置列表(含公共)</button>
        <div id="config-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>调试信息</h3>
        <button class="test-button" onclick="showDebugInfo()">显示调试信息</button>
        <div id="debug-result" class="result"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 测试认证
        function testAuth() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.textContent = '测试中...';

            $.ajax({
                url: '/api/user/current',
                method: 'GET',
                beforeSend: function(xhr) {
                    const token = localStorage.getItem('token');
                    if (token) {
                        xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                    }
                },
                success: function(response) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 认证成功:\n' + JSON.stringify(response, null, 2);
                },
                error: function(xhr) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 认证失败:\n' +
                        'Status: ' + xhr.status + '\n' +
                        'Response: ' + xhr.responseText;
                }
            });
        }

        // 测试配置统计
        function testConfigStatistics() {
            const resultDiv = document.getElementById('config-result');
            resultDiv.textContent = '测试统计信息中...';

            $.ajax({
                url: '/api/paper-configs/statistics',
                method: 'GET',
                beforeSend: function(xhr) {
                    const token = localStorage.getItem('token');
                    if (token) {
                        xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                    }
                },
                success: function(response) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 统计信息获取成功:\n' + JSON.stringify(response, null, 2);
                },
                error: function(xhr) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 统计信息获取失败:\n' +
                        'Status: ' + xhr.status + '\n' +
                        'Response: ' + xhr.responseText;
                }
            });
        }

        // 测试配置列表
        function testConfigList() {
            const resultDiv = document.getElementById('config-result');
            resultDiv.textContent = '测试配置列表中...';

            $.ajax({
                url: '/api/paper-configs',
                method: 'GET',
                beforeSend: function(xhr) {
                    const token = localStorage.getItem('token');
                    if (token) {
                        xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                    }
                },
                success: function(response) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 配置列表获取成功:\n' + JSON.stringify(response, null, 2);
                },
                error: function(xhr) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 配置列表获取失败:\n' +
                        'Status: ' + xhr.status + '\n' +
                        'Response: ' + xhr.responseText;
                }
            });
        }

        // 测试配置列表(含公共)
        function testConfigListWithPublic() {
            const resultDiv = document.getElementById('config-result');
            resultDiv.textContent = '测试配置列表(含公共)中...';

            $.ajax({
                url: '/api/paper-configs?includePublic=true',
                method: 'GET',
                beforeSend: function(xhr) {
                    const token = localStorage.getItem('token');
                    if (token) {
                        xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                    }
                },
                success: function(response) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 配置列表(含公共)获取成功:\n' + JSON.stringify(response, null, 2);
                },
                error: function(xhr) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 配置列表(含公共)获取失败:\n' +
                        'Status: ' + xhr.status + '\n' +
                        'Response: ' + xhr.responseText;
                }
            });
        }

        // 显示调试信息
        function showDebugInfo() {
            const resultDiv = document.getElementById('debug-result');
            const token = localStorage.getItem('token');
            const currentUser = localStorage.getItem('currentUser');

            const debugInfo = {
                token: token ? 'Token存在 (长度: ' + token.length + ')' : '无Token',
                tokenPreview: token ? token.substring(0, 20) + '...' : '无',
                currentUser: currentUser ? JSON.parse(currentUser) : '无用户信息',
                currentUrl: window.location.href,
                userAgent: navigator.userAgent
            };

            resultDiv.className = 'result';
            resultDiv.textContent = '🔍 调试信息:\n' + JSON.stringify(debugInfo, null, 2);
        }

        // 页面加载时显示基本信息
        window.onload = function() {
            showDebugInfo();
        };
    </script>
</body>
</html>
