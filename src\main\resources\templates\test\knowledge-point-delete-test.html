<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识点删除功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/paper-generate.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .test-log {
            background-color: #343a40;
            color: #fff;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-bug text-danger"></i>
                知识点删除功能测试
            </h1>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
                <p>此页面用于测试知识点卡片的删除功能，验证事件委托和动画效果是否正常工作。</p>
                <ul>
                    <li>点击红色的 <i class="fas fa-times text-danger"></i> 按钮删除知识点</li>
                    <li>点击蓝色的 <i class="fas fa-magic text-primary"></i> 按钮测试单独出题功能</li>
                    <li>观察控制台日志和动画效果</li>
                </ul>
            </div>

            <!-- 测试区域 -->
            <div class="test-section">
                <h3><i class="fas fa-flask"></i> 测试知识点卡片</h3>
                <p class="text-muted">以下是模拟的知识点卡片，用于测试删除功能：</p>
                
                <div id="test-knowledge-points">
                    <!-- 测试卡片将在这里动态生成 -->
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-success" onclick="addTestCard()">
                        <i class="fas fa-plus"></i> 添加测试卡片
                    </button>
                    <button class="btn btn-warning" onclick="clearLog()">
                        <i class="fas fa-eraser"></i> 清空日志
                    </button>
                </div>
            </div>

            <!-- 日志区域 -->
            <div class="test-section">
                <h3><i class="fas fa-terminal"></i> 测试日志</h3>
                <div id="test-log" class="test-log">
                    <div>测试日志初始化完成...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/js/common.js"></script>

    <script>
        // 全局变量，模拟已选择的知识点
        const selectedKnowledgePoints = new Map();
        let cardCounter = 1;

        // 日志函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[测试] ${message}`);
        }

        // 清空日志
        function clearLog() {
            document.getElementById('test-log').innerHTML = '<div>日志已清空...</div>';
        }

        // 添加测试卡片
        function addTestCard() {
            const id = cardCounter++;
            const knowId = 190 + id;
            const name = `测试知识点 ${id}`;
            const topicCount = Math.floor(Math.random() * 500) + 100;
            
            // 添加到全局状态
            selectedKnowledgePoints.set(id.toString(), {
                id: id.toString(),
                name: name,
                topicCount: `题目: ${topicCount} 题`,
                isFree: Math.random() > 0.5,
                knowId: knowId
            });

            // 生成卡片HTML
            const html = `
                <div class="card mb-3 selected-knowledge-point" data-id="${id}" data-name="${name}">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0 text-truncate" title="${name}" style="max-width: 80%;">${name}</h5>
                            <button type="button" class="remove-btn" data-id="${id}" title="移除此知识点">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mb-2">
                            <span class="badge badge-info"><i class="fas fa-list-ol mr-1"></i>题目: ${topicCount} 题</span>
                            <span class="badge ${Math.random() > 0.5 ? 'badge-success' : 'badge-warning'}">
                                <i class="fas fa-${Math.random() > 0.5 ? 'unlock' : 'lock'} mr-1"></i>
                                ${Math.random() > 0.5 ? '免费' : '付费'}
                            </span>
                            <span class="badge badge-primary"><i class="fas fa-hashtag mr-1"></i>ID: ${id}</span>
                            <span class="badge badge-secondary"><i class="fas fa-key mr-1"></i>知识点ID: ${knowId}</span>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary btn-block generate-single-btn" 
                                data-id="${id}" data-know-id="${knowId}" data-name="${name}">
                            <i class="fas fa-magic mr-1"></i>单独出题
                        </button>
                    </div>
                </div>
            `;

            $('#test-knowledge-points').append(html);
            log(`添加测试卡片: ${name} (ID: ${id}, knowId: ${knowId})`);
        }

        // 模拟updateSelectionCounter函数
        function updateSelectionCounter() {
            log(`更新选择计数器: 当前选中 ${selectedKnowledgePoints.size} 个知识点`);
        }

        // 模拟openPaperGenerationModal函数
        function openPaperGenerationModal(knowledgeConfig, title) {
            log(`打开试卷生成模态框: ${title}, 配置: ${JSON.stringify(knowledgeConfig)}`);
            Swal.fire({
                title: '模拟试卷生成',
                html: `<strong>标题:</strong> ${title}<br><strong>配置:</strong> ${JSON.stringify(knowledgeConfig, null, 2)}`,
                icon: 'info'
            });
        }

        $(document).ready(function() {
            log('页面加载完成，开始初始化测试环境...');

            // 添加几个初始测试卡片
            for (let i = 0; i < 3; i++) {
                addTestCard();
            }

            // 使用事件委托绑定移除按钮事件（与修复后的代码一致）
            $(document).off('click', '.selected-knowledge-point .remove-btn').on('click', '.selected-knowledge-point .remove-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const knowledgeId = $(this).data('id');
                const knowledgeName = $(this).closest('.selected-knowledge-point').data('name') || '未知知识点';
                
                log(`点击删除按钮: knowledgeId=${knowledgeId}, name=${knowledgeName}`);
                
                // 显示确认对话框
                Swal.fire({
                    title: '移除知识点',
                    html: `确定要移除知识点 <strong>${escapeHtml(knowledgeName)}</strong> 吗？`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '移除',
                    cancelButtonText: '取消',
                    confirmButtonColor: '#dc3545'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 从全局状态中移除
                        if (selectedKnowledgePoints.has(knowledgeId.toString())) {
                            selectedKnowledgePoints.delete(knowledgeId.toString());
                            log(`从全局状态删除知识点: ${knowledgeId}`);
                        }

                        // 添加移除动画
                        const $knowledgePoint = $(this).closest('.selected-knowledge-point');
                        $knowledgePoint.addClass('fade-out');
                        
                        setTimeout(() => {
                            $knowledgePoint.remove();
                            updateSelectionCounter();
                            
                            // 显示成功提示
                            Swal.fire({
                                title: '已移除',
                                text: `知识点 ${knowledgeName} 已成功移除`,
                                icon: 'success',
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 2000
                            });
                            
                            log(`知识点删除完成: ${knowledgeName}`);
                        }, 300);
                    } else {
                        log(`用户取消删除: ${knowledgeName}`);
                    }
                });
            });

            // 使用事件委托绑定单独出题按钮事件（与修复后的代码一致）
            $(document).off('click', '.selected-knowledge-point .generate-single-btn').on('click', '.selected-knowledge-point .generate-single-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const knowledgeId = $(this).data('id');
                const knowId = $(this).data('know-id') || knowledgeId;
                const knowledgeName = $(this).data('name');

                log(`点击单独出题按钮: knowledgeId=${knowledgeId}, knowId=${knowId}, name=${knowledgeName}`);

                // 创建单个知识点配置
                const knowledgeConfig = [{
                    knowledgeId: knowId,
                    questionCount: 10,
                    includeShortAnswer: true
                }];

                // 打开试卷生成模态框
                openPaperGenerationModal(knowledgeConfig, knowledgeName + ' 专项练习');
            });

            log('事件绑定完成，测试环境准备就绪！');
        });
    </script>
</body>
</html>
