package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.PaperGenerationRequest;
import java.util.HashMap;
import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 试卷生成引擎核心类。
 * 负责协调整个试卷生成过程，包括从题库获取候选题目、应用各种过滤器、
 * 使用遗传算法进行题目选择和优化，以及通过动态规划进行总分微调。
 * 本引擎旨在根据用户定义的复杂约束（知识点、难度、题型、分数、重用策略等）
 * 智能地组建一份高质量的试卷。
 */
@Service
@Slf4j
public class PaperGenerationEngine {
    private final TopicMapper topicMapper; // 数据库题目访问接口
    // private final TopicCacheManager cacheManager; // Removed as it's no longer used
    private final DiversityFilter diversityFilter; // 题目多样性过滤器，确保试卷内容丰富性
    private final DPAdjuster dpAdjuster; // 动态规划调整器，用于精确匹配试卷总分
    private final GeneticSolver geneticSolver; // 遗传算法求解器，核心的题目选择与优化模块
    private final TopicEnhancementDataMapper enhancementDataMapper; // 题目增强数据访问接口 (如使用次数、认知层次)
    private final PreciseTopicAllocator preciseTopicAllocator; // 精确题型分配器

    /**
     * PaperGenerationEngine 构造函数。
     * 通过依赖注入初始化所有必要的服务组件。
     *
     * @param topicMapper             用于访问和操作题目数据。
     * @param diversityFilter         用于对候选题目进行多样性筛选。
     * @param dpAdjuster              用于通过动态规划精确调整试卷总分。
     * @param geneticSolver           用于执行遗传算法，选取最优题目组合。
     * @param enhancementDataMapper   用于访问题目的附加增强信息。
     */
    public PaperGenerationEngine(TopicMapper topicMapper,
                                 // TopicCacheManager cacheManager, // Removed from constructor
                                 DiversityFilter diversityFilter,
                                 DPAdjuster dpAdjuster,
                                 GeneticSolver geneticSolver,
                                 TopicEnhancementDataMapper enhancementDataMapper,
                                 PreciseTopicAllocator preciseTopicAllocator) {
        this.topicMapper = topicMapper;
        // this.cacheManager = cacheManager; // Removed assignment
        this.diversityFilter = diversityFilter;
        this.dpAdjuster = dpAdjuster;
        this.geneticSolver = geneticSolver;
        this.enhancementDataMapper = enhancementDataMapper;
        this.preciseTopicAllocator = preciseTopicAllocator;
        log.info("PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator.");
    }

    /**
     * 智能组卷核心方法。
     * 根据前端传入的 {@link PaperGenerationRequest} 对象中定义的各项参数和约束，
     * 执行一系列复杂的算法步骤（包括题目筛选、缓存利用、过滤、遗传算法寻优、动态规划调整等），
     * 最终生成一份符合要求的题目列表作为试卷内容。
     *
     * 新增功能：当某题型题目不足时，会动态调整其他题型的数量，确保总题目数和总分数符合要求。
     *
     * @param request 包含试卷生成所有配置参数的请求对象。关键参数包括：
     *                <ul>
     *                  <li>{@code knowledgeIds}: 知识点ID列表，定义试卷的考察范围。</li>
     *                  <li>{@code totalScore}: 试卷的目标总分。</li>
     *                  <li>{@code typeScoreMap}: 题型到对应分值的映射，例如：{"singleChoice": 2, "multipleChoice": 4}。</li>
     *                  <li>{@code difficultyCriteria}: 难度分布要求，例如：{"easy": 0.3, "medium": 0.5, "hard": 0.2}，表示各难度题目所占分数比例。</li>
     *                  <li>{@code topicTypeCounts}: 各题型的目标数量，例如：{"singleChoice": 10, "judgment": 5}。</li>
     *                  <li>{@code cognitiveLevelCriteria}: (可选) 认知层次分布要求，如布鲁姆认知层次。</li>
     *                  <li>{@code minReuseIntervalDays}: (可选) 题目最小重用间隔天数，用于控制题目曝光频率。</li>
     *                </ul>
     * @return 返回一个 {@link Topic} 对象的列表，代表最终选出的试卷题目。
     *         如果无法根据请求参数生成满足条件的试卷（例如题库题目不足），则可能返回空列表。
     */
    public List<Topic> generatePaper(PaperGenerationRequest request) {
        log.info("Starting paper generation process with detailed request: {}", request);

        int targetTotalScore = request.getTotalScore();
        Map<String, Integer> typeTargetScores = request.getTypeScoreMap(); // Global score per type
        Map<String, Double> difficultyDistribution = request.getDifficultyCriteria();
        Map<String, Double> cognitiveLevelDistribution = request.getCognitiveLevelCriteria();
        Integer minReuseIntervalDays = request.getMinReuseIntervalDays();

        // --- Step 1: Process KnowledgePointConfigs to gather initial topic pools ---
        List<Topic> generalPoolCandidateTopics = new ArrayList<>();
        Map<String, Integer> remainingGlobalTypeCounts = request.getTopicTypeCounts() != null
            ? new HashMap<>(request.getTopicTypeCounts())
            : new HashMap<>();

        // 将前端格式的题型要求转换为数据库格式，供整个方法使用
        final Map<String, Integer> dbFormatTypeCounts = new HashMap<>();
        for (Map.Entry<String, Integer> entry : remainingGlobalTypeCounts.entrySet()) {
            String frontendType = entry.getKey();  // 如 "JUDGE"
            String dbType = mapTopicType(frontendType);  // 转换为 "judge"
            dbFormatTypeCounts.put(dbType, entry.getValue());
        }

        //  关键修复：计算并添加简答题数量到全局题型计数中
        if (request.getKnowledgePointConfigs() != null) {
            int totalShortAnswerCount = 0;
            for (KnowledgePointConfigRequest kpConfig : request.getKnowledgePointConfigs()) {
                if (kpConfig.hasShortAnswerConfiguration()) {
                    totalShortAnswerCount += kpConfig.getShortAnswerCount();
                    log.info("Knowledge point {} contributes {} short answer questions",
                            kpConfig.getKnowledgeId(), kpConfig.getShortAnswerCount());
                }
            }

            if (totalShortAnswerCount > 0) {
                dbFormatTypeCounts.put("short", totalShortAnswerCount);
                remainingGlobalTypeCounts.put("SHORT", totalShortAnswerCount);
                log.info("Added {} short answer questions to global type counts", totalShortAnswerCount);
            }
        }

        log.info("Converted type counts for entire process: frontend={} -> database={}",
                remainingGlobalTypeCounts, dbFormatTypeCounts);

        if (request.getKnowledgePointConfigs() == null || request.getKnowledgePointConfigs().isEmpty()) {
            log.warn("KnowledgePointConfigs is null or empty. Cannot select topics based on KPs.");
            // Depending on requirements, might try to select based on global types from all topics, or return empty.
            // For now, if no KPs are specified, we can't proceed with KP-based selection.
            // If global selection without KPs is desired, that's a different logic path.
        } else {
            for (KnowledgePointConfigRequest kpConfig : request.getKnowledgePointConfigs()) {
                if (kpConfig.getKnowledgeId() == null) {
                    log.warn("Skipping KnowledgePointConfig with null knowledgeId: {}", kpConfig);
                    continue;
                }
                List<Topic> topicsForThisKp = topicMapper.selectFromBakByKnowId(kpConfig.getKnowledgeId().intValue());
                if (topicsForThisKp == null || topicsForThisKp.isEmpty()) {
                    log.debug("No topics found for knowledgeId: {}", kpConfig.getKnowledgeId());
                    continue;
                }

                log.info("Knowledge point {} contributed {} questions", kpConfig.getKnowledgeId(), topicsForThisKp.size());

                // Filter by includeShortAnswer
                if (!kpConfig.getIncludeShortAnswer()) {
                    topicsForThisKp.removeIf(topic -> "short".equals(mapTopicType(topic.getType())));
                    log.debug("Filtered out short answer questions for knowledge point {} (includeShortAnswer=false)", kpConfig.getKnowledgeId());
                }

                // 修复：无论是否设置了questionCount，都将该知识点的所有题目加入候选池
                // 这样遗传算法就有足够的题目可以选择，能够满足题型要求
                generalPoolCandidateTopics.addAll(topicsForThisKp);
                log.debug("Added {} topics from KP {} (includeShortAnswer: {}) to general pool. QuestionCount setting: {}",
                      topicsForThisKp.size(), kpConfig.getKnowledgeId(), kpConfig.getIncludeShortAnswer(), kpConfig.getQuestionCount());
            }
        }

        // Consolidate and ensure uniqueness
        // 现在所有题目都在通用池中，确保去重
        List<Topic> allTopicsFromReferencedKps = generalPoolCandidateTopics.stream().distinct().collect(Collectors.toList());

        if (allTopicsFromReferencedKps.isEmpty() && (request.getKnowledgePointConfigs() != null && !request.getKnowledgePointConfigs().isEmpty())) {
            log.warn("No topics selected after processing KnowledgePointConfigs. Cannot proceed.");
            return Collections.emptyList();
        }

        // 检查是否需要扩展题目池以满足题型要求
        allTopicsFromReferencedKps = expandQuestionPoolIfNeeded(allTopicsFromReferencedKps, dbFormatTypeCounts);

        log.info("Final topic pool size after potential expansion: {}. Target global counts: {}",
            allTopicsFromReferencedKps.size(), remainingGlobalTypeCounts);

        Set<Integer> allTopicIdsFromReferencedKps = allTopicsFromReferencedKps.stream()
                                                    .map(Topic::getId)
                                                    .collect(Collectors.toSet());

        Map<Integer, TopicEnhancementData> enhancementDataMap;
        if (!allTopicIdsFromReferencedKps.isEmpty()) {
        try {
                List<TopicEnhancementData> enhancementList = enhancementDataMapper.selectBatchIds(new ArrayList<>(allTopicIdsFromReferencedKps));
            enhancementDataMap = enhancementList.stream()
                    .collect(Collectors.toMap(TopicEnhancementData::getTopicId, ted -> ted, (e, r) -> e)); // Handle duplicates if any
            log.debug("Retrieved enhancement data for {} topics.", enhancementDataMap.size());
        } catch (Exception e) {
            log.error("Failed to retrieve topic enhancement data: {}", e.getMessage());
                enhancementDataMap = Collections.emptyMap();
            }
        } else {
            enhancementDataMap = Collections.emptyMap();
        }
        final Map<Integer, TopicEnhancementData> finalEnhancementDataMap = enhancementDataMap;

        // The 'allTopicsFromReferencedKps' list is already correctly populated with candidates
        // from the initial processing of KnowledgePointConfigs (earmarked vs. general pool).
        // The previous complex loop for Step 2 has been removed.

        log.info("Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): {} topics. IDs: {}",
                 allTopicsFromReferencedKps.size(),
                 allTopicsFromReferencedKps.stream().map(Topic::getId).collect(Collectors.toList()));

        if (allTopicsFromReferencedKps.isEmpty() && (request.getKnowledgePointConfigs() != null && !request.getKnowledgePointConfigs().isEmpty())) {
            log.warn("Candidate topic pool (allTopicsFromReferencedKps) is empty before diversity filter. Request: {}", request);
            return Collections.emptyList();
        }

        // --- Step 3: Apply Type-Aware Smart Diversity Filter with Knowledge Point Level Control ---
        List<Topic> availableTopicsForGA = diversityFilter.smartFilterWithTypeAwareness(
                allTopicsFromReferencedKps, finalEnhancementDataMap, minReuseIntervalDays, dbFormatTypeCounts);
        log.info("Step 3: After Type-Aware Smart Diversity Filter (with KP-level control), {} topics available for GA (input size was {}). MinReuseIntervalDays: {}",
                 availableTopicsForGA.size(), allTopicsFromReferencedKps.size(), minReuseIntervalDays);

        // The 'allTopics' list is now the primary candidate pool.
        // Subsequent filters (diversity) and GA will operate on this 'allTopics' list.

        // --- Step 3: Apply Filters (Diversity) to the 'allTopics' pool ---
        List<Topic> candidatePool = availableTopicsForGA; // Start with the topics selected from KpConfigs

        // Smart diversity filter (already applied above, this is redundant but kept for safety)
        log.debug("Step 3: Applying additional diversity filter check...");
        try {
            // This is now redundant since we already applied smartFilter above, but kept for safety
            // candidatePool = diversityFilter.filter(candidatePool, finalEnhancementDataMap, minReuseIntervalDays);
            log.info("Diversity filtering already applied via smartFilter. {} topics remain.", candidatePool.size());
        } catch (Exception e) {
            log.error("Error during diversity filtering check: {}. Proceeding with current pool.", e.getMessage());
            // If diversity filter fails, 'candidatePool' remains as it was before the call.
        }

        if (candidatePool.isEmpty()) {
            log.warn("Candidate pool for Genetic Algorithm is empty after diversity filter. Cannot generate paper.");
            return Collections.emptyList();
        }

        // --- Step 4: Check for Precise Allocation Requirements ---
        log.info("Step 4a: Checking for precise allocation requirements...");

        // 检查是否有知识点配置了简答题
        boolean hasShortAnswerConfiguration = false;
        if (request.getKnowledgePointConfigs() != null) {
            log.info("Checking knowledge point configurations for short answer requirements...");
            for (KnowledgePointConfigRequest kpConfig : request.getKnowledgePointConfigs()) {
                log.info("KP {}: includeShortAnswer={}, shortAnswerCount={}, hasShortAnswerConfiguration={}",
                        kpConfig.getKnowledgeId(),
                        kpConfig.getIncludeShortAnswer(),
                        kpConfig.getShortAnswerCount(),
                        kpConfig.hasShortAnswerConfiguration());

                if (kpConfig.hasShortAnswerConfiguration()) {
                    hasShortAnswerConfiguration = true;
                }
            }
        }

        log.info("Overall hasShortAnswerConfiguration: {}", hasShortAnswerConfiguration);

        List<Topic> selectedTopics = Collections.emptyList();
        List<Topic> shortAnswerTopics = Collections.emptyList();

        //  简化版混合模式：分别处理基础题型和简答题
        if (hasShortAnswerConfiguration) {
            log.info("Found short answer configuration. Using hybrid approach: GA for basic types + Precise for short answers...");

            try {
                // 1. 先用精确分配器处理简答题
                PreciseTopicAllocator.AllocationResult shortAnswerResult =
                    preciseTopicAllocator.allocateTopics(candidatePool, request.getKnowledgePointConfigs());

                if (shortAnswerResult.isSuccess()) {
                    shortAnswerTopics = shortAnswerResult.getAllocatedTopics();
                    log.info("Short answer allocation successful: {} topics allocated. Messages: {}",
                            shortAnswerTopics.size(), shortAnswerResult.getMessages());

                    // 验证简答题分配结果
                    Map<String, Long> shortAnswerTypeCounts = shortAnswerTopics.stream()
                        .collect(Collectors.groupingBy(
                            topic -> mapTopicType(topic.getType()),
                            Collectors.counting()));

                    log.info("Short answer allocation type distribution: {}", shortAnswerTypeCounts);

                } else {
                    log.warn("Short answer allocation failed: {}. Will proceed with GA only.",
                            shortAnswerResult.getSummary());
                    shortAnswerTopics = Collections.emptyList();
                }

            } catch (Exception e) {
                log.error("Error during short answer allocation: {}. Will proceed with GA only.", e.getMessage(), e);
                shortAnswerTopics = Collections.emptyList();
            }
        }

        // --- Step 4b: Use Genetic Algorithm for basic types ---
        log.info("Step 4b: Executing genetic algorithm for basic types with {} candidate topics...", candidatePool.size());
        try {
            // Extract knowledge IDs for GA
            List<Integer> knowledgeIdsForGA = request.getKnowledgePointConfigs().stream()
                                                .filter(kc -> kc.getKnowledgeId() != null)
                                                .map(kc -> kc.getKnowledgeId().intValue())
                                                .distinct()
                                                .collect(Collectors.toList());

            //  关键修复：为遗传算法创建基础题型计数（排除简答题）
            Map<String, Integer> basicTypeCountsForGA = new HashMap<>(dbFormatTypeCounts);
            basicTypeCountsForGA.remove("short"); // 移除简答题，由精确分配器处理

            // 计算基础题型的目标总分（排除简答题分数）
            int shortAnswerScore = shortAnswerTopics.stream()
                .mapToInt(t -> request.getTypeScoreMap().getOrDefault("SHORT", 10))
                .sum();
            int basicTypeTargetScore = targetTotalScore - shortAnswerScore;

            log.info("GA will handle basic types only: {}", basicTypeCountsForGA);
            log.info("GA target score: {} (total {} - short answer {})",
                    basicTypeTargetScore, targetTotalScore, shortAnswerScore);

            List<Topic> basicTypeTopics = geneticSolver.solve(
                    candidatePool,               // List<Topic> availableQuestions
                    basicTypeTargetScore,        // int targetScore - 排除简答题分数
                    basicTypeCountsForGA,        // Map<String, Integer> typeTargetCounts - 排除简答题
                    difficultyDistribution,      // Map<String, Double> difficultyDistributionTarget
                    cognitiveLevelDistribution,  // Map<String, Double> cognitiveLevelDistributionTarget
                    finalEnhancementDataMap,     // Map<Integer, TopicEnhancementData> enhancementDataMap
                    knowledgeIdsForGA,           // List<Integer> targetKnowledgeIds
                    request.getKnowledgePointConfigs(),  // List<KnowledgePointConfigRequest> knowledgePointConfigs
                    request.getTypeScoreMap()    // Map<String, Integer> typeScoreMap
            );

            //  合并基础题型和简答题
            selectedTopics = new ArrayList<>();
            selectedTopics.addAll(basicTypeTopics);
            selectedTopics.addAll(shortAnswerTopics);

            log.info("Combined results: {} basic type topics + {} short answer topics = {} total topics",
                    basicTypeTopics.size(), shortAnswerTopics.size(), selectedTopics.size());

            // 记录遗传算法选择结果的题型分布 - 使用数据库格式进行统计
            Map<String, Long> gaTypeDistribution = selectedTopics.stream()
                .collect(Collectors.groupingBy(
                    topic -> mapTopicType(topic.getType()),  // 转换为数据库格式
                    Collectors.counting()));

            log.info("Combined algorithm selected {} topics with type distribution: {}",
                    selectedTopics.size(), gaTypeDistribution);

            // 验证结果是否满足题型要求
            boolean typesConstraintsMet = true;
            for (Map.Entry<String, Integer> entry : remainingGlobalTypeCounts.entrySet()) {
                String frontendType = entry.getKey();  // 前端格式 (如 JUDGE)
                String dbType = mapTopicType(frontendType);  // 转换为数据库格式 (如 judge)
                int required = entry.getValue();
                long actual = gaTypeDistribution.getOrDefault(dbType, 0L);  // 使用数据库格式查找

                if (required > 0 && actual != required) {
                    typesConstraintsMet = false;
                    log.warn("Combined result: Type constraint not met for {} ({}): required={}, actual={}",
                            frontendType, dbType, required, actual);
                }
            }

            if (typesConstraintsMet) {
                log.info("Combined result: All type constraints met!");
            } else {
                log.warn("Combined result: Type constraints not fully met. DP adjuster will try to preserve type distribution.");
            }

        } catch (Exception e) {
            log.error("Error during genetic algorithm execution: {}. Proceeding with empty list.", e.getMessage(), e);
            selectedTopics = Collections.emptyList(); // Ensure selectedTopics is assigned even on error
        }

        // 应用动态规划进一步优化总分精确匹配
        if (dpAdjuster != null && !selectedTopics.isEmpty()) {
            log.info("正在使用DP动态规划进行最终优化调整，目标分数: {}, 同时保持题型分布", targetTotalScore);

            try {
                // 在执行DP调整前，记录当前题型分布
                Map<String, Long> preAdjustmentTypeDistribution = selectedTopics.stream()
                    .collect(Collectors.groupingBy(
                        topic -> mapTopicType(topic.getType()),
                        Collectors.counting()));

                log.info("Pre-DP adjustment type distribution: {}", preAdjustmentTypeDistribution);

                // 记录开始时间，用于超时监控
                long dpStartTime = System.currentTimeMillis();
                final long DP_TIMEOUT_MS = 15000; // 15秒超时限制

                // 创建一个独立的线程来执行DP调整
                List<Topic> dpResult = new ArrayList<>();
                final List<Topic> topicsForDp = new ArrayList<>(selectedTopics); // 创建副本用于线程

                Thread dpThread = new Thread(() -> {
                    try {
                        // 传递数据库格式的题型目标数量，以便在DP调整中同时考虑分数和题型约束
                        dpResult.addAll(dpAdjuster.adjust(topicsForDp, targetTotalScore, typeTargetScores, dbFormatTypeCounts));
                    } catch (Exception e) {
                        log.error("Error in DP adjustment thread: {}", e.getMessage(), e);
                    }
                });

                // 设置线程名称，方便调试
                dpThread.setName("DP-Adjuster-Thread");

                // 启动DP调整线程
                dpThread.start();

                // 等待线程完成或超时
                try {
                    dpThread.join(DP_TIMEOUT_MS);

                    // 检查线程是否仍在运行（超时）
                    if (dpThread.isAlive()) {
                        log.warn("DP adjustment thread timed out after {} ms. Using pre-adjustment topics.", DP_TIMEOUT_MS);
                        // 中断线程
                        dpThread.interrupt();
                    } else if (!dpResult.isEmpty()) {
                        // 如果DP调整成功，使用调整后的结果

                        // 验证调整后的题型分布是否满足要求
                        Map<String, Long> postAdjustmentTypeDistribution = dpResult.stream()
                            .collect(Collectors.groupingBy(
                                topic -> mapTopicType(topic.getType()),
                                Collectors.counting()));

                        log.info("After DP adjustment: Type distribution = {}", postAdjustmentTypeDistribution);

                        // 检查是否所有题型都满足要求
                        boolean allTypesSatisfied = true;
                        for (Map.Entry<String, Integer> entry : remainingGlobalTypeCounts.entrySet()) {
                            String type = entry.getKey();
                            int required = entry.getValue();
                            long actual = postAdjustmentTypeDistribution.getOrDefault(type, 0L);

                            if (required > 0 && actual != required) {
                                allTypesSatisfied = false;
                                log.warn("After DP: Type constraint not met for {}: required={}, actual={}",
                                        type, required, actual);
                            }
                        }

                        // 只有在DP调整后仍然满足题型约束的情况下才使用调整结果
                        if (allTypesSatisfied) {
                            selectedTopics = new ArrayList<>(dpResult);

                            // 计算调整后的总分
                            int finalScore = selectedTopics.stream()
                                .mapToInt(t -> typeTargetScores.getOrDefault(mapTopicType(t.getType()),
                                        t.getScore() != null ? t.getScore() : 0))
                                .sum();
                            log.info("DP score adjustment complete. Final total score = {}, with type distribution preserved.", finalScore);
                        } else {
                            log.warn("DP adjustment broke type constraints. Using pre-adjustment topics to preserve type distribution.");
                            // 保留原始选择以维护题型分布
                        }

                        log.info("DP adjustment completed in {} ms",
                                 System.currentTimeMillis() - dpStartTime);
                    }
                } catch (InterruptedException e) {
                    log.error("Interrupted while waiting for DP thread: {}", e.getMessage());
                    Thread.currentThread().interrupt(); // 重置中断状态
                }
            } catch (Exception e) {
                log.error("Exception during DP adjustment: {}", e.getMessage(), e);
            }
        } else if (selectedTopics.isEmpty()) {
            log.warn("Selected topics list is empty, skipping DP adjustment.");
        } else {
            log.warn("DPAdjuster unavailable, skipping score precision optimization step.");
        }

        // 检查题目数量是否满足要求，若不足则尝试动态调整
        if (selectedTopics != null && !selectedTopics.isEmpty()) {
            try {
                Map<String, Integer> actualTopicCounts = countTopicsByType(selectedTopics);
                Map<String, Integer> targetTypeCounts = request.getTopicTypeCounts();

                if (targetTypeCounts != null && !targetTypeCounts.isEmpty()) {
                    log.info("检查各题型题目数量: 实际={}, 目标={}", actualTopicCounts, targetTypeCounts);

                    // 如果实际题目数量与目标不符，尝试调整
                    if (!areTopicCountsSatisfied(actualTopicCounts, targetTypeCounts)) {
                        log.info("发现题目数量不满足要求，开始动态调整...");
                        selectedTopics = dynamicallyAdjustTopics(selectedTopics, targetTypeCounts, typeTargetScores, targetTotalScore);
                        log.info("动态调整完成，调整后题目数量={}", selectedTopics.size());
                    } else {
                        log.info("各题型题目数量满足要求，无需调整");
                    }
                } else {
                    log.debug("未设置题型数量要求，跳过动态调整检查");
                }
            } catch (Exception e) {
                log.error("检查和调整题目数量时发生异常: {}", e.getMessage(), e);
                // 发生异常时，继续使用原始的题目列表，确保系统不会崩溃
            }
        } else {
            log.warn("生成的题目列表为空，无法进行动态调整");
        }

        log.info("Paper generation process completed. Returning {} topics.", selectedTopics == null ? 0 : selectedTopics.size());
        return selectedTopics;
    }

    /**
     * 智能扩展题目池以满足题型要求
     * 当选定知识点的题目不足时，从全局题库中补充相应题型的题目
     *
     * @param currentPool 当前题目池
     * @param typeTargetCounts 目标题型数量（数据库格式）
     * @return 扩展后的题目池
     */
    private List<Topic> expandQuestionPoolIfNeeded(List<Topic> currentPool, Map<String, Integer> typeTargetCounts) {
        if (typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            return currentPool;
        }

        // 统计当前题目池中各题型的数量
        Map<String, Integer> currentTypeCounts = new HashMap<>();
        for (Topic topic : currentPool) {
            String type = mapTopicType(topic.getType());
            currentTypeCounts.put(type, currentTypeCounts.getOrDefault(type, 0) + 1);
        }

        // 检查哪些题型需要补充
        Map<String, Integer> shortfalls = new HashMap<>();
        boolean needsExpansion = false;

        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int required = entry.getValue();
            int available = currentTypeCounts.getOrDefault(type, 0);

            if (required > 0 && available < required) {
                int shortfall = required - available;
                shortfalls.put(type, shortfall);
                needsExpansion = true;
                log.warn("题型 {} 不足: 需要 {} 题，当前仅有 {} 题，缺少 {} 题",
                        type, required, available, shortfall);
            }
        }

        if (!needsExpansion) {
            log.info("当前题目池已满足所有题型要求，无需扩展");
            return currentPool;
        }

        log.info("开始智能扩展题目池以补充不足的题型...");

        // 获取当前题目池中已有的题目ID，避免重复
        Set<Integer> existingTopicIds = currentPool.stream()
                .map(Topic::getId)
                .collect(Collectors.toSet());

        List<Topic> expandedPool = new ArrayList<>(currentPool);
        int totalAdded = 0;

        // 为每个不足的题型从全局题库中补充题目
        for (Map.Entry<String, Integer> shortfallEntry : shortfalls.entrySet()) {
            String type = shortfallEntry.getKey();
            int needed = shortfallEntry.getValue();

            try {
                // 从全局题库中获取该题型的题目
                List<Topic> globalTopicsOfType = topicMapper.selectByType(type);

                if (globalTopicsOfType == null || globalTopicsOfType.isEmpty()) {
                    log.warn("全局题库中没有找到题型 {} 的题目", type);
                    continue;
                }

                // 过滤掉已存在的题目，随机选择需要的数量
                List<Topic> availableTopics = globalTopicsOfType.stream()
                        .filter(topic -> !existingTopicIds.contains(topic.getId()))
                        .collect(Collectors.toList());

                if (availableTopics.isEmpty()) {
                    log.warn("题型 {} 的全局题目都已在当前题目池中", type);
                    continue;
                }

                // 随机打乱并选择需要的数量
                Collections.shuffle(availableTopics);
                int actualAdded = Math.min(needed, availableTopics.size());

                for (int i = 0; i < actualAdded; i++) {
                    Topic topicToAdd = availableTopics.get(i);
                    expandedPool.add(topicToAdd);
                    existingTopicIds.add(topicToAdd.getId());
                    totalAdded++;
                }

                log.info("为题型 {} 从全局题库补充了 {} 道题目", type, actualAdded);

            } catch (Exception e) {
                log.error("补充题型 {} 时发生错误: {}", type, e.getMessage(), e);
            }
        }

        if (totalAdded > 0) {
            log.info("题目池扩展完成，共补充 {} 道题目，扩展后题目池大小: {}", totalAdded, expandedPool.size());

            // 重新统计扩展后的题型分布
            Map<String, Integer> finalTypeCounts = new HashMap<>();
            for (Topic topic : expandedPool) {
                String type = mapTopicType(topic.getType());
                finalTypeCounts.put(type, finalTypeCounts.getOrDefault(type, 0) + 1);
            }
            log.info("扩展后题型分布: {}", finalTypeCounts);
        } else {
            log.warn("题目池扩展失败，无法补充任何题目");
        }

        return expandedPool;
    }

    /**
     * 辅助方法：将数据库中的题型值或前端传入的题型键名映射为标准化的内部题型标识符。
     * 这个方法用于统一不同来源的题型表示，确保系统内部使用一致的题型标识。
     *
     * @param typeKey 题型键名字符串（可能来自数据库或前端）。
     * @return 标准化的内部题型标识符；如果无法匹配，则返回原始 {@code typeKey}。
     */
    public String mapTopicType(String typeKey) {
        if (typeKey == null) {
            return "choice"; // 默认返回数据库格式
        }

        // 使用统一的TopicTypeMapper工具类，转换为数据库标准格式
        return TopicTypeMapper.toDbFormat(typeKey);
    }

    /**
     * 将标准化的内部题型标识符转换为数据库中存储的题型值。
     * 这个方法用于查询数据库时将内部题型标识符转换为数据库实际存储的值。
     *
     * @param internalType 标准化的内部题型标识符
     * @return 数据库中对应的题型值
     */
    public String mapInternalTypeToDatabaseType(String internalType) {
        if (internalType == null) {
            return "unknown";
        }

        switch (internalType) {
            // 新格式映射
            case "SINGLE_CHOICE":
                return "choice"; // Database stores as "choice"
            case "MULTIPLE_CHOICE":
                return "multiple"; // Database stores as "multiple"
            case "JUDGE":
                return "judge"; // Database stores as "judge"
            case "SHORT":
                return "short"; // Database stores as "short"
            case "FILL":
                return "fill"; // Database stores as "fill"
            case "SUBJECTIVE":
                return "subjective"; // Database stores as "subjective"
            case "GROUP":
                return "group"; // Database stores as "group"
            // 兼容旧格式
            case "singleChoice":
                return "choice";
            case "multipleChoice":
                return "multiple";
            case "judgment":
                return "judge";
            case "shortAnswer":
                return "short";
            case "fillBlank":
                return "fill";
            case "subjective":
                return "subjective";
            case "groupQuestion":
                return "group";
            default:
                log.warn("Unrecognized internal type: '{}'. Returning as is.", internalType);
                return internalType; // Fallback: return original type if not mapped
        }
    }

    /**
     * 统计各题型的实际数量
     * @param topics 选中的题目列表
     * @return 各题型数量的映射
     */
    private Map<String, Integer> countTopicsByType(List<Topic> topics) {
        Map<String, Integer> counts = new java.util.HashMap<>();
        for (Topic topic : topics) {
            String type = mapTopicType(topic.getType());
            counts.put(type, counts.getOrDefault(type, 0) + 1);
        }
        return counts;
    }

    /**
     * 检查实际题目数量是否满足要求
     * @param actualCounts 实际各题型数量
     * @param targetCounts 目标各题型数量
     * @return 是否满足要求
     */
    private boolean areTopicCountsSatisfied(Map<String, Integer> actualCounts, Map<String, Integer> targetCounts) {
        if (targetCounts == null || targetCounts.isEmpty()) {
            return true; // 如果没有具体要求，认为满足
        }

        for (Map.Entry<String, Integer> entry : targetCounts.entrySet()) {
            String type = entry.getKey();
            Integer target = entry.getValue();

            if (target == null || target <= 0) continue;

            Integer actual = actualCounts.getOrDefault(type, 0);
            if (actual < target) {
                return false; // 任一题型不满足要求，返回false
            }
        }

        return true;
    }

    /**
     * 动态调整题目，处理题目数量不足的情况
     * @param selectedTopics 已选中的题目
     * @param targetTypeCounts 目标题型数量
     * @param typeScores 各题型分数
     * @param targetTotalScore 目标总分
     * @return 调整后的题目列表
     */
    /**
     * 动态调整题目，处理题目数量不足的情况
     * <p>
     * 该方法尝试根据缺口数量从相似题型中调整题目，确保试卷符合要求。
     * 方法会先从相似的题型中寻找替代，然后如果还不足则使用任何可用的题型。
     * 如果还是不足，则记录警告并尽量返回可用的最佳题目组合。
     * </p>
     *
     * @param selectedTopics 已选中的题目
     * @param targetTypeCounts 目标题型数量
     * @param typeScores 各题型分数
     * @param targetTotalScore 目标总分
     * @return 调整后的题目列表
     * @throws IllegalArgumentException 如果输入参数无效
     */
    private List<Topic> dynamicallyAdjustTopics(List<Topic> selectedTopics, Map<String, Integer> targetTypeCounts,
                                               Map<String, Integer> typeScores, int targetTotalScore) {
        // 如果选中的题目为空，无法调整
        if (selectedTopics == null || selectedTopics.isEmpty()) {
            log.warn("Cannot adjust empty topic list");
            return Collections.emptyList();
        }

        // 预防性验证
        if (targetTypeCounts == null || targetTypeCounts.isEmpty()) {
            log.warn("目标题型数量为空，无法进行动态调整");
            return selectedTopics;
        }

        if (typeScores == null || typeScores.isEmpty()) {
            log.warn("题型分数配置为空，将使用默认分数选项");
            // 继续执行，但将在后面的代码中处理此情况
        }

        // 统计当前各题型数量
        Map<String, Integer> currentCounts = countTopicsByType(selectedTopics);
        Map<String, List<Topic>> topicsByType;

        try {
            topicsByType = selectedTopics.stream()
                    .collect(Collectors.groupingBy(topic -> mapTopicType(topic.getType())));
        } catch (Exception e) {
            log.error("对题目进行分组时出错: {}", e.getMessage(), e);
            return selectedTopics; // 出错时返回原始题目
        }

        // 计算各题型缺口
        Map<String, Integer> shortfalls = new java.util.HashMap<>();
        int totalShortfall = 0;

        for (Map.Entry<String, Integer> entry : targetTypeCounts.entrySet()) {
            String type = entry.getKey();
            int target = entry.getValue();
            int current = currentCounts.getOrDefault(type, 0);

            if (current < target) {
                int shortfall = target - current;
                shortfalls.put(type, shortfall);
                totalShortfall += shortfall;
                log.info("Shortfall detected for type {}: {} topics needed", type, shortfall);
            }
        }

        if (totalShortfall == 0) {
            log.info("No shortfalls detected, returning original topics");
            return selectedTopics;
        }

        // 尝试补充缺失的题目
        List<Topic> adjustedTopics = new ArrayList<>(selectedTopics);

        // 寻找可替代题型（拥有多余题目的题型）
        Map<String, Integer> surplusTypes = new java.util.HashMap<>();
        for (Map.Entry<String, List<Topic>> entry : topicsByType.entrySet()) {
            String type = entry.getKey();
            int count = entry.getValue().size();
            int target = targetTypeCounts.getOrDefault(type, 0);

            if (count > target && target > 0) {
                surplusTypes.put(type, count - target); // 记录多余的题目数量
            }
        }

        // 确定替代策略：优先使用相似题型替代，例如单选题和多选题，填空题和简答题等
        Map<String, List<String>> similarTypes = new java.util.HashMap<>();
        similarTypes.put("SINGLE_CHOICE", Arrays.asList("MULTIPLE_CHOICE", "JUDGE"));
        similarTypes.put("MULTIPLE_CHOICE", Arrays.asList("SINGLE_CHOICE", "JUDGE"));
        similarTypes.put("JUDGE", Arrays.asList("SINGLE_CHOICE", "MULTIPLE_CHOICE"));
        similarTypes.put("FILL", Arrays.asList("SHORT"));
        similarTypes.put("SHORT", Arrays.asList("FILL"));
        // 添加其他类型的默认替代策略
        similarTypes.put("SUBJECTIVE", Arrays.asList("SHORT", "FILL"));
        similarTypes.put("GROUP", new ArrayList<>());  // 组合题较特殊，暂不设置默认替代

        // 根据缺口调整题目
        for (Map.Entry<String, Integer> shortfallEntry : shortfalls.entrySet()) {
            String shortfallType = shortfallEntry.getKey();
            int needed = shortfallEntry.getValue();

            // 首先尝试从相似题型中获取替代题目
            List<String> potentialReplacements = similarTypes.getOrDefault(shortfallType, new ArrayList<>());
            for (String replacementType : potentialReplacements) {
                if (surplusTypes.containsKey(replacementType) && surplusTypes.get(replacementType) > 0) {
                    int available = surplusTypes.get(replacementType);
                    int toUse = Math.min(needed, available);

                    // 获取要转换的题目
                    List<Topic> replacementTopics = topicsByType.get(replacementType)
                            .subList(targetTypeCounts.getOrDefault(replacementType, 0),
                                     targetTypeCounts.getOrDefault(replacementType, 0) + toUse);

                    // 修改题目类型
                    for (Topic topic : replacementTopics) {
                        topic.setType(shortfallType);
                        log.debug("Converted topic ID {} from type {} to {}",
                                 topic.getId(), replacementType, shortfallType);
                    }

                    // 更新计数
                    needed -= toUse;
                    surplusTypes.put(replacementType, available - toUse);

                    if (needed == 0) break;
                }
            }

            // 如果仍有缺口且有其他多余题目，则使用任何可用类型
            if (needed > 0) {
                for (Map.Entry<String, Integer> surplusEntry : surplusTypes.entrySet()) {
                    String surplusType = surplusEntry.getKey();
                    int surplus = surplusEntry.getValue();

                    if (surplus > 0) {
                        int toUse = Math.min(needed, surplus);

                        // 获取要转换的题目
                        List<Topic> replacementTopics = topicsByType.get(surplusType)
                                .subList(targetTypeCounts.getOrDefault(surplusType, 0),
                                         targetTypeCounts.getOrDefault(surplusType, 0) + toUse);

                        // 修改题目类型
                        for (Topic topic : replacementTopics) {
                            topic.setType(shortfallType);
                            log.debug("Converted topic ID {} from type {} to {}",
                                     topic.getId(), surplusType, shortfallType);
                        }

                        // 更新计数
                        needed -= toUse;
                        surplusTypes.put(surplusType, surplus - toUse);

                        if (needed == 0) break;
                    }
                }
            }

            // 如果仍有缺口，记录警告
            if (needed > 0) {
                log.warn("Unable to fully satisfy requirement for type {}, still short by {} topics",
                        shortfallType, needed);
            }
        }

        // 重新调整分数以符合总分要求
        int currentTotalScore = adjustedTopics.stream()
                .mapToInt(t -> {
                    if (typeScores != null) {
                        return typeScores.getOrDefault(mapTopicType(t.getType()),
                                t.getScore() != null ? t.getScore() : 0);
                    } else {
                        return t.getScore() != null ? t.getScore() : 0;
                    }
                })
                .sum();

        if (currentTotalScore != targetTotalScore) {
            log.info("After adjustment, total score is {} but target is {}, attempting to adjust scores...",
                     currentTotalScore, targetTotalScore);

            try {
                if (dpAdjuster != null) {
                    adjustedTopics = dpAdjuster.adjust(adjustedTopics, targetTotalScore, typeScores);
                    // 计算调整后的总分，并做空值检查
                    int finalScore = adjustedTopics.stream()
                            .mapToInt(t -> {
                                if (typeScores != null) {
                                    return typeScores.getOrDefault(mapTopicType(t.getType()),
                                            t.getScore() != null ? t.getScore() : 0);
                                } else {
                                    return t.getScore() != null ? t.getScore() : 0;
                                }
                            })
                            .sum();
                    log.info("分数调整完成，调整后总分={}", finalScore);
                } else {
                    log.warn("分数调整器dpAdjuster为null，跳过分数调整步骤");
                }
            } catch (Exception e) {
                log.error("分数调整过程中发生异常: {}", e.getMessage(), e);
                // 发生异常时继续使用调整前的题目列表
            }
        }

        return adjustedTopics;
    }
}