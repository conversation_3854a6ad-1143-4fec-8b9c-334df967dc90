package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.repository.KnowledgePointRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识点题目服务，提供知识点与题目关联的相关功能
 * 包含缓存机制以提高性能
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KnowledgePointQuestionService {
    
    private final KnowledgePointRepository knowledgePointRepository;
    
    /**
     * 获取有题目的知识点ID列表
     * @param knowledgeIds 需要检查的知识点ID列表
     * @return 有题目的知识点ID列表
     */
    @Cacheable(value = "knowledgePointQuestions", key = "#knowledgeIds.hashCode()")
    public List<Long> getKnowledgePointsWithQuestions(List<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyList();
        }
        return knowledgePointRepository.findKnowledgePointsWithQuestions(knowledgeIds);
    }
    
    /**
     * 获取知识点题目数量统计
     * @param knowledgeIds 知识点ID列表
     * @return 知识点ID到题目数量的映射
     */
    @Cacheable(value = "knowledgePointQuestionCounts", key = "#knowledgeIds.hashCode()")
    public Map<Long, Integer> getQuestionCountsByKnowledgePoints(List<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return knowledgePointRepository.countQuestionsByKnowledgePoints(knowledgeIds)
                .stream()
                .collect(Collectors.toMap(
                        map -> ((Number) map.get("knowledgeId")).longValue(),
                        map -> ((Number) map.get("questionCount")).intValue()
                ));
    }
    
    /**
     * 过滤出有足够题目的知识点
     * @param knowledgeIds 知识点ID列表
     * @param minQuestions 最小题目数量要求
     * @return 满足条件的知识点ID列表
     */
    public List<Long> filterKnowledgePointsWithEnoughQuestions(List<Long> knowledgeIds, int minQuestions) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        Map<Long, Integer> questionCounts = getQuestionCountsByKnowledgePoints(knowledgeIds);
        return knowledgeIds.stream()
                .filter(knowledgeId -> questionCounts.getOrDefault(knowledgeId, 0) >= minQuestions)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取有简答题的知识点ID列表
     * @param knowledgeIds 需要检查的知识点ID列表
     * @return 有简答题的知识点ID列表
     */
    @Cacheable(value = "knowledgePointShortAnswerQuestions", key = "#knowledgeIds.hashCode()")
    public List<Long> getKnowledgePointsWithShortAnswerQuestions(List<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyList();
        }
        return knowledgePointRepository.findKnowledgePointsWithShortAnswerQuestions(knowledgeIds);
    }
    
    /**
     * 检查知识点是否有简答题
     * @param knowledgeId 知识点ID
     * @return 是否有简答题
     */
    @Cacheable(value = "knowledgePointHasShortAnswer", key = "#knowledgeId")
    public boolean hasShortAnswerQuestions(Long knowledgeId) {
        if (knowledgeId == null) {
            return false;
        }
        List<Long> result = getKnowledgePointsWithShortAnswerQuestions(Collections.singletonList(knowledgeId));
        return result != null && !result.isEmpty();
    }
    
    /**
     * 批量检查知识点是否有简答题
     * @param knowledgeIds 知识点ID列表
     * @return 知识点ID到是否有简答题的映射
     */
    @Cacheable(value = "knowledgePointsHaveShortAnswer", key = "#knowledgeIds.hashCode()")
    public Map<Long, Boolean> checkKnowledgePointsHaveShortAnswerQuestions(List<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyMap();
        }
        
        List<Long> knowledgePointsWithShortAnswer = getKnowledgePointsWithShortAnswerQuestions(knowledgeIds);
        Map<Long, Boolean> result = new HashMap<>();
        
        for (Long id : knowledgeIds) {
            result.put(id, knowledgePointsWithShortAnswer.contains(id));
        }
        
        return result;
    }
    
    /**
     * 获取知识点的题目类型统计
     * @param knowledgeIds 知识点ID列表
     * @return 知识点ID到题目类型统计的映射
     */
    @Cacheable(value = "knowledgePointQuestionTypes", key = "#knowledgeIds.hashCode()")
    public Map<Long, Map<String, Integer>> getQuestionTypesByKnowledgePoints(List<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyMap();
        }
        
        List<Map<String, Object>> typeCounts = knowledgePointRepository.countQuestionTypesByKnowledgePoints(knowledgeIds);
        Map<Long, Map<String, Integer>> result = new HashMap<>();
        
        // 初始化结果映射
        for (Long id : knowledgeIds) {
            result.put(id, new HashMap<>());
        }
        
        // 填充题目类型统计
        for (Map<String, Object> typeCount : typeCounts) {
            Long knowledgeId = ((Number) typeCount.get("knowledgeId")).longValue();
            String questionType = (String) typeCount.get("questionType");
            Integer count = ((Number) typeCount.get("questionCount")).intValue();
            
            Map<String, Integer> typeMap = result.get(knowledgeId);
            if (typeMap != null) {
                typeMap.put(questionType, count);
            }
        }
        
        return result;
    }
    
    /**
     * 过滤出有简答题的知识点
     * @param knowledgeIds 知识点ID列表
     * @return 有简答题的知识点ID列表
     */
    public List<Long> filterKnowledgePointsWithShortAnswerQuestions(List<Long> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return getKnowledgePointsWithShortAnswerQuestions(knowledgeIds);
    }
}
