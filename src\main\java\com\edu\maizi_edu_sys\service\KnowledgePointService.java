package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.KnowledgePointDto;
import com.edu.maizi_edu_sys.dto.SimpleKnowledgePointDto;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;

import java.util.List;

public interface KnowledgePointService {
    List<KnowledgePointDto> findKnowledgePointsByClassificationId(Integer classificationId, Integer page, Integer size);

    KnowledgePointDto createKnowledgePoint(KnowledgePointDto knowledgePointDto);

    KnowledgePointDto updateKnowledgePoint(KnowledgePointDto knowledgePointDto);

    void deleteKnowledgePoint(Long id);

    long countByClassificationId(Integer classificationId);

    List<SimpleKnowledgePointDto> getAllSimpleKnowledgePoints();

    List<KnowledgePoint> findByIds(List<Long> ids);

}