<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理 - <PERSON><PERSON> EDU</title>
    <link rel="stylesheet" href="/static/css/common.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <a href="/">Maizi EDU</a>
        </div>
        <div class="nav-menu">
            <a href="/main/chat" class="nav-item">出题</a>
            <a href="/topics/upload-topics" class="nav-item">上传</a>
            <a href="/paper/generate" class="nav-item">组卷</a>
            <a href="/paper/check" class="nav-item">查重</a>
            <a href="/topics/bank" class="nav-item active">题库</a>
            <a href="/main/books" class="nav-item">教材资源</a>
        </div>
        <div class="nav-user">
            <div class="user-info">
                <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                <span class="username">加载中...</span>
            </div>
            <div class="dropdown-menu">
                <a href="/user/profile">个人信息</a>
                <a href="#" id="logout">退出登录</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>题库管理</h2>
            <div>
                <button class="btn btn-outline-primary me-2" id="refreshBtn">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <button class="btn btn-primary" id="addTopicBtn">
                    <i class="bi bi-plus-lg"></i> 新增题目
                </button>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-light">
                <form id="searchForm" class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <label for="topicType" class="form-label">题目类型</label>
                        <select class="form-select" id="topicType">
                            <option value="">全部</option>
                            <option value="choice">单选题</option>
                            <option value="multiple">多选题</option>
                            <option value="judge">判断题</option>
                            <option value="fill">填空题</option>
                            <option value="short">简答题</option>
                            <option value="subjective">主观题</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="knowId" class="form-label">知识点ID</label>
                        <input type="text" class="form-control" id="knowId">
                    </div>
                    <div class="col-md-4">
                        <label for="keyword" class="form-label">关键词</label>
                        <input type="text" class="form-control" id="keyword" placeholder="题目内容关键词">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th width="5%">ID</th>
                        <th width="10%">类型</th>
                        <th width="40%">题目</th>
                        <th width="10%">难度</th>
                        <th width="15%">知识点</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody id="topicsList">
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载题目数据...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页将通过JS动态生成 -->
            </ul>
        </nav>
    </div>

    <!-- 消息提示框 -->
    <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/toast.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载用户信息
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/auth/login';
                return;
            }
            
            fetch('/api/user/info', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    document.querySelector('.username').textContent = data.data.username;
                    if (data.data.avatar) {
                        document.querySelector('.avatar').src = data.data.avatar;
                    }
                } else {
                    window.location.href = '/auth/login';
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                showToast('获取用户信息失败，请重新登录', 'error');
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 1500);
            });
            
            // 注销功能
            document.getElementById('logout').addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                showToast('已成功退出登录', 'info');
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 1500);
            });
            
            // 搜索表单提交
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                showToast('搜索功能正在开发中...', 'info');
            });
            
            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', function() {
                showToast('刷新功能正在开发中...', 'info');
            });
            
            // 新增题目按钮
            document.getElementById('addTopicBtn').addEventListener('click', function() {
                showToast('新增题目功能正在开发中...', 'info');
            });
            
            // 模拟加载数据
            setTimeout(function() {
                document.getElementById('topicsList').innerHTML = '<tr><td colspan="6" class="text-center py-4">暂无数据</td></tr>';
            }, 1500);
        });
    </script>
</body>
</html> 