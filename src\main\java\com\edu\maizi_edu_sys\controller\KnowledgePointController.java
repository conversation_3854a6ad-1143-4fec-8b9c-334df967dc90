package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.SimpleKnowledgePointDto;
import com.edu.maizi_edu_sys.service.KnowledgePointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Collections;

@RestController
@RequestMapping("/api/knowledge-points")
public class KnowledgePointController {

    private final KnowledgePointService knowledgePointService;

    @Autowired
    public KnowledgePointController(KnowledgePointService knowledgePointService) {
        this.knowledgePointService = knowledgePointService;
    }

    @GetMapping("/all-simplified")
    public ResponseEntity<List<SimpleKnowledgePointDto>> getAllSimpleKnowledgePoints() {
        List<SimpleKnowledgePointDto> simpleDtos = knowledgePointService.getAllSimpleKnowledgePoints();
        return ResponseEntity.ok(simpleDtos != null ? simpleDtos : Collections.emptyList());
    }
} 