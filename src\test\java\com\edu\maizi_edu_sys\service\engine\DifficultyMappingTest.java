package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.config.TestConfig;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Import(TestConfig.class)
public class DifficultyMappingTest {

    @Autowired
    private TopicMapper topicMapper;
    
    @Test
    @DisplayName("测试数据库中的难度分布")
    public void testDatabaseDifficultyDistribution() {
        // 查询数据库中的样本题目
        Topic sampleTopic = topicMapper.findAnyTopic();
        if (sampleTopic == null) {
            System.out.println("警告: 数据库中没有题目数据");
            return;
        }
        
        // 测试难度分类函数
        System.out.println("难度映射测试样例:");
        testDifficultyMapping(0.1, "easy");
        testDifficultyMapping(0.2, "easy");
        testDifficultyMapping(0.3, "medium");
        testDifficultyMapping(0.4, "medium");
        testDifficultyMapping(0.5, "hard");
        
        // 统计数据库中的实际难度分布
        int count = topicMapper.countAllTopics();
        System.out.println("数据库中总题目数: " + count);
        
        if (count > 0) {
            // 这里需要查询所有题目的难度 (实际使用中可能需要分页处理)
            List<Topic> allTopics = new ArrayList<>(); // 实际情况中应该分页查询
            // 由于无法直接获取所有题目，我们可以查询多个知识点下的题目作为样本
            for (int i = 1; i <= 10; i++) {
                List<Topic> topics = topicMapper.selectFromBakByKnowId(i);
                if (topics != null && !topics.isEmpty()) {
                    allTopics.addAll(topics);
                    if (allTopics.size() > 500) break; // 限制样本大小以避免内存问题
                }
            }
            
            // 按难度值统计分布
            Map<Double, Long> difficultyDistribution = allTopics.stream()
                    .collect(Collectors.groupingBy(Topic::getDifficulty, Collectors.counting()));
            
            System.out.println("数据库中难度分布 (基于" + allTopics.size() + "个样本):");
            difficultyDistribution.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        double percent = (double) entry.getValue() / allTopics.size() * 100;
                        String category = getDifficultyCategory(entry.getKey());
                        System.out.printf("难度值: %.1f (%s) - %d题 (%.1f%%)\n", 
                                entry.getKey(), category, entry.getValue(), percent);
                    });
            
            // 按分类统计分布
            Map<String, Long> categoryDistribution = allTopics.stream()
                    .collect(Collectors.groupingBy(
                            topic -> getDifficultyCategory(topic.getDifficulty()), 
                            Collectors.counting()));
            
            System.out.println("\n按分类的难度分布:");
            categoryDistribution.forEach((category, num) -> {
                double percent = (double) num / allTopics.size() * 100;
                System.out.printf("%s难度: %d题 (%.1f%%)\n", category, num, percent);
            });
        }
    }
    
    private void testDifficultyMapping(double difficultyValue, String expectedCategory) {
        String actualCategory = getDifficultyCategory(difficultyValue);
        System.out.printf("难度值 %.1f 映射到 %s 类别\n", difficultyValue, actualCategory);
        assertEquals(expectedCategory, actualCategory, 
                String.format("难度值 %.1f 应该映射到 %s 类别", difficultyValue, expectedCategory));
    }
    
    // 与GeneticSolver中保持一致的难度分类函数
    private String getDifficultyCategory(double difficultyValue) {
        if (difficultyValue <= 0.2) return "easy";
        if (difficultyValue <= 0.4) return "medium";
        return "hard";
    }
    
    @Test
    @DisplayName("测试TopicMapper难度范围查询")
    public void testTopicMapperDifficultyRangeQuery() {
        // 测试不同难度参数的题目查询结果
        testDifficultyRangeQuery(0.1, "easy");
        testDifficultyRangeQuery(0.3, "medium");
        testDifficultyRangeQuery(0.5, "hard");
    }
    
    private void testDifficultyRangeQuery(double difficultyValue, String categoryName) {
        // 获取一个知识点ID进行测试
        List<Integer> knowIds = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            long count = topicMapper.countFromBakByKnowId(i);
            if (count > 0) {
                knowIds.add(i);
                if (knowIds.size() >= 3) break;
            }
        }
        
        if (knowIds.isEmpty()) {
            System.out.println("警告: 找不到有题目的知识点，无法测试");
            return;
        }
        
        System.out.printf("\n测试难度值 %.1f (%s) 的题目查询:\n", difficultyValue, categoryName);
        
        for (Integer knowId : knowIds) {
            // 使用普通范围
            List<Integer> normalResults = topicMapper.findIdsByKnowledgeAndTypeAndDifficulty(
                    knowId, "单选题", difficultyValue);
            
            // 使用宽松范围
            List<Integer> widerResults = topicMapper.findIdsByKnowledgeAndTypeWithWiderRange(
                    knowId, "单选题", difficultyValue);
            
            System.out.printf("知识点ID %d - 普通范围查询到 %d 题，宽松范围查询到 %d 题\n", 
                    knowId, 
                    normalResults != null ? normalResults.size() : 0, 
                    widerResults != null ? widerResults.size() : 0);
            
            if (normalResults == null || normalResults.isEmpty()) {
                System.out.printf("  警告: 知识点ID %d 的 %.1f 难度没有查询到题目\n", knowId, difficultyValue);
            }
        }
    }
} 