# ========================
# 应用程序基础配置
# ========================
server:
  port: 8081  # 服务器端口号
  # Tomcat连接配置 - 优化文件下载和客户端连接处理
  tomcat:
    connection-timeout: 60000      # 连接超时时间(毫秒) - 60秒
    keep-alive-timeout: 60000      # Keep-Alive超时时间(毫秒) - 60秒
    max-keep-alive-requests: 100   # 最大Keep-Alive请求数
    max-connections: 8192          # 最大连接数
    accept-count: 100              # 等待队列长度
    threads:
      max: 200                     # 最大工作线程数
      min-spare: 10                # 最小空闲线程数
  # HTTP响应配置
  compression:
    enabled: true                  # 启用响应压缩
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024        # 最小压缩响应大小

# ========================
# Thymeleaf 模板引擎配置
# ========================
spring:
  thymeleaf:
    cache: false           # 禁用模板缓存（开发环境建议关闭）
    prefix: classpath:/templates/  # 模板文件路径前缀
    suffix: .html          # 模板文件后缀
    mode: HTML             # 模板解析模式

  # ======================
  # 数据源配置 (MySQL)
  # ======================
  datasource:
    url: ***************************************************************************************************************
    username: root         # 数据库用户名
    password: Hilury157195!    # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver  # JDBC驱动类名

  # ======================
  # JPA 持久化配置
  # ======================
  jpa:
    hibernate:
      ddl-auto: update     # 自动更新数据库结构（update/none）
    show-sql: true         # 显示生成的SQL语句
    properties:
      hibernate:
        format_sql: true   # 格式化SQL输出
    database-platform: org.hibernate.dialect.MySQL8Dialect  # 数据库方言
    open-in-view: false    # 禁用OpenSessionInView

  # ======================
  # 静态资源配置
  # ======================
  web:
    resources:
      static-locations: classpath:/static/  # 静态资源路径
  mvc:
    static-path-pattern: /static/**         # 静态资源访问路径

  # ======================
  # 文件上传配置
  # ======================
  servlet:
    multipart:
      max-file-size: 10MB      # 单个文件最大尺寸
      max-request-size: 10MB   # 请求最大尺寸

  # ======================
  # Redis 配置
  # ======================
  redis:
    host: localhost        # Redis服务器地址
    port: 6379             # Redis端口号
    database: 0            # 数据库索引
    timeout: 10000         # 连接超时时间(毫秒)
    lettuce:
      pool:
        max-active: 8      # 最大活跃连接数
        max-wait: -1       # 最大等待时间(负值表示无限等待)
        max-idle: 8        # 最大空闲连接数
        min-idle: 0        # 最小空闲连接数

  main:
    allow-bean-definition-overriding: true  # 允许Bean定义覆盖

# ========================
# JWT 鉴权配置
# ========================
jwt:
  secret: F9A8C28B7E5D3F1A6E5D4C3B2A1F0E9D8C7B6A5F4E3D2C1B0A9G8H7I6J5K4L3M2N1  # 密钥
  expiration: 86400000    # Token有效期(毫秒)，默认24小时

# ========================
# MyBatis-Plus 配置
# ========================
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml  # Mapper文件路径
  type-aliases-package: com.edu.maizi_edu_sys.entity  # 实体类包扫描路径
  configuration:
    map-underscore-to-camel-case: true  # 开启字段下划线转驼峰
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出实现类
  global-config:
    db-config:
      logic-delete-field: is_deleted    # 逻辑删除字段名
      logic-delete-value: true          # 逻辑删除标记值
      logic-not-delete-value: false     # 逻辑未删除标记值

# ========================
# 日志系统配置
# ========================
logging:
  level:
    com.baomidou.mybatisplus: DEBUG     # MyBatis-Plus日志级别
    com.edu.maizi_edu_sys.repository: DEBUG  # DAO层日志级别
    com.edu.maizi_edu_sys: DEBUG        # 应用主包日志级别
    com.edu.maizi_edu_sys.service.impl.ChatServiceImpl: DEBUG  # 聊天服务日志级别
    com.edu.maizi_edu_sys.controller: DEBUG  # 控制器日志级别
    com.volcengine.ark: INFO            # 火山引擎组件日志级别
    org.springframework.web.socket: WARN # WebSocket日志级别
    org.springframework.messaging: WARN # 消息通信日志级别
    org.springframework.web: INFO       # Spring Web日志级别
    root: INFO                          # 全局默认日志级别
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"  # 控制台输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"     # 文件输出格式

# ========================
# 用户模块配置
# ========================
user:
  avatar-path: uploads/avatars/        # 头像存储路径
  default-avatar: default-avatar.png   # 默认头像文件名
  max-avatar-size: 5242880             # 最大头像尺寸(5MB)
  allowed-avatar-types: image/jpeg, image/png, image/gif  # 允许上传的头像类型

# ========================
# 文件上传配置
# ========================
file:
  upload:
    base-path: ./uploads               # 文件上传根目录
    avatar:
      path: ${file.upload.base-path}/avatars  # 头像上传子目录

# ========================
# 聊天机器人配置
# ========================
bot:
  id: bot-20250507182807-dbmrx         # 机器人唯一标识
  api-key: 71c264ae-dc70-42b6-84c1-72b055ecfb8c  # API访问密钥

# ========================
# 遗传算法参数配置
# ========================
algorithm:
  genetic:
    population-size: 100
    max-generations: 50
    min-generations: 30
    crossover-rate: 0.8
    mutation-rate: 0.1
    tournament-size: 5
    early-terminate-threshold: 0.97
    global-timeout-seconds: 2
    fitness-weights:
      score: 0.4
      quality: 0.2
      difficulty-distribution: 0.15
      cognitive-distribution: 0.15
      kp-coverage: 0.05
      topic-type-diversity: 0.05
    repair:
      enabled: true
      max-steps: 3
      greedy-threshold: 0.1
    adaptive-mutation:
      enabled: true
      max-rate: 0.3
      min-rate: 0.05
      stagnation-threshold: 5
  diversity:
    similarity-threshold: 0.85
    # 知识点级别多样性控制
    knowledge-point-level:
      enabled: true                           # 是否启用知识点级别多样性控制
      min-reuse-interval-days: 1              # 知识点内部最小重用间隔（天）- 降低到1天
      max-topics-per-knowledge-point: 50      # 每个知识点最大题目数量 - 增加到50题
      priority-weight-usage: 10.0             # 使用次数权重
      priority-weight-time: 1.0               # 时间权重
  quality:
    error-rate-weight: 0.6
    freshness-weight: 0.4
  exposure-control:
    default-min-reuse-interval-days: 1

