/**
 * PDF选项显示调试脚本
 * 用于测试PDF下载时选择题选项是否正确显示
 */

$(document).ready(function() {
    console.log('📄 PDF选项调试脚本已加载');
    
    // 添加PDF调试按钮
    addPdfDebugButton();
});

/**
 * 添加PDF调试按钮
 */
function addPdfDebugButton() {
    // 在下载按钮旁边添加调试按钮
    const debugButton = `
        <button class="btn btn-warning btn-sm ml-2" onclick="testPdfOptions()" title="测试PDF选项">
            <i class="fas fa-bug"></i> PDF调试
        </button>
    `;
    
    $('.print-button-container').append(debugButton);
}

/**
 * 测试PDF选项显示
 */
window.testPdfOptions = function() {
    console.log('🧪 开始测试PDF选项显示...');
    
    // 收集页面上的选择题信息
    const choiceQuestions = [];
    
    $('.question').each(function() {
        const $question = $(this);
        const $options = $question.find('.options');
        
        if ($options.length > 0) {
            const questionText = $question.find('.topic-title-content').text().trim();
            const topicOptions = $question.find('.topic-options');
            const optionsData = topicOptions.attr('data-options');
            const topicId = topicOptions.attr('data-topic-id');
            const topicType = topicOptions.attr('data-topic-type');
            
            const renderedOptions = [];
            $question.find('.option').each(function() {
                const label = $(this).find('.option-label').text();
                const text = $(this).find('.option-text').text();
                renderedOptions.push({ label, text });
            });
            
            choiceQuestions.push({
                id: topicId,
                type: topicType,
                question: questionText,
                optionsData: optionsData,
                renderedOptions: renderedOptions
            });
        }
    });
    
    console.log(`找到 ${choiceQuestions.length} 个选择题:`, choiceQuestions);
    
    // 显示测试结果
    showPdfOptionsTestResult(choiceQuestions);
};

/**
 * 显示PDF选项测试结果
 */
function showPdfOptionsTestResult(questions) {
    let resultHtml = `
        <div style="font-family: monospace; font-size: 12px; text-align: left; max-height: 400px; overflow-y: auto;">
            <h6>📄 PDF选项显示测试结果</h6>
            <p><strong>选择题总数:</strong> ${questions.length}</p>
            <hr>
    `;
    
    questions.forEach((q, index) => {
        const hasOptions = q.renderedOptions.length > 0;
        const statusIcon = hasOptions ? '✅' : '❌';
        
        resultHtml += `
            <div style="margin-bottom: 15px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <p><strong>${statusIcon} 题目 ${index + 1} (ID: ${q.id})</strong></p>
                <p><strong>类型:</strong> ${q.type}</p>
                <p><strong>题目:</strong> ${q.question.substring(0, 50)}${q.question.length > 50 ? '...' : ''}</p>
                <p><strong>选项数量:</strong> ${q.renderedOptions.length}</p>
                
                ${q.renderedOptions.length > 0 ? `
                    <p><strong>选项内容:</strong></p>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        ${q.renderedOptions.map(opt => `<li>${opt.label} ${opt.text}</li>`).join('')}
                    </ul>
                ` : `
                    <p style="color: red;"><strong>⚠️ 无选项显示</strong></p>
                    <p><strong>原始数据:</strong> ${q.optionsData || 'null'}</p>
                `}
            </div>
        `;
    });
    
    resultHtml += `
            <hr>
            <p><strong>建议:</strong></p>
            <ul>
                <li>如果选项显示正常，PDF应该也能正确显示</li>
                <li>如果选项缺失，检查题目的options字段数据</li>
                <li>可以下载PDF验证实际效果</li>
            </ul>
        </div>
    `;
    
    Swal.fire({
        title: 'PDF选项测试结果',
        html: resultHtml,
        width: '700px',
        showConfirmButton: true,
        confirmButtonText: '下载测试PDF',
        showCancelButton: true,
        cancelButtonText: '关闭'
    }).then((result) => {
        if (result.isConfirmed) {
            // 触发PDF下载进行验证
            const paperId = $('.version-selector').first().attr('data-paper-id');
            if (paperId) {
                window.open(`/api/papers/download/${paperId}?format=pdf&paperType=standard`, '_blank');
            }
        }
    });
}

/**
 * 模拟PDF选项解析测试
 */
window.simulatePdfOptionsProcessing = function() {
    console.log('🔧 模拟PDF选项解析处理...');
    
    const testCases = [
        {
            name: '标准格式',
            data: '[{"key":"A","name":"选项A内容"},{"key":"B","name":"选项B内容"}]',
            type: 'choice'
        },
        {
            name: '旧格式',
            data: '{"A":"选项A内容","B":"选项B内容","C":"选项C内容"}',
            type: 'multiple'
        },
        {
            name: '简单数组',
            data: '["选项A","选项B","选项C","选项D"]',
            type: 'choice'
        }
    ];
    
    console.log('测试不同格式的选项数据解析:');
    
    testCases.forEach(testCase => {
        console.log(`\n测试 ${testCase.name}:`);
        console.log(`原始数据: ${testCase.data}`);
        
        try {
            const parsed = JSON.parse(testCase.data);
            console.log(`解析结果:`, parsed);
            
            // 模拟后端解析逻辑
            const optionEntries = [];
            
            if (Array.isArray(parsed)) {
                parsed.forEach((option, idx) => {
                    if (typeof option === 'object' && option.key && option.name) {
                        optionEntries.push({ key: option.key, value: option.name });
                    } else if (typeof option === 'string') {
                        const key = String.fromCharCode(65 + idx);
                        optionEntries.push({ key: key, value: option });
                    }
                });
            } else if (typeof parsed === 'object') {
                Object.keys(parsed).forEach(key => {
                    optionEntries.push({ key: key, value: parsed[key] });
                });
            }
            
            console.log(`PDF选项条目:`, optionEntries);
            console.log(`✅ 解析成功，将生成 ${optionEntries.length} 个选项`);
            
        } catch (e) {
            console.error(`❌ 解析失败: ${e.message}`);
        }
    });
};

/**
 * 检查题目类型映射
 */
window.checkTopicTypeMapping = function() {
    console.log('🔍 检查题目类型映射...');
    
    const topicTypes = [];
    $('.topic-options').each(function() {
        const topicType = $(this).attr('data-topic-type');
        const topicId = $(this).attr('data-topic-id');
        topicTypes.push({ id: topicId, type: topicType });
    });
    
    console.log('页面上的题目类型:', topicTypes);
    
    // 检查类型映射
    const typeMapping = {
        'choice': 'SINGLE_CHOICE',
        'multiple': 'MULTIPLE_CHOICE',
        'singleChoice': 'SINGLE_CHOICE',
        'multipleChoice': 'MULTIPLE_CHOICE'
    };
    
    console.log('类型映射规则:', typeMapping);
    
    topicTypes.forEach(topic => {
        const mappedType = typeMapping[topic.type] || topic.type;
        const shouldHaveOptions = mappedType === 'SINGLE_CHOICE' || mappedType === 'MULTIPLE_CHOICE';
        console.log(`题目 ${topic.id}: ${topic.type} -> ${mappedType}, 应有选项: ${shouldHaveOptions}`);
    });
};

// 在控制台提供快捷命令提示
console.log(`
📄 PDF选项调试命令:
- testPdfOptions() - 测试PDF选项显示
- simulatePdfOptionsProcessing() - 模拟PDF选项解析
- checkTopicTypeMapping() - 检查题目类型映射
`);
