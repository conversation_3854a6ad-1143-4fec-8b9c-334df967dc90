<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.repository.TopicMapper">

    <select id="countTopicsByTypeForKnowledgePoints" resultType="java.util.Map">
        SELECT
            type,
            COUNT(*) as count
        FROM
            topic_bak
        WHERE
            know_id IN
        <foreach item="item" index="index" collection="knowIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
            type
    </select>
    
    <!-- 添加单个知识点的题型统计查询 -->
    <select id="countTopicsByTypeForKnowledgePoint" resultType="java.util.Map">
        SELECT
            type,
            COUNT(*) as count
        FROM
            topic_bak
        WHERE
            know_id = #{knowId}
        GROUP BY
            type
    </select>

</mapper>