/**
 * 默认头像路径修复工具
 * 
 * 此脚本解决系统中默认头像路径不一致的问题
 * 确保所有默认头像引用使用标准路径: /static/images/default-avatar.png
 */

(function() {
    console.log("Default avatar fix script loaded");
    
    // Track failed images to prevent infinite retries
    const failedImageUrls = new Set();
    
    // Standard default image path - base64 encoded transparent 1x1 pixel as fallback
    const transparentPixel = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    
    // Better default path if available
    const defaultAvatarPath = '/static/images/default-avatar.png';
    
    // Observer to watch for avatar images
    function setupImageErrorHandler() {
        // Find all avatar images that might need fixing
        document.querySelectorAll('img.avatar').forEach(fixImage);
        
        // Set up observer to catch dynamically added avatars
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        // Handle direct avatar additions
                        if (node.tagName === 'IMG' && node.classList.contains('avatar')) {
                            fixImage(node);
                        }
                        
                        // Handle containers with avatars inside
                        if (node.nodeType === 1) { // ELEMENT_NODE
                            node.querySelectorAll('img.avatar').forEach(fixImage);
                        }
                    });
                }
            });
        });
        
        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });
    }
    
    function fixImage(imgElement) {
        // Skip if already processed
        if (imgElement.dataset.avatarFixed === 'true') return;
        
        // Mark as processed to prevent multiple handlers
        imgElement.dataset.avatarFixed = 'true';
        
        // Handle error
        imgElement.addEventListener('error', function() {
            const src = this.src;
            
            // Skip if we've already tried fixing this image before
            if (failedImageUrls.has(src)) return;
            failedImageUrls.add(src);
            
            console.log(`Image load failed, using standard default: ${src}`);
            
            // Use transparent pixel as absolute fallback that won't fail
            this.src = transparentPixel;
            
            // Try to load the default avatar only once
            if (!failedImageUrls.has(defaultAvatarPath)) {
                const testImg = new Image();
                testImg.onload = () => {
                    this.src = defaultAvatarPath;
                };
                testImg.onerror = () => {
                    // If default avatar fails, the transparent pixel remains
                    failedImageUrls.add(defaultAvatarPath);
                    console.log("Standard default avatar not available, using transparent pixel");
                };
                testImg.src = defaultAvatarPath;
            }
        });
    }
    
    // Run when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupImageErrorHandler);
    } else {
        setupImageErrorHandler();
    }
})();
