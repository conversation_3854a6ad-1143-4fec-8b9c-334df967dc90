package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List; // If a topic can be linked to multiple KPs for display

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TopicInOverviewDTO {
    private Integer id;
    private String content; // Or however you store the main topic text
    private String type;    // e.g., "singleChoice", "multipleChoice"
    private Integer score;  // Actual score assigned to this topic in the paper
    private String difficultyLevel; // e.g., "easy", "medium", "hard"
    private String answer; // The correct answer(s)
    private String analysis; // Explanation or analysis for the topic

    // Information about the knowledge point(s) this topic is related to
    private List<SimpleKnowledgePointDto> relatedKnowledgePoints; // Using existing SimpleKnowledgePointDto
} 