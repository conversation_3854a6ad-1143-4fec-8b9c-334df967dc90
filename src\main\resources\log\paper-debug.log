2025-05-28 14:41:02.753 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator.
2025-05-28 14:41:19.302 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:41:19.304 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=5
2025-05-28 14:41:19.308 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-28 14:41:19.333 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-28 14:41:19.338 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-28 14:41:19.463 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
