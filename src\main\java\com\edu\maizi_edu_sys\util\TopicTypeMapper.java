package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一的题型映射工具类
 * 解决前端、后端、数据库之间的题型名称不一致问题
 */
@Slf4j
public class TopicTypeMapper {

    // 数据库存储的标准题型（作为内部标准）
    public static final String DB_SINGLE_CHOICE = "choice";
    public static final String DB_MULTIPLE_CHOICE = "multiple";
    public static final String DB_JUDGMENT = "judge";
    public static final String DB_FILL_BLANK = "fill";
    public static final String DB_SHORT_ANSWER = "short";
    public static final String DB_SUBJECTIVE = "subjective";
    public static final String DB_GROUP = "group";

    // 前端使用的题型格式
    public static final String FRONTEND_SINGLE_CHOICE = "SINGLE_CHOICE";
    public static final String FRONTEND_MULTIPLE_CHOICE = "MULTIPLE_CHOICE";
    public static final String FRONTEND_JUDGMENT = "JUDGE";
    public static final String FRONTEND_FILL_BLANK = "FILL";
    public static final String FRONTEND_SHORT_ANSWER = "SHORT";
    public static final String FRONTEND_SUBJECTIVE = "SUBJECTIVE";
    public static final String FRONTEND_GROUP = "GROUP";

    // 中文名称映射
    private static final Map<String, String> CHINESE_NAMES = new HashMap<>();
    static {
        CHINESE_NAMES.put(DB_SINGLE_CHOICE, "单选题");
        CHINESE_NAMES.put(DB_MULTIPLE_CHOICE, "多选题");
        CHINESE_NAMES.put(DB_JUDGMENT, "判断题");
        CHINESE_NAMES.put(DB_FILL_BLANK, "填空题");
        CHINESE_NAMES.put(DB_SHORT_ANSWER, "简答题");
        CHINESE_NAMES.put(DB_SUBJECTIVE, "主观题");
        CHINESE_NAMES.put(DB_GROUP, "组合题");
    }

    /**
     * 将任意格式的题型转换为数据库标准格式
     * 这是系统的统一标准，所有组件都应该使用数据库格式作为内部标准
     * 
     * @param typeKey 任意格式的题型标识
     * @return 数据库标准格式的题型标识
     */
    public static String toDbFormat(String typeKey) {
        if (typeKey == null || typeKey.trim().isEmpty()) {
            log.warn("题型为空，返回默认值");
            return DB_SINGLE_CHOICE;
        }

        String normalized = typeKey.toLowerCase().trim();

        // 前端格式映射
        switch (typeKey.toUpperCase().trim()) {
            case FRONTEND_SINGLE_CHOICE:
                return DB_SINGLE_CHOICE;
            case FRONTEND_MULTIPLE_CHOICE:
                return DB_MULTIPLE_CHOICE;
            case FRONTEND_JUDGMENT:
                return DB_JUDGMENT;
            case FRONTEND_FILL_BLANK:
                return DB_FILL_BLANK;
            case FRONTEND_SHORT_ANSWER:
                return DB_SHORT_ANSWER;
            case FRONTEND_SUBJECTIVE:
                return DB_SUBJECTIVE;
            case FRONTEND_GROUP:
                return DB_GROUP;
        }

        // 数据库格式（已经是标准格式）
        switch (normalized) {
            case DB_SINGLE_CHOICE:
            case DB_MULTIPLE_CHOICE:
            case DB_JUDGMENT:
            case DB_FILL_BLANK:
            case DB_SHORT_ANSWER:
            case DB_SUBJECTIVE:
            case DB_GROUP:
                return normalized;
        }

        // 其他常见格式映射
        switch (normalized) {
            case "singlechoice":
            case "single_choice":
            case "单选题":
            case "single":
            case "选择题":
                return DB_SINGLE_CHOICE;

            case "multiplechoice":
            case "multiple_choice":
            case "多选题":
            case "multi":
                return DB_MULTIPLE_CHOICE;

            case "judgment":
            case "判断题":
            case "truefalse":
            case "tf":
            case "true_false":
            case "判断":
                return DB_JUDGMENT;

            case "fillblank":
            case "fill_blank":
            case "填空题":
            case "填空":
                return DB_FILL_BLANK;

            case "shortanswer":
            case "short_answer":
            case "简答题":
            case "essay":
            case "简答":
                return DB_SHORT_ANSWER;

            case "主观题":
            case "主观":
            case "论述题":
            case "论述":
                return DB_SUBJECTIVE;

            case "groupquestion":
            case "group_question":
            case "组合题":
            case "组合":
                return DB_GROUP;

            default:
                log.warn("未识别的题型: '{}', 返回默认单选题", typeKey);
                return DB_SINGLE_CHOICE;
        }
    }

    /**
     * 将数据库格式转换为前端格式
     * 
     * @param dbType 数据库格式的题型
     * @return 前端格式的题型
     */
    public static String toFrontendFormat(String dbType) {
        if (dbType == null) {
            return FRONTEND_SINGLE_CHOICE;
        }

        switch (dbType.toLowerCase().trim()) {
            case DB_SINGLE_CHOICE:
                return FRONTEND_SINGLE_CHOICE;
            case DB_MULTIPLE_CHOICE:
                return FRONTEND_MULTIPLE_CHOICE;
            case DB_JUDGMENT:
                return FRONTEND_JUDGMENT;
            case DB_FILL_BLANK:
                return FRONTEND_FILL_BLANK;
            case DB_SHORT_ANSWER:
                return FRONTEND_SHORT_ANSWER;
            case DB_SUBJECTIVE:
                return FRONTEND_SUBJECTIVE;
            case DB_GROUP:
                return FRONTEND_GROUP;
            default:
                log.warn("未识别的数据库题型: '{}', 返回默认前端格式", dbType);
                return FRONTEND_SINGLE_CHOICE;
        }
    }

    /**
     * 获取题型的中文名称
     * 
     * @param dbType 数据库格式的题型
     * @return 中文名称
     */
    public static String getChineseName(String dbType) {
        return CHINESE_NAMES.getOrDefault(dbType, "未知题型");
    }

    /**
     * 验证题型是否有效
     * 
     * @param typeKey 题型标识
     * @return 是否为有效题型
     */
    public static boolean isValidType(String typeKey) {
        if (typeKey == null || typeKey.trim().isEmpty()) {
            return false;
        }

        String dbType = toDbFormat(typeKey);
        return CHINESE_NAMES.containsKey(dbType);
    }

    /**
     * 获取所有支持的数据库格式题型
     * 
     * @return 数据库格式题型数组
     */
    public static String[] getAllDbTypes() {
        return new String[]{
            DB_SINGLE_CHOICE, DB_MULTIPLE_CHOICE, DB_JUDGMENT, 
            DB_FILL_BLANK, DB_SHORT_ANSWER, DB_SUBJECTIVE, DB_GROUP
        };
    }

    /**
     * 获取所有支持的前端格式题型
     * 
     * @return 前端格式题型数组
     */
    public static String[] getAllFrontendTypes() {
        return new String[]{
            FRONTEND_SINGLE_CHOICE, FRONTEND_MULTIPLE_CHOICE, FRONTEND_JUDGMENT,
            FRONTEND_FILL_BLANK, FRONTEND_SHORT_ANSWER, FRONTEND_SUBJECTIVE, FRONTEND_GROUP
        };
    }
}
