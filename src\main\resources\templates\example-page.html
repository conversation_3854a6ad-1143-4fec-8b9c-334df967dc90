<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面 - <PERSON><PERSON> EDU</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!--  通用导航栏样式 -->
    <link rel="stylesheet" href="/static/css/navbar.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-content {
            min-height: calc(100vh - 70px);
            padding-top: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!--  通用导航栏 - 传入当前页面标识 -->
    <div th:replace="fragments/navbar :: navbar('example')"></div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="h3 mb-1">
                            <i class="fas fa-rocket text-primary me-2"></i>
                            通用导航栏示例页面
                        </h1>
                        <p class="text-muted mb-0">展示如何在任何页面中使用通用导航栏组件</p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" onclick="testUserProfile()">
                            <i class="fas fa-user me-1"></i>
                            测试用户信息编辑
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 功能特性 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <h4>即插即用</h4>
                        <p class="text-muted">只需一行代码即可在任何页面中添加完整的导航栏功能，无需重复编写HTML和CSS。</p>
                        <div class="code-block">
                            &lt;div th:replace="fragments/navbar :: navbar('pageName')"&gt;&lt;/div&gt;
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <h4>用户信息管理</h4>
                        <p class="text-muted">支持用户信息编辑、头像上传、退出登录等完整的用户管理功能。</p>
                        <ul class="list-unstyled mt-3">
                            <li><i class="fas fa-check text-success me-2"></i>个人信息编辑</li>
                            <li><i class="fas fa-check text-success me-2"></i>头像上传</li>
                            <li><i class="fas fa-check text-success me-2"></i>安全退出</li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>响应式设计</h4>
                        <p class="text-muted">完全响应式设计，适配各种设备屏幕，提供一致的用户体验。</p>
                        <div class="row text-center mt-3">
                            <div class="col-4">
                                <i class="fas fa-desktop text-primary"></i>
                                <small class="d-block">桌面端</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-tablet-alt text-primary"></i>
                                <small class="d-block">平板</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-mobile-alt text-primary"></i>
                                <small class="d-block">手机</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4>现代化UI</h4>
                        <p class="text-muted">采用现代化的设计语言，包含动画效果、渐变背景、阴影等视觉元素。</p>
                        <div class="d-flex gap-2 mt-3">
                            <span class="badge bg-primary">渐变背景</span>
                            <span class="badge bg-success">流畅动画</span>
                            <span class="badge bg-info">阴影效果</span>
                            <span class="badge bg-warning">悬停反馈</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="feature-card">
                <h4><i class="fas fa-book text-primary me-2"></i>使用说明</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>1. 引入资源文件</h6>
                        <div class="code-block">
&lt;!-- CSS --&gt;
&lt;link rel="stylesheet" href="/static/css/navbar.css"&gt;

&lt;!-- JavaScript --&gt;
&lt;script src="/static/js/navbar.js"&gt;&lt;/script&gt;
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>2. 添加导航栏</h6>
                        <div class="code-block">
&lt;!-- 在页面中添加导航栏 --&gt;
&lt;div th:replace="fragments/navbar :: navbar('configs')"&gt;&lt;/div&gt;
                        </div>
                    </div>
                </div>
                
                <h6 class="mt-4">3. 页面标识说明</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>页面标识</th>
                                <th>对应菜单</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td>chat</td><td>出题</td><td>AI出题页面</td></tr>
                            <tr><td>upload</td><td>上传</td><td>题目上传页面</td></tr>
                            <tr><td>generate</td><td>组卷</td><td>试卷生成页面</td></tr>
                            <tr><td>check</td><td>查重</td><td>试卷查重页面</td></tr>
                            <tr><td>bank</td><td>题库</td><td>题库管理页面</td></tr>
                            <tr><td>books</td><td>教材资源</td><td>教材资源页面</td></tr>
                            <tr><td>configs</td><td>配置管理</td><td>配置管理页面</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!--  通用导航栏功能 -->
    <script src="/static/js/navbar.js"></script>
    
    <script>
        // 测试用户信息编辑功能
        function testUserProfile() {
            // 直接调用通用导航栏的用户信息编辑功能
            if (typeof openUserProfileModal === 'function') {
                openUserProfileModal();
            } else {
                Swal.fire('提示', '请确保已正确加载导航栏功能', 'info');
            }
        }
        
        // 页面加载完成后的初始化
        $(document).ready(function() {
            console.log('🔧 示例页面加载完成');
            
            // 显示成功消息
            setTimeout(() => {
                Swal.fire({
                    title: '页面加载成功',
                    text: '通用导航栏已正常工作',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false,
                    position: 'top-end',
                    toast: true
                });
            }, 1000);
        });
    </script>
</body>
</html>
