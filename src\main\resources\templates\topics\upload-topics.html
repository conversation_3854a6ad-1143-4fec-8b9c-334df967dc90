<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目上传 - <PERSON><PERSON> EDU</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/lib/codemirror.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/theme/material-darker.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/upload-topics.css">

    <!-- KaTeX Support -->
    <div th:replace="fragments/katex-support :: katex"></div>
</head>
<body>
    <!-- 导航栏将由 common.js 动态插入 -->

    <div class="container-fluid mt-4">
        <header class="page-header mb-4">
            <h2><i class="bi bi-cloud-arrow-up-fill"></i> 题目批量上传</h2>
            <p class="text-muted">通过JSON格式批量导入题目，支持Markdown和LaTeX公式。</p>
        </header>

        <div class="row g-3">
            <!-- JSON 编辑器区域 -->
            <div class="col-lg-6">
                <div class="card editor-card shadow-sm">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-filetype-json"></i> JSON 数据编辑器</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="insertMathExample()" title="插入数学公式示例">
                                <i class="bi bi-calculator"></i> 数学示例
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" id="insertExampleBtn" title="插入示例JSON">
                                <i class="bi bi-file-earmark-code"></i> 插入示例
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <textarea id="jsonEditor"></textarea>
                    </div>
                    <div class="card-footer editor-actions text-end">
                        <button class="btn btn-primary" id="validateAndPreviewBtn">
                            <i class="bi bi-check-circle"></i> 验证并预览
                        </button>
                        <button class="btn btn-success ms-2" id="submitTopicsBtn" disabled>
                            <i class="bi bi-cloud-upload"></i> 提交题目
                        </button>
                    </div>
                </div>
                <div class="alert alert-danger mt-3 d-none" id="errorMessage"></div>
                <div class="alert alert-success mt-3 d-none" id="successMessage"></div>
            </div>

            <!-- 预览区域 -->
            <div class="col-lg-6">
                <div class="card preview-card shadow-sm">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-eye-fill"></i> 题目预览</h5>
                        <div class="preview-controls">
                            <span class="badge bg-primary me-2" id="previewQuestionCount">0 / 0</span>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" id="previewFirstBtn" disabled title="第一题"><i class="bi bi-chevron-bar-left"></i></button>
                                <button class="btn btn-outline-secondary" id="previewPrevBtn" disabled title="上一题"><i class="bi bi-chevron-left"></i></button>
                                <button class="btn btn-outline-secondary" id="previewNextBtn" disabled title="下一题"><i class="bi bi-chevron-right"></i></button>
                                <button class="btn btn-outline-secondary" id="previewLastBtn" disabled title="最后一题"><i class="bi bi-chevron-bar-right"></i></button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="previewContainer" class="preview-container-empty">
                            <p class="text-muted text-center p-5">
                                <i class="bi bi-card-text fs-1"></i><br>
                                在左侧编辑器中输入或粘贴JSON数据，然后点击"验证并预览"按钮。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 格式说明 -->
        <div class="card mt-4 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> JSON 格式说明</h5>
            </div>
            <div class="card-body">
                <p>请确保您的JSON数据符合以下结构。所有文本字段（如 `title`, `options.name`, `parse`）均支持Markdown和LaTeX数学公式。</p>
                <pre class="bg-dark text-light p-3 rounded-2 small"><code>
[
  {
    "know_id": 218, // 知识点ID (必需, 整数)
    "type": "choice", // 题型 (必需, choice/multiple/judge/fill/short/subjective)
    "title": "新民主主义革命的开端是（    ）", // 题目标题 (必需)
    "options": [ // 选项 (选择题/多选题必需)
      {"key":"A","name":"中国共产党成立（1921年）"},
      {"key":"B","name":"五四运动（1919年）"}
      // ...更多选项
    ],
    "answer": "B", // 答案 (单选/多选/判断题必需, 填空题为数组或字符串)
    "parse": "五四运动中无产阶级首次以独立姿态登上政治舞台...", // 解析 (可选)
    "source": "《思想政治必修1中国特色社会主义》...", // 题目来源 (可选)
    "difficulty": 0.3 // 难度 (必需, 0.0 ~ 1.0)
  }
  // ...更多题目
]
                </code></pre>
                <h6>LaTeX 数学公式示例:</h6>
                <ul>
                    <li>行内公式: <code>$\sum_{i=1}^{n} i^2 = \frac{n(n+1)(2n+1)}{6}$</code> 将渲染为: <span class="tex2jax_process">$\sum_{i=1}^{n} i^2 = \frac{n(n+1)(2n+1)}{6}$</span></li>
                    <li>块级公式: <code>$$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$$</code> 将渲染为: <div class="tex2jax_process">$$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$$</div></li>
                </ul>
                 <p class="mt-2"><strong>注意:</strong> JSON字符串中的反斜杠 <code>\</code> 需要转义为 <code>\\</code>。例如, LaTeX命令 <code>\frac</code> 在JSON字符串中应写作 <code>\\frac</code>。</p>
            </div>
        </div>
    </div>

    <!-- 消息提示框容器 -->
    <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100"></div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/lib/codemirror.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/mode/javascript/javascript.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/toast.js"></script>
    <script src="/static/js/upload-topics.js"></script>
</body>
</html>