package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * 批量生成试卷响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchPaperGenerationResponse {

    private Boolean overallSuccess = false;
    private String errorMessage;
    private Integer totalCount = 0;
    private Integer successCount = 0;
    private Integer failedCount = 0;
    private List<SinglePaperResult> results = new ArrayList<>();

    /**
     * 单套试卷生成结果
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SinglePaperResult {
        private String title;
        private Boolean success;
        private String errorMessage;
        private Long paperId;
        private PaperGenerationResponse data;
    }

    /**
     * 添加成功结果
     */
    public void addSuccessResult(String title, Long paperId, PaperGenerationResponse data) {
        SinglePaperResult result = new SinglePaperResult();
        result.setTitle(title);
        result.setSuccess(true);
        result.setPaperId(paperId);
        result.setData(data);
        
        this.results.add(result);
        this.successCount++;
        this.totalCount++;
    }

    /**
     * 添加失败结果
     */
    public void addFailedResult(String title, String errorMessage) {
        SinglePaperResult result = new SinglePaperResult();
        result.setTitle(title);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        
        this.results.add(result);
        this.failedCount++;
        this.totalCount++;
    }

    /**
     * 完成批量生成，设置整体状态
     */
    public void complete() {
        this.overallSuccess = this.successCount > 0;
        if (this.failedCount > 0 && this.successCount == 0) {
            this.errorMessage = "所有试卷生成失败";
        } else if (this.failedCount > 0) {
            this.errorMessage = String.format("部分试卷生成失败：成功 %d 套，失败 %d 套", 
                                             this.successCount, this.failedCount);
        }
    }
}
