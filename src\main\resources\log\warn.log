2025-05-28 14:40:52.861 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-28 14:40:52.862 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-28 14:40:52.862 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-28 14:40:52.863 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:40:52.863 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:41:01.118 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-28 14:41:02.882 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-28 14:44:12.376 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-05-28 14:47:31.532 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:47:31.533 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:47:31.533 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:47:31.533 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:47:31.534 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:47:39.061 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-28 14:47:40.631 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-28 14:50:47.559 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-05-28 14:54:24.916 [http-nio-8081-exec-1] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-05-28 14:54:31.260 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
