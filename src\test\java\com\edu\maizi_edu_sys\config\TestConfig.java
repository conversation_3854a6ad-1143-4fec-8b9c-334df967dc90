package com.edu.maizi_edu_sys.config;

import com.edu.maizi_edu_sys.service.engine.TopicCacheManager;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;

@TestConfiguration
public class TestConfig {
    
    @Bean
    @Primary
    public TopicCacheManager mockCacheManager() {
        @SuppressWarnings("unchecked")
        RedisTemplate<String, byte[]> mockRedisTemplate = Mockito.mock(RedisTemplate.class);
        return new TopicCacheManager();
    }
} 