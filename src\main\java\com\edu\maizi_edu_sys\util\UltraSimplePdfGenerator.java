package com.edu.maizi_edu_sys.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 超简单PDF生成器
 * 只使用iText核心功能，避免所有外部依赖
 * 专注于稳定性和可靠性
 */
@Slf4j
public class UltraSimplePdfGenerator {

    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");

    /**
     * 将HTML内容转换为PDF
     * @param html HTML内容
     * @return PDF资源
     */
    public static Resource convertHtmlToPdf(String html) {
        log.info("开始使用超简单PDF生成器转换HTML到PDF");

        try {
            // 1. 清理HTML内容
            String cleanText = cleanHtml(html);
            log.debug("HTML清理完成，长度: {}", cleanText.length());

            // 2. 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Document document = new Document(PageSize.A4, 50, 50, 50, 50);
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            document.open();

            // 使用默认字体，确保兼容性
            Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
            Font normalFont = new Font(Font.FontFamily.HELVETICA, 12, Font.NORMAL);
            Font mathFont = new Font(Font.FontFamily.TIMES_ROMAN, 12, Font.ITALIC);

            // 添加标题
            Paragraph title = new Paragraph("Test Paper / 测试试卷", titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(20);
            document.add(title);

            // 智能处理内容
            String[] lines = cleanText.split("\n");
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 判断内容类型并选择合适的字体和格式
                Font selectedFont = normalFont;
                int alignment = Element.ALIGN_LEFT;
                float spacingAfter = 8f;

                // 检查是否是标题（通常较短且可能包含"题"、"试卷"等关键词）
                if (line.length() < 50 && (line.contains("试卷") || line.contains("题") ||
                    line.matches(".*[一二三四五六七八九十].*") || line.matches(".*第.*部分.*"))) {
                    selectedFont = titleFont;
                    alignment = Element.ALIGN_CENTER;
                    spacingAfter = 15f;
                }
                // 检查是否是选项（以A. B. C. D.开头）
                else if (line.matches("^\\s*[A-D][.、].*")) {
                    selectedFont = normalFont;
                    alignment = Element.ALIGN_LEFT;
                    spacingAfter = 5f;
                    line = "    " + line; // 添加缩进
                }
                // 检查是否是题目编号（以数字开头）
                else if (line.matches("^\\d+[.、].*")) {
                    selectedFont = new Font(Font.FontFamily.HELVETICA, 13, Font.BOLD);
                    alignment = Element.ALIGN_LEFT;
                    spacingAfter = 10f;
                }

                // 检查是否包含数学公式
                if (containsMathFormula(line)) {
                    line = processMathFormulas(line);
                    selectedFont = mathFont;
                }

                // 创建段落
                Paragraph para = new Paragraph(line, selectedFont);
                para.setAlignment(alignment);
                para.setSpacingAfter(spacingAfter);
                document.add(para);
            }

            // 添加生成信息
            Paragraph footer = new Paragraph("\n\nGenerated by Ultra Simple PDF Generator",
                new Font(Font.FontFamily.HELVETICA, 10, Font.ITALIC));
            footer.setAlignment(Element.ALIGN_CENTER);
            document.add(footer);

            document.close();

            byte[] pdfBytes = outputStream.toByteArray();
            log.info("PDF生成成功，大小: {} bytes", pdfBytes.length);

            return new ByteArrayResource(pdfBytes);

        } catch (Exception e) {
            log.error("PDF生成失败: {}", e.getMessage(), e);
            return createErrorPdf("PDF Generation Failed: " + e.getMessage());
        }
    }

    /**
     * 智能解析HTML内容
     */
    private static String cleanHtml(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "Empty Content";
        }

        StringBuilder result = new StringBuilder();

        // 按行处理HTML
        String[] lines = html.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            // 提取标题
            if (line.matches(".*<h[1-6][^>]*>.*</h[1-6]>.*")) {
                String title = extractTextBetweenTags(line, "h[1-6]");
                if (!title.isEmpty()) {
                    result.append("\n").append(title).append("\n");
                }
            }
            // 提取段落内容
            else if (line.contains("<p") || line.contains("<div")) {
                String text = stripHtmlTags(line);
                if (!text.isEmpty()) {
                    result.append(text).append("\n");
                }
            }
            // 处理选项
            else if (line.contains("option-item") || line.matches(".*[A-D]\\s*[.、].*")) {
                String option = stripHtmlTags(line);
                if (!option.isEmpty()) {
                    result.append("  ").append(option).append("\n");
                }
            }
            // 处理题目标题
            else if (line.contains("question-title")) {
                String questionText = stripHtmlTags(line);
                if (!questionText.isEmpty()) {
                    result.append("\n").append(questionText).append("\n");
                }
            }
            // 其他内容
            else {
                String text = stripHtmlTags(line);
                if (!text.isEmpty() && text.length() > 3) {
                    result.append(text).append("\n");
                }
            }
        }

        String finalResult = result.toString();

        // 解码HTML实体
        finalResult = finalResult.replace("&amp;", "&")
                                .replace("&lt;", "<")
                                .replace("&gt;", ">")
                                .replace("&quot;", "\"")
                                .replace("&nbsp;", " ")
                                .replace("&mdash;", "—")
                                .replace("&ldquo;", "\u201c")
                                .replace("&rdquo;", "\u201d");

        return finalResult.trim();
    }

    /**
     * 提取HTML标签之间的文本
     */
    private static String extractTextBetweenTags(String html, String tagPattern) {
        try {
            Pattern pattern = Pattern.compile("<" + tagPattern + "[^>]*>(.*?)</" + tagPattern.replaceAll("\\[.*\\]", "") + ">", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            java.util.regex.Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                return stripHtmlTags(matcher.group(1)).trim();
            }
        } catch (Exception e) {
            // 如果正则表达式失败，回退到简单处理
        }
        return stripHtmlTags(html);
    }

    /**
     * 移除HTML标签
     */
    private static String stripHtmlTags(String html) {
        if (html == null) return "";
        return HTML_TAG_PATTERN.matcher(html).replaceAll("").trim();
    }

    /**
     * 检查是否包含数学公式
     */
    private static boolean containsMathFormula(String text) {
        return text.contains("$") ||
               text.contains("\\") ||
               text.contains("π") ||
               text.contains("∫") ||
               text.contains("Σ") ||
               text.contains("α") ||
               text.contains("β") ||
               text.contains("γ");
    }

    /**
     * 处理数学公式
     */
    private static String processMathFormulas(String text) {
        // 移除LaTeX标记
        String processed = text.replace("$", "");

        // 转换常见的LaTeX符号
        processed = processed.replace("\\pi", "π")
                           .replace("\\alpha", "α")
                           .replace("\\beta", "β")
                           .replace("\\gamma", "γ")
                           .replace("\\delta", "δ")
                           .replace("\\theta", "θ")
                           .replace("\\lambda", "λ")
                           .replace("\\mu", "μ")
                           .replace("\\sigma", "σ")
                           .replace("\\phi", "φ")
                           .replace("\\omega", "ω")
                           .replace("\\infty", "∞")
                           .replace("\\sum", "Σ")
                           .replace("\\int", "∫")
                           .replace("\\sqrt", "√")
                           .replace("\\rightarrow", "→")
                           .replace("\\leftarrow", "←")
                           .replace("\\Rightarrow", "⇒")
                           .replace("\\Leftarrow", "⇐");

        // 处理分数
        processed = processed.replaceAll("\\\\frac\\{([^}]*)\\}\\{([^}]*)\\}", "($1)/($2)");

        // 处理上下标（简化处理）
        processed = processed.replaceAll("\\^\\{([^}]*)\\}", "^$1")
                           .replaceAll("_\\{([^}]*)\\}", "_$1");

        // 移除剩余的LaTeX命令
        processed = processed.replaceAll("\\\\[a-zA-Z]+\\{([^}]*)\\}", "$1")
                           .replaceAll("\\\\[a-zA-Z]+", "")
                           .replaceAll("\\{([^}]*)\\}", "$1");

        return processed;
    }

    /**
     * 创建错误PDF
     */
    private static Resource createErrorPdf(String errorMessage) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Document document = new Document(PageSize.A4);
            PdfWriter.getInstance(document, outputStream);
            document.open();

            Font font = new Font(Font.FontFamily.HELVETICA, 14, Font.NORMAL);

            Paragraph error = new Paragraph("PDF Generation Failed", font);
            error.setAlignment(Element.ALIGN_CENTER);
            error.setSpacingAfter(20);
            document.add(error);

            Paragraph details = new Paragraph("Error: " + errorMessage,
                new Font(Font.FontFamily.HELVETICA, 12, Font.NORMAL));
            details.setAlignment(Element.ALIGN_CENTER);
            document.add(details);

            document.close();
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (Exception e) {
            log.error("创建错误PDF失败: {}", e.getMessage());
            return new ByteArrayResource("PDF Generation Failed".getBytes(StandardCharsets.UTF_8));
        }
    }
}
