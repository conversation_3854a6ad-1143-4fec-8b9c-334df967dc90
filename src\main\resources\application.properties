server.port=8081
spring.thymeleaf.cache=false


spring.datasource.url=***************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Hilury157195!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.open-in-view=false

# Disable Hibernate Bean Validation to fix ValueExtractorManager error
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.validator.autoregister_listeners=false
spring.jpa.properties.javax.persistence.validation.mode=none

jwt.secret=F9A8C28B7E5D3F1A6E5D4C3B2A1F0E9D8C7B6A5F4E3D2C1B0A9G8H7I6J5K4L3M2N1
jwt.expiration=86400000

mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.edu.maizi_edu_sys.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.global-config.db-config.logic-delete-field=is_deleted
mybatis-plus.global-config.db-config.logic-delete-value=true
mybatis-plus.global-config.db-config.logic-not-delete-value=false

# Enable SQL logging for MyBatis-Plus
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
logging.level.com.baomidou.mybatisplus=DEBUG
logging.level.com.edu.maizi_edu_sys.repository=DEBUG

spring.web.resources.static-locations=classpath:/static/
spring.mvc.static-path-pattern=/static/**

spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB


user.avatar-path=uploads/avatars/
user.default-avatar=default-avatar.png
user.max-avatar-size=5242880
user.allowed-avatar-types=image/jpeg,image/png,image/gif

spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0
spring.redis.timeout=10000
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

file.upload.base-path=./uploads
file.upload.avatar.path=${file.upload.base-path}/avatars

bot.id=bot-20250507182807-dbmrx
bot.api-key=71c264ae-dc70-42b6-84c1-72b055ecfb8c

spring.main.allow-bean-definition-overriding=true

# Reduce logging verbosity - only show errors and warnings
logging.level.com.edu.maizi_edu_sys=DEBUG
logging.level.com.edu.maizi_edu_sys.service.impl.ChatServiceImpl=DEBUG
logging.level.com.edu.maizi_edu_sys.controller=DEBUG
logging.level.com.volcengine.ark=INFO

# Ensure log formatting includes thread information and full class names
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Reduce WebSocket debugging
logging.level.org.springframework.web.socket=WARN
logging.level.org.springframework.messaging=WARN

# Add more general application logging
logging.level.org.springframework.web=INFO
logging.level.root=INFO