2025-05-19 19:18:22.829 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 19:20:02.914 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=61, questionCount=null, includeShortAnswer=true)], title=生理学  专项练习, totalScore=59, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 19:20:03.264 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 1643. Earmarked: 0, General Pool (unique): 1643. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=5}
2025-05-19 19:20:03.610 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 1643 topics. IDs: [105741, 105742, 105743, 105744, 105745, 105746, 105747, 105748, 105749, 105750, 105752, 105756, 105760, 105764, 105769, 105771, 105774, 105778, 105785, 105790, 105795, 105802, 105808, 105825, 105830, 105843, 105846, 105851, 105854, 105860, 105866, 105882, 105893, 105902, 105910, 105915, 105923, 105930, 105934, 105941, 105996, 106003, 106009, 106012, 106016, 106030, 106045, 106057, 106067, 106079, 106091, 106097, 106101, 106107, 106111, 106121, 106129, 106137, 106146, 106155, 106289, 106293, 106298, 106312, 106321, 106322, 107035, 107039, 107045, 107048, 107057, 107060, 107062, 107071, 107075, 107078, 107081, 107086, 107089, 107093, 107097, 107105, 107112, 107118, 107123, 107129, 107132, 107140, 107145, 107153, 107157, 107164, 107174, 107180, 107188, 107194, 107198, 107201, 107206, 107210, 107217, 107225, 107229, 107238, 107248, 107370, 107383, 107393, 107400, 107405, 107412, 107417, 107425, 107431, 107437, 107441, 107455, 107462, 107478, 107487, 107493, 107499, 107506, 107515, 107523, 107529, 107534, 107538, 107543, 107548, 107556, 107562, 107568, 107573, 107582, 107601, 107628, 107642, 107655, 107661, 107666, 107672, 107681, 107689, 107697, 107707, 107715, 107724, 107734, 107746, 107756, 107762, 107766, 107773, 107774, 107775, 107776, 107780, 107796, 107845, 107863, 107887, 107936, 107938, 107943, 107947, 107987, 108003, 108010, 108016, 108022, 108025, 108028, 108034, 108036, 108039, 108043, 108046, 108049, 108051, 108053, 108054, 108057, 108061, 108065, 108070, 108072, 108073, 108075, 108079, 108088, 108091, 108095, 108101, 108110, 108114, 108120, 108122, 108125, 108130, 108133, 108139, 108144, 108149, 108155, 108159, 108163, 108168, 108174, 108179, 108182, 108185, 108188, 108191, 108193, 108195, 108198, 108201, 108206, 108208, 108210, 108211, 108214, 108216, 108217, 108218, 108219, 108651, 108658, 108666, 108673, 108682, 108687, 108693, 108701, 108706, 108711, 108716, 108721, 108723, 108728, 108730, 108734, 108735, 108738, 108739, 108744, 108746, 108747, 108750, 108752, 108756, 108759, 108760, 108764, 108767, 108770, 108772, 108776, 108778, 108779, 108781, 108783, 108790, 108795, 108796, 108801, 108808, 108809, 108813, 108816, 108826, 108831, 108838, 108850, 108866, 108882, 108916, 108926, 108933, 108962, 108965, 108967, 108968, 108972, 108975, 108976, 108981, 108985, 108990, 108994, 108996, 109000, 109004, 109007, 109010, 109012, 109016, 109017, 109024, 109035, 109040, 109044, 109048, 109053, 109056, 109059, 109070, 109073, 109076, 109079, 109081, 109085, 109088, 109092, 109095, 109098, 109104, 109107, 109112, 109116, 109122, 109130, 109157, 109165, 109170, 109176, 109183, 109210, 109217, 109224, 109232, 109240, 109258, 109261, 109266, 109271, 109279, 109317, 109325, 109328, 109334, 109340, 109354, 109361, 109371, 109380, 109388, 109439, 109442, 109448, 109451, 109455, 109459, 109463, 109467, 109473, 109476, 109480, 109487, 109493, 109497, 109501, 109507, 109511, 109515, 109521, 109526, 109532, 109538, 109546, 109550, 109552, 109557, 109562, 109564, 109569, 109577, 109586, 109593, 109601, 109604, 109611, 109620, 110281, 110291, 110296, 110301, 110306, 110309, 110312, 110316, 110321, 110327, 110331, 110335, 110338, 110347, 110362, 110366, 110372, 110378, 110383, 110389, 110395, 110400, 110407, 110410, 110423, 110431, 110437, 110444, 110452, 110478, 110483, 110487, 110495, 110500, 110505, 110515, 110526, 110541, 110547, 110567, 110575, 110582, 110603, 110612, 110625, 110635, 110644, 110654, 110666, 110681, 110689, 110700, 110720, 110728, 110735, 110742, 110752, 110760, 110769, 110780, 110785, 110795, 110804, 110813, 110824, 110830, 110838, 110845, 110852, 110860, 110868, 110875, 110883, 110890, 110896, 110899, 110906, 110911, 110914, 110923, 110926, 110931, 110937, 110941, 110947, 110951, 110956, 110962, 110968, 111005, 111011, 111023, 111029, 111036, 111042, 111056, 111062, 111069, 111073, 111080, 111083, 111089, 111098, 111104, 111110, 111115, 111120, 111126, 111133, 111139, 111144, 111149, 111157, 111163, 111166, 111171, 111176, 111178, 111183, 111189, 111194, 111198, 111205, 111211, 111225, 111233, 111240, 111246, 111259, 111263, 111268, 111272, 111276, 111281, 111287, 111292, 111300, 111311, 111315, 111320, 111325, 111331, 111334, 111336, 111339, 111343, 111345, 111352, 111355, 111359, 111360, 111362, 111365, 116112, 116710, 156165, 156166, 156167, 156168, 156169, 156170, 156171, 156181, 156183, 156187, 156189, 156190, 156193, 156196, 156198, 156200, 156202, 156207, 156223, 156227, 156229, 156233, 156237, 156240, 156245, 156564, 156574, 156582, 156586, 156592, 156595, 156602, 156605, 156612, 156618, 156620, 156753, 156756, 156761, 156763, 156765, 156767, 156768, 156772, 156776, 156782, 156792, 156800, 156805, 156808, 156810, 156813, 156816, 156818, 156819, 156820, 156821, 156822, 156823, 156825, 156826, 156827, 156828, 156829, 156830, 156831, 156833, 156834, 156835, 156836, 156837, 156838, 156839, 156840, 156842, 156843, 156844, 156845, 156847, 156849, 156851, 156852, 156853, 156854, 156857, 156859, 156861, 156862, 156863, 156864, 156866, 156867, 156868, 156869, 156871, 156872, 156874, 156875, 156876, 156877, 156878, 156882, 156883, 156885, 156887, 156889, 156890, 156893, 156894, 156895, 156897, 156899, 156900, 156901, 156904, 156905, 156908, 156910, 156913, 156914, 156916, 156917, 156918, 156920, 156922, 156924, 156925, 156926, 156928, 156929, 156933, 156934, 156935, 156951, 156953, 156955, 156956, 156958, 156959, 156961, 156962, 156963, 156965, 156966, 156967, 156969, 156970, 156971, 156973, 156975, 156977, 156981, 156982, 156983, 156984, 156985, 156986, 156987, 156988, 156989, 156990, 156993, 156996, 157001, 157003, 157005, 157006, 157007, 157008, 157009, 157010, 157011, 157012, 157013, 157014, 157015, 157016, 157017, 157018, 157019, 157020, 157021, 157022, 157023, 157025, 157027, 157028, 157029, 157031, 157032, 157033, 157035, 157037, 157038, 157039, 157041, 157042, 157043, 157044, 157045, 157047, 157048, 157049, 157051, 157052, 157053, 157055, 157056, 157057, 157058, 157060, 157061, 157063, 157064, 157065, 157066, 157067, 157069, 157071, 157072, 157073, 157074, 157075, 157076, 157077, 157078, 157079, 157080, 157081, 157082, 157084, 157085, 157086, 157087, 157089, 157090, 157091, 157092, 157094, 157095, 157096, 157098, 157100, 157101, 157102, 157104, 157106, 157107, 157108, 157109, 157111, 157113, 157114, 157116, 157117, 157118, 157119, 157120, 157123, 157125, 157127, 157129, 157130, 157131, 157136, 157138, 157140, 157142, 157144, 157145, 157146, 157147, 157149, 157150, 157151, 157152, 157153, 157154, 157155, 157156, 157157, 157158, 157159, 157160, 157161, 157162, 157163, 157164, 157165, 157166, 157167, 157168, 157169, 157170, 157171, 157172, 157173, 157174, 157176, 157177, 157178, 157179, 157180, 157181, 157182, 157183, 157184, 157185, 157186, 157187, 157189, 157191, 157192, 157194, 157195, 157196, 157197, 157198, 157199, 157200, 157201, 157202, 157203, 157204, 157205, 157206, 157207, 157208, 157209, 157210, 157211, 157212, 157213, 157214, 157215, 157216, 157217, 157218, 157219, 157220, 157221, 157222, 157223, 157224, 157225, 157226, 157228, 157229, 157230, 157231, 157233, 157234, 157236, 157258, 157259, 157260, 157261, 157262, 157263, 157264, 157265, 157266, 157267, 157268, 157269, 157270, 157271, 157272, 157273, 157274, 157275, 157394, 157401, 157407, 157416, 157420, 157556, 157560, 157562, 157566, 157568, 157572, 157575, 157576, 157578, 157580, 157582, 157584, 157585, 157588, 157590, 157592, 157593, 157596, 157598, 157599, 157600, 157602, 157604, 157605, 157607, 157609, 157611, 157612, 157615, 157616, 157619, 157620, 157622, 157624, 157625, 157629, 157630, 157633, 157634, 157636, 157638, 157639, 157641, 157643, 157644, 157648, 157649, 157652, 157656, 157658, 157663, 157667, 157671, 157675, 157676, 157681, 157684, 157686, 157689, 157690, 157692, 157694, 157696, 157697, 157698, 157700, 157701, 157703, 157705, 157706, 157708, 157709, 157710, 157712, 157713, 157715, 157717, 157719, 157720, 157722, 157723, 157725, 157730, 157734, 157735, 157739, 157741, 157743, 157745, 157747, 157749, 157751, 157752, 157753, 157755, 157757, 157759, 157760, 157764, 157766, 157769, 157770, 157773, 157777, 157788, 157791, 157794, 157797, 157798, 157807, 157811, 157812, 157816, 157818, 157821, 157824, 157826, 157829, 157833, 157836, 157839, 157840, 157843, 157845, 157847, 157856, 157862, 157868, 157869, 157876, 157882, 157883, 157887, 157889, 157893, 157897, 157898, 157905, 157911, 157912, 157918, 157933, 157942, 157945, 157946, 157950, 157953, 157957, 157962, 157965, 157968, 157969, 157973, 157975, 157978, 157983, 157984, 157989, 157990, 157993, 157997, 157999, 158003, 158004, 158009, 158020, 158026, 158033, 158037, 158042, 158046, 158050, 158051, 158054, 158058, 158059, 158063, 158066, 158068, 158071, 158072, 158074, 158077, 158079, 158083, 158085, 158091, 158094, 158095, 158096, 158100, 158103, 158108, 158109, 158111, 158114, 158118, 158120, 158121, 158123, 158127, 158128, 158134, 158135, 158136, 158141, 158142, 158145, 158146, 158148, 158152, 158153, 158154, 158159, 158163, 158164, 158165, 158171, 158172, 158176, 158179, 158186, 158187, 158195, 158196, 158202, 158208, 158212, 158215, 158217, 158219, 158222, 158227, 158233, 158237, 158238, 158241, 158243, 158245, 158247, 158249, 158253, 158255, 158257, 158260, 158261, 158263, 158265, 158267, 158268, 158270, 158272, 158274, 158275, 158276, 158278, 158281, 158283, 158284, 158288, 158289, 158293, 158300, 158305, 158306, 158312, 158314, 158318, 158320, 158326, 158327, 158333, 158340, 158344, 158347, 158354, 158360, 158361, 158366, 158368, 158379, 158385, 158389, 158392, 158399, 158401, 158406, 158409, 158414, 158415, 158423, 158430, 158436, 158450, 158452, 158456, 158460, 158461, 158466, 158470, 158474, 158478, 158481, 158485, 158491, 158503, 158508, 158510, 158514, 158517, 158520, 158528, 158534, 158538, 158544, 158546, 158551, 158558, 158564, 158578, 158598, 158600, 158604, 158607, 158611, 158613, 158616, 158620, 158622, 158624, 158628, 158629, 158631, 158634, 158638, 158640, 158642, 158645, 158647, 158648, 158650, 158653, 158656, 158657, 158659, 158661, 158663, 158666, 158668, 158675, 158682, 158685, 158691, 158696, 158701, 158703, 158707, 158711, 158720, 158722, 158770, 158772, 158775, 158776, 158781, 158783, 158787, 158789, 158794, 158797, 158802, 158806, 158810, 158813, 158816, 158818, 158822, 158824, 158828, 158833, 158860, 158861, 158862, 158863, 158864, 158865, 158866, 158867, 158868, 158869, 158870, 158871, 158872, 158873, 158874, 158875, 158876, 158877, 158878, 158879, 158882, 158902, 158906, 158910, 158913, 158915, 158923, 158926, 158928, 158930, 158932, 158935, 158938, 158940, 158947, 158948, 158951, 158954, 158957, 158959, 158962, 158964, 158969, 158974, 158978, 158982, 159004, 159008, 159011, 159013, 159019, 159025, 159030, 159033, 159036, 159038, 159040, 159042, 159044, 159046, 159048, 159050, 159052, 159054, 159055, 159061, 159064, 159069, 159073, 159077, 159079, 159082, 159085, 159087, 159089, 159093, 159095, 159099, 159102, 159106, 159110, 159116, 159117, 159121, 159131, 159132, 159138, 159145, 159151, 159154, 159158, 159166, 159170, 159176, 159180, 159183, 159187, 159190, 159193, 159195, 159197, 159198, 159200, 159201, 159203, 159205, 159208, 159209, 159212, 159214, 159216, 159218, 159220, 159221, 159222, 159224, 159225, 159226, 159227, 159228, 159229, 159230, 159231, 159232, 159233, 159234, 159235, 159236, 159237, 159238, 159239, 159240, 159241, 159242, 159243, 159244, 159245, 159246, 159247, 159263, 159264, 159265, 159266, 159549, 159553, 159572, 159574, 159593, 159596, 159601, 159612, 159619, 159624, 159633, 159635, 159639, 159649, 159654, 159665, 159674, 159683, 159695, 159705, 159716, 159722, 159725, 159726, 159727, 159728, 159730, 159734, 159736, 159737, 159739, 159742, 159744, 159747, 159752, 159756, 159759, 159761, 159775, 159779, 159782, 159786, 159791, 159794, 159795, 159799, 159800, 159802, 159804, 159806, 159807, 159808, 159810, 159812, 159814, 159819, 159820, 159821, 159823, 159824, 159826, 159897, 159898, 159901, 159906, 159909, 159913, 159916, 159918, 159921, 159923, 159926, 159928, 159932, 159933, 159934, 159938, 159944, 159948, 159949, 159953, 159955, 159959, 159961, 159965, 159969, 159971, 159977, 159979, 159981, 159985, 159988, 159994, 159997, 160003, 160006, 160007, 160010, 160014, 160017, 160019, 160021, 160023, 160027, 160030, 160033, 160035, 160038, 160040, 160043, 160047, 160052, 160053, 160055, 160059, 160068, 160072, 160075, 160080, 160084, 160088, 160104, 160109, 160112, 160114, 160117, 160119, 160121, 160123, 160126, 160132, 160136, 160141, 160143, 160164, 160170, 160174, 160187, 160194, 160197, 160200, 160201, 160206, 160209, 160213, 160217, 160220, 160223, 160226, 160233, 160238, 160241, 160245, 160250, 160252, 160256, 160262, 160266, 160272, 160276, 160278, 160284, 160289, 160301, 160302, 160309, 160312, 160329, 160333, 160334, 160335, 160339, 160344, 160346, 160353, 160356, 160361, 160364, 160366, 160369, 160371, 160376, 160377, 160378, 160384, 160387, 160390, 160393, 160395, 160396, 160399, 160402, 160403, 160407, 160410, 160412, 160417, 160418, 160422, 160424, 160426, 160428, 160429, 160432, 160434]
2025-05-19 19:20:03.613 [http-nio-8081-exec-5] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 19:20:03.613 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 1643 topics available for GA (input size was 1643). MinReuseIntervalDays: null
2025-05-19 19:20:03.613 [http-nio-8081-exec-5] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 19:20:03.614 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 1643 topics remain.
2025-05-19 19:20:03.614 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 1643 candidate topics...
2025-05-19 19:20:03.615 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 19:20:03.615 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 19:20:03.942 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 0.7436311216429701, Type Distribution = 2532
2025-05-19 19:20:04.261 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 0.7436311216429701, Type Distribution = 2532
2025-05-19 19:20:04.359 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 0.7436311216429701, Type Distribution = 2532
2025-05-19 19:20:04.422 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 0.7436311216429701, Type Distribution = 2532
2025-05-19 19:20:04.422 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 0.7436311216429701.
2025-05-19 19:20:04.423 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 808ms. Best solution fitness: {:.4f}, Selected 0.7436311216429701 topics with total score: 844.
2025-05-19 19:20:04.428 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 844 topics with type distribution: {singleChoice=543, judgment=117, multipleChoice=177, shortAnswer=7}
2025-05-19 19:20:04.428 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 59, 同时保持题型分布
2025-05-19 19:20:04.429 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {singleChoice=543, judgment=117, multipleChoice=177, shortAnswer=7}
2025-05-19 19:20:04.441 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 2532, Target score: 59. Number of topics: 844
2025-05-19 19:20:04.443 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=543, judgment=117, multipleChoice=177, shortAnswer=7}, Target type counts: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=5}
2025-05-19 19:20:04.443 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Starting strict type-preserving optimization
2025-05-19 19:20:04.444 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Initial selection with strict type constraints: 0 topics, score=0, type distribution={SHORT_ANSWER=0, JUDGMENT=0, SINGLE_CHOICE=0, FILL_IN_BLANKS=0, MULTIPLE_CHOICE=0}
2025-05-19 19:20:04.446 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: After type-preserving adjustment: score=0 (target=59), type distribution={SHORT_ANSWER=0, JUDGMENT=0, SINGLE_CHOICE=0, FILL_IN_BLANKS=0, MULTIPLE_CHOICE=0}
2025-05-19 19:20:04.448 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=543, judgment=117, multipleChoice=177, shortAnswer=7}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 19:20:04.448 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 19:20:04.450 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-19 19:20:04.450 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 5 topics needed
2025-05-19 19:20:04.450 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-19 19:20:04.451 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 19:20:04.451 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 19:20:04.454 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 2532 but target is 59, attempting to adjust scores...
2025-05-19 19:20:04.455 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 2532, Target score: 59. Number of topics: 844
2025-05-19 19:20:04.455 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (2532) is greater than target (59). Attempting to find a subset that sums to target score.
2025-05-19 19:20:04.462 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=2532
2025-05-19 19:20:04.463 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=844
2025-05-19 19:20:04.463 [http-nio-8081-exec-5] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 844 topics.
2025-05-19 19:20:05.074 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 51, Target score: 59. Number of topics: 17
2025-05-19 19:29:43.624 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 19:30:06.682 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=61, questionCount=null, includeShortAnswer=true)], title=生理学  专项练习, totalScore=53, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 19:30:07.012 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 1643. Earmarked: 0, General Pool (unique): 1643. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-19 19:30:07.113 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 1643 topics. IDs: [105741, 105742, 105743, 105744, 105745, 105746, 105747, 105748, 105749, 105750, 105752, 105756, 105760, 105764, 105769, 105771, 105774, 105778, 105785, 105790, 105795, 105802, 105808, 105825, 105830, 105843, 105846, 105851, 105854, 105860, 105866, 105882, 105893, 105902, 105910, 105915, 105923, 105930, 105934, 105941, 105996, 106003, 106009, 106012, 106016, 106030, 106045, 106057, 106067, 106079, 106091, 106097, 106101, 106107, 106111, 106121, 106129, 106137, 106146, 106155, 106289, 106293, 106298, 106312, 106321, 106322, 107035, 107039, 107045, 107048, 107057, 107060, 107062, 107071, 107075, 107078, 107081, 107086, 107089, 107093, 107097, 107105, 107112, 107118, 107123, 107129, 107132, 107140, 107145, 107153, 107157, 107164, 107174, 107180, 107188, 107194, 107198, 107201, 107206, 107210, 107217, 107225, 107229, 107238, 107248, 107370, 107383, 107393, 107400, 107405, 107412, 107417, 107425, 107431, 107437, 107441, 107455, 107462, 107478, 107487, 107493, 107499, 107506, 107515, 107523, 107529, 107534, 107538, 107543, 107548, 107556, 107562, 107568, 107573, 107582, 107601, 107628, 107642, 107655, 107661, 107666, 107672, 107681, 107689, 107697, 107707, 107715, 107724, 107734, 107746, 107756, 107762, 107766, 107773, 107774, 107775, 107776, 107780, 107796, 107845, 107863, 107887, 107936, 107938, 107943, 107947, 107987, 108003, 108010, 108016, 108022, 108025, 108028, 108034, 108036, 108039, 108043, 108046, 108049, 108051, 108053, 108054, 108057, 108061, 108065, 108070, 108072, 108073, 108075, 108079, 108088, 108091, 108095, 108101, 108110, 108114, 108120, 108122, 108125, 108130, 108133, 108139, 108144, 108149, 108155, 108159, 108163, 108168, 108174, 108179, 108182, 108185, 108188, 108191, 108193, 108195, 108198, 108201, 108206, 108208, 108210, 108211, 108214, 108216, 108217, 108218, 108219, 108651, 108658, 108666, 108673, 108682, 108687, 108693, 108701, 108706, 108711, 108716, 108721, 108723, 108728, 108730, 108734, 108735, 108738, 108739, 108744, 108746, 108747, 108750, 108752, 108756, 108759, 108760, 108764, 108767, 108770, 108772, 108776, 108778, 108779, 108781, 108783, 108790, 108795, 108796, 108801, 108808, 108809, 108813, 108816, 108826, 108831, 108838, 108850, 108866, 108882, 108916, 108926, 108933, 108962, 108965, 108967, 108968, 108972, 108975, 108976, 108981, 108985, 108990, 108994, 108996, 109000, 109004, 109007, 109010, 109012, 109016, 109017, 109024, 109035, 109040, 109044, 109048, 109053, 109056, 109059, 109070, 109073, 109076, 109079, 109081, 109085, 109088, 109092, 109095, 109098, 109104, 109107, 109112, 109116, 109122, 109130, 109157, 109165, 109170, 109176, 109183, 109210, 109217, 109224, 109232, 109240, 109258, 109261, 109266, 109271, 109279, 109317, 109325, 109328, 109334, 109340, 109354, 109361, 109371, 109380, 109388, 109439, 109442, 109448, 109451, 109455, 109459, 109463, 109467, 109473, 109476, 109480, 109487, 109493, 109497, 109501, 109507, 109511, 109515, 109521, 109526, 109532, 109538, 109546, 109550, 109552, 109557, 109562, 109564, 109569, 109577, 109586, 109593, 109601, 109604, 109611, 109620, 110281, 110291, 110296, 110301, 110306, 110309, 110312, 110316, 110321, 110327, 110331, 110335, 110338, 110347, 110362, 110366, 110372, 110378, 110383, 110389, 110395, 110400, 110407, 110410, 110423, 110431, 110437, 110444, 110452, 110478, 110483, 110487, 110495, 110500, 110505, 110515, 110526, 110541, 110547, 110567, 110575, 110582, 110603, 110612, 110625, 110635, 110644, 110654, 110666, 110681, 110689, 110700, 110720, 110728, 110735, 110742, 110752, 110760, 110769, 110780, 110785, 110795, 110804, 110813, 110824, 110830, 110838, 110845, 110852, 110860, 110868, 110875, 110883, 110890, 110896, 110899, 110906, 110911, 110914, 110923, 110926, 110931, 110937, 110941, 110947, 110951, 110956, 110962, 110968, 111005, 111011, 111023, 111029, 111036, 111042, 111056, 111062, 111069, 111073, 111080, 111083, 111089, 111098, 111104, 111110, 111115, 111120, 111126, 111133, 111139, 111144, 111149, 111157, 111163, 111166, 111171, 111176, 111178, 111183, 111189, 111194, 111198, 111205, 111211, 111225, 111233, 111240, 111246, 111259, 111263, 111268, 111272, 111276, 111281, 111287, 111292, 111300, 111311, 111315, 111320, 111325, 111331, 111334, 111336, 111339, 111343, 111345, 111352, 111355, 111359, 111360, 111362, 111365, 116112, 116710, 156165, 156166, 156167, 156168, 156169, 156170, 156171, 156181, 156183, 156187, 156189, 156190, 156193, 156196, 156198, 156200, 156202, 156207, 156223, 156227, 156229, 156233, 156237, 156240, 156245, 156564, 156574, 156582, 156586, 156592, 156595, 156602, 156605, 156612, 156618, 156620, 156753, 156756, 156761, 156763, 156765, 156767, 156768, 156772, 156776, 156782, 156792, 156800, 156805, 156808, 156810, 156813, 156816, 156818, 156819, 156820, 156821, 156822, 156823, 156825, 156826, 156827, 156828, 156829, 156830, 156831, 156833, 156834, 156835, 156836, 156837, 156838, 156839, 156840, 156842, 156843, 156844, 156845, 156847, 156849, 156851, 156852, 156853, 156854, 156857, 156859, 156861, 156862, 156863, 156864, 156866, 156867, 156868, 156869, 156871, 156872, 156874, 156875, 156876, 156877, 156878, 156882, 156883, 156885, 156887, 156889, 156890, 156893, 156894, 156895, 156897, 156899, 156900, 156901, 156904, 156905, 156908, 156910, 156913, 156914, 156916, 156917, 156918, 156920, 156922, 156924, 156925, 156926, 156928, 156929, 156933, 156934, 156935, 156951, 156953, 156955, 156956, 156958, 156959, 156961, 156962, 156963, 156965, 156966, 156967, 156969, 156970, 156971, 156973, 156975, 156977, 156981, 156982, 156983, 156984, 156985, 156986, 156987, 156988, 156989, 156990, 156993, 156996, 157001, 157003, 157005, 157006, 157007, 157008, 157009, 157010, 157011, 157012, 157013, 157014, 157015, 157016, 157017, 157018, 157019, 157020, 157021, 157022, 157023, 157025, 157027, 157028, 157029, 157031, 157032, 157033, 157035, 157037, 157038, 157039, 157041, 157042, 157043, 157044, 157045, 157047, 157048, 157049, 157051, 157052, 157053, 157055, 157056, 157057, 157058, 157060, 157061, 157063, 157064, 157065, 157066, 157067, 157069, 157071, 157072, 157073, 157074, 157075, 157076, 157077, 157078, 157079, 157080, 157081, 157082, 157084, 157085, 157086, 157087, 157089, 157090, 157091, 157092, 157094, 157095, 157096, 157098, 157100, 157101, 157102, 157104, 157106, 157107, 157108, 157109, 157111, 157113, 157114, 157116, 157117, 157118, 157119, 157120, 157123, 157125, 157127, 157129, 157130, 157131, 157136, 157138, 157140, 157142, 157144, 157145, 157146, 157147, 157149, 157150, 157151, 157152, 157153, 157154, 157155, 157156, 157157, 157158, 157159, 157160, 157161, 157162, 157163, 157164, 157165, 157166, 157167, 157168, 157169, 157170, 157171, 157172, 157173, 157174, 157176, 157177, 157178, 157179, 157180, 157181, 157182, 157183, 157184, 157185, 157186, 157187, 157189, 157191, 157192, 157194, 157195, 157196, 157197, 157198, 157199, 157200, 157201, 157202, 157203, 157204, 157205, 157206, 157207, 157208, 157209, 157210, 157211, 157212, 157213, 157214, 157215, 157216, 157217, 157218, 157219, 157220, 157221, 157222, 157223, 157224, 157225, 157226, 157228, 157229, 157230, 157231, 157233, 157234, 157236, 157258, 157259, 157260, 157261, 157262, 157263, 157264, 157265, 157266, 157267, 157268, 157269, 157270, 157271, 157272, 157273, 157274, 157275, 157394, 157401, 157407, 157416, 157420, 157556, 157560, 157562, 157566, 157568, 157572, 157575, 157576, 157578, 157580, 157582, 157584, 157585, 157588, 157590, 157592, 157593, 157596, 157598, 157599, 157600, 157602, 157604, 157605, 157607, 157609, 157611, 157612, 157615, 157616, 157619, 157620, 157622, 157624, 157625, 157629, 157630, 157633, 157634, 157636, 157638, 157639, 157641, 157643, 157644, 157648, 157649, 157652, 157656, 157658, 157663, 157667, 157671, 157675, 157676, 157681, 157684, 157686, 157689, 157690, 157692, 157694, 157696, 157697, 157698, 157700, 157701, 157703, 157705, 157706, 157708, 157709, 157710, 157712, 157713, 157715, 157717, 157719, 157720, 157722, 157723, 157725, 157730, 157734, 157735, 157739, 157741, 157743, 157745, 157747, 157749, 157751, 157752, 157753, 157755, 157757, 157759, 157760, 157764, 157766, 157769, 157770, 157773, 157777, 157788, 157791, 157794, 157797, 157798, 157807, 157811, 157812, 157816, 157818, 157821, 157824, 157826, 157829, 157833, 157836, 157839, 157840, 157843, 157845, 157847, 157856, 157862, 157868, 157869, 157876, 157882, 157883, 157887, 157889, 157893, 157897, 157898, 157905, 157911, 157912, 157918, 157933, 157942, 157945, 157946, 157950, 157953, 157957, 157962, 157965, 157968, 157969, 157973, 157975, 157978, 157983, 157984, 157989, 157990, 157993, 157997, 157999, 158003, 158004, 158009, 158020, 158026, 158033, 158037, 158042, 158046, 158050, 158051, 158054, 158058, 158059, 158063, 158066, 158068, 158071, 158072, 158074, 158077, 158079, 158083, 158085, 158091, 158094, 158095, 158096, 158100, 158103, 158108, 158109, 158111, 158114, 158118, 158120, 158121, 158123, 158127, 158128, 158134, 158135, 158136, 158141, 158142, 158145, 158146, 158148, 158152, 158153, 158154, 158159, 158163, 158164, 158165, 158171, 158172, 158176, 158179, 158186, 158187, 158195, 158196, 158202, 158208, 158212, 158215, 158217, 158219, 158222, 158227, 158233, 158237, 158238, 158241, 158243, 158245, 158247, 158249, 158253, 158255, 158257, 158260, 158261, 158263, 158265, 158267, 158268, 158270, 158272, 158274, 158275, 158276, 158278, 158281, 158283, 158284, 158288, 158289, 158293, 158300, 158305, 158306, 158312, 158314, 158318, 158320, 158326, 158327, 158333, 158340, 158344, 158347, 158354, 158360, 158361, 158366, 158368, 158379, 158385, 158389, 158392, 158399, 158401, 158406, 158409, 158414, 158415, 158423, 158430, 158436, 158450, 158452, 158456, 158460, 158461, 158466, 158470, 158474, 158478, 158481, 158485, 158491, 158503, 158508, 158510, 158514, 158517, 158520, 158528, 158534, 158538, 158544, 158546, 158551, 158558, 158564, 158578, 158598, 158600, 158604, 158607, 158611, 158613, 158616, 158620, 158622, 158624, 158628, 158629, 158631, 158634, 158638, 158640, 158642, 158645, 158647, 158648, 158650, 158653, 158656, 158657, 158659, 158661, 158663, 158666, 158668, 158675, 158682, 158685, 158691, 158696, 158701, 158703, 158707, 158711, 158720, 158722, 158770, 158772, 158775, 158776, 158781, 158783, 158787, 158789, 158794, 158797, 158802, 158806, 158810, 158813, 158816, 158818, 158822, 158824, 158828, 158833, 158860, 158861, 158862, 158863, 158864, 158865, 158866, 158867, 158868, 158869, 158870, 158871, 158872, 158873, 158874, 158875, 158876, 158877, 158878, 158879, 158882, 158902, 158906, 158910, 158913, 158915, 158923, 158926, 158928, 158930, 158932, 158935, 158938, 158940, 158947, 158948, 158951, 158954, 158957, 158959, 158962, 158964, 158969, 158974, 158978, 158982, 159004, 159008, 159011, 159013, 159019, 159025, 159030, 159033, 159036, 159038, 159040, 159042, 159044, 159046, 159048, 159050, 159052, 159054, 159055, 159061, 159064, 159069, 159073, 159077, 159079, 159082, 159085, 159087, 159089, 159093, 159095, 159099, 159102, 159106, 159110, 159116, 159117, 159121, 159131, 159132, 159138, 159145, 159151, 159154, 159158, 159166, 159170, 159176, 159180, 159183, 159187, 159190, 159193, 159195, 159197, 159198, 159200, 159201, 159203, 159205, 159208, 159209, 159212, 159214, 159216, 159218, 159220, 159221, 159222, 159224, 159225, 159226, 159227, 159228, 159229, 159230, 159231, 159232, 159233, 159234, 159235, 159236, 159237, 159238, 159239, 159240, 159241, 159242, 159243, 159244, 159245, 159246, 159247, 159263, 159264, 159265, 159266, 159549, 159553, 159572, 159574, 159593, 159596, 159601, 159612, 159619, 159624, 159633, 159635, 159639, 159649, 159654, 159665, 159674, 159683, 159695, 159705, 159716, 159722, 159725, 159726, 159727, 159728, 159730, 159734, 159736, 159737, 159739, 159742, 159744, 159747, 159752, 159756, 159759, 159761, 159775, 159779, 159782, 159786, 159791, 159794, 159795, 159799, 159800, 159802, 159804, 159806, 159807, 159808, 159810, 159812, 159814, 159819, 159820, 159821, 159823, 159824, 159826, 159897, 159898, 159901, 159906, 159909, 159913, 159916, 159918, 159921, 159923, 159926, 159928, 159932, 159933, 159934, 159938, 159944, 159948, 159949, 159953, 159955, 159959, 159961, 159965, 159969, 159971, 159977, 159979, 159981, 159985, 159988, 159994, 159997, 160003, 160006, 160007, 160010, 160014, 160017, 160019, 160021, 160023, 160027, 160030, 160033, 160035, 160038, 160040, 160043, 160047, 160052, 160053, 160055, 160059, 160068, 160072, 160075, 160080, 160084, 160088, 160104, 160109, 160112, 160114, 160117, 160119, 160121, 160123, 160126, 160132, 160136, 160141, 160143, 160164, 160170, 160174, 160187, 160194, 160197, 160200, 160201, 160206, 160209, 160213, 160217, 160220, 160223, 160226, 160233, 160238, 160241, 160245, 160250, 160252, 160256, 160262, 160266, 160272, 160276, 160278, 160284, 160289, 160301, 160302, 160309, 160312, 160329, 160333, 160334, 160335, 160339, 160344, 160346, 160353, 160356, 160361, 160364, 160366, 160369, 160371, 160376, 160377, 160378, 160384, 160387, 160390, 160393, 160395, 160396, 160399, 160402, 160403, 160407, 160410, 160412, 160417, 160418, 160422, 160424, 160426, 160428, 160429, 160432, 160434]
2025-05-19 19:30:07.114 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 19:30:07.114 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 1643 topics available for GA (input size was 1643). MinReuseIntervalDays: null
2025-05-19 19:30:07.115 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 19:30:07.115 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 1643 topics remain.
2025-05-19 19:30:07.115 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 1643 candidate topics...
2025-05-19 19:30:07.117 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 19:30:07.117 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 19:30:07.401 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 0.7450659983291563, Type Distribution = 2394
2025-05-19 19:30:07.777 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 0.7450659983291563, Type Distribution = 2394
2025-05-19 19:30:07.944 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 0.7450659983291563, Type Distribution = 2394
2025-05-19 19:30:08.082 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 0.7450659983291563, Type Distribution = 2394
2025-05-19 19:30:08.082 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 0.7450659983291563.
2025-05-19 19:30:08.083 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 967ms. Best solution fitness: {:.4f}, Selected 0.7450659983291563 topics with total score: 798.
2025-05-19 19:30:08.088 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 798 topics with type distribution: {singleChoice=499, judgment=113, multipleChoice=177, shortAnswer=9}
2025-05-19 19:30:08.089 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 53, 同时保持题型分布
2025-05-19 19:30:08.090 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {singleChoice=499, judgment=113, multipleChoice=177, shortAnswer=9}
2025-05-19 19:30:08.097 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 2394, Target score: 53. Number of topics: 798
2025-05-19 19:30:08.099 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=499, judgment=113, multipleChoice=177, shortAnswer=9}, Target type counts: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-19 19:30:08.099 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Starting strict type-preserving optimization
2025-05-19 19:30:08.101 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Initial selection with strict type constraints: 0 topics, score=0, type distribution={SHORT_ANSWER=0, JUDGMENT=0, SINGLE_CHOICE=0, FILL_IN_BLANKS=0, MULTIPLE_CHOICE=0}
2025-05-19 19:30:08.102 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: After type-preserving adjustment: score=0 (target=53), type distribution={SHORT_ANSWER=0, JUDGMENT=0, SINGLE_CHOICE=0, FILL_IN_BLANKS=0, MULTIPLE_CHOICE=0}
2025-05-19 19:30:08.104 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=499, judgment=113, multipleChoice=177, shortAnswer=9}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 19:30:08.104 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 19:30:08.105 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-19 19:30:08.105 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 3 topics needed
2025-05-19 19:30:08.106 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-19 19:30:08.106 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 19:30:08.106 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 19:30:08.107 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 2394 but target is 53, attempting to adjust scores...
2025-05-19 19:30:08.109 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 2394, Target score: 53. Number of topics: 798
2025-05-19 19:30:08.109 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (2394) is greater than target (53). Attempting to find a subset that sums to target score.
2025-05-19 19:30:08.114 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=2394
2025-05-19 19:30:08.114 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=798
2025-05-19 19:30:08.114 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 798 topics.
2025-05-19 19:30:08.911 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 45, Target score: 53. Number of topics: 15
2025-05-19 19:37:25.605 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 20:13:22.677 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 20:38:50.829 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=190, questionCount=null, includeShortAnswer=true)], title=识记类  专项练习, totalScore=53, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-19 20:38:50.967 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 857. Earmarked: 0, General Pool (unique): 857. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-19 20:38:51.012 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 857 topics. IDs: [172949, 172971, 172998, 173013, 173084, 173103, 173122, 173144, 173161, 173178, 173208, 173219, 173268, 173286, 173305, 173339, 173345, 173347, 173349, 173351, 173352, 173354, 173358, 173359, 173360, 173363, 173364, 173366, 173367, 173369, 173371, 173373, 173374, 173375, 173377, 173378, 173380, 173382, 173384, 173386, 173388, 173392, 173394, 173407, 173415, 173421, 173423, 174242, 174258, 174285, 174665, 174684, 174723, 174896, 174912, 174917, 174919, 174920, 174957, 174982, 174993, 175121, 175122, 175143, 175159, 175165, 175169, 175191, 175199, 175239, 175251, 175259, 175286, 175302, 175311, 175314, 175322, 175326, 175329, 175332, 175334, 175336, 175341, 175386, 175390, 175391, 175392, 175393, 175394, 175395, 175396, 175397, 175398, 175399, 175400, 175401, 175402, 175403, 175404, 175405, 175406, 175407, 175408, 175409, 175410, 175411, 175412, 175413, 175414, 175415, 175416, 175417, 175418, 175419, 175420, 175421, 175422, 175423, 175424, 175425, 175426, 175427, 175428, 175429, 175430, 175431, 175432, 175433, 175434, 175435, 175436, 175438, 175439, 175440, 175441, 175443, 175461, 175462, 175478, 175479, 175480, 175491, 175492, 175493, 175494, 175495, 175496, 175497, 175498, 175499, 175500, 175501, 175502, 175503, 175504, 175505, 175506, 175507, 175508, 175509, 175510, 175511, 175512, 175513, 175514, 175515, 175516, 175517, 175518, 175519, 175520, 175521, 175522, 175523, 175524, 175525, 175526, 175527, 175528, 175529, 175530, 175531, 175532, 175533, 175534, 175535, 175536, 175537, 175538, 175539, 175540, 175541, 175542, 175543, 175544, 175545, 175546, 175547, 175548, 175549, 175550, 175551, 175552, 175553, 175554, 175555, 175556, 175557, 175558, 175559, 175560, 175561, 175562, 175563, 175564, 175565, 175566, 175567, 175568, 175569, 175570, 175571, 175572, 175573, 175574, 175575, 175576, 175577, 175578, 175579, 175580, 175581, 175582, 175583, 175584, 175585, 175586, 175587, 175588, 175589, 175590, 175591, 175592, 175593, 175594, 175595, 175596, 175597, 175598, 175599, 175600, 175601, 175602, 175603, 175604, 175605, 175606, 175607, 175608, 175609, 175610, 175611, 175612, 175613, 175614, 175615, 175616, 175617, 175618, 175619, 175620, 175621, 175622, 175623, 175624, 175625, 175626, 175627, 175628, 175629, 175630, 175631, 175632, 175633, 175634, 175635, 175636, 175637, 175638, 175639, 175640, 175641, 175642, 175643, 175644, 175645, 175646, 175647, 175648, 175649, 175650, 175651, 175652, 175653, 175654, 175655, 175656, 175657, 175658, 175659, 175660, 175661, 175662, 175663, 175664, 175665, 175666, 175667, 175668, 175669, 175670, 175671, 175672, 175673, 175674, 175675, 175676, 175677, 175678, 175679, 175680, 175681, 175682, 175683, 175684, 175685, 175686, 175687, 175688, 175689, 175690, 175691, 175692, 175693, 175694, 175695, 175696, 175697, 175698, 175699, 175700, 175701, 175702, 175703, 175704, 175705, 175706, 175707, 175708, 175709, 175710, 175711, 175712, 175713, 175714, 175715, 175716, 175717, 175718, 175719, 175720, 175721, 175722, 175723, 175724, 175725, 175726, 175727, 175728, 175729, 175730, 175731, 175732, 175733, 175734, 175735, 175736, 175737, 175738, 175739, 175740, 175741, 175742, 175743, 175744, 175745, 175746, 175747, 175748, 175749, 175750, 175751, 175752, 175753, 175754, 175755, 175756, 175757, 175758, 175759, 175760, 175761, 175762, 175763, 175764, 175765, 175766, 175767, 175768, 175769, 175770, 175771, 175772, 175773, 175774, 175775, 175776, 175777, 175778, 175779, 175780, 175781, 175782, 175783, 175918, 175919, 175920, 175921, 175922, 175923, 175924, 175925, 175926, 175927, 175928, 175929, 175930, 175931, 175932, 175933, 175934, 175935, 175936, 175937, 175938, 175939, 175940, 175941, 175942, 175943, 175944, 175945, 175946, 175947, 175948, 175949, 175950, 175951, 175952, 175953, 175954, 175955, 175956, 175957, 175958, 175959, 175960, 175961, 175962, 175963, 175964, 175965, 175966, 175967, 175968, 175969, 175970, 175971, 175972, 175973, 175974, 175975, 175976, 175977, 175978, 175979, 175980, 175981, 175982, 175983, 175984, 175985, 175986, 175987, 175988, 175989, 175990, 175991, 175992, 175993, 175994, 175995, 175996, 175998, 176001, 176003, 176005, 176007, 176010, 176013, 176014, 176015, 176016, 176017, 176018, 176019, 176020, 176021, 176022, 176023, 176024, 176025, 176026, 176027, 176028, 176029, 176030, 176031, 176032, 176033, 176034, 176035, 176036, 176037, 176038, 176039, 176040, 176041, 176042, 176043, 176044, 176045, 176046, 176047, 176048, 176049, 176050, 176051, 176052, 176053, 176054, 176055, 176056, 176057, 176058, 176059, 176060, 176061, 176062, 176063, 176065, 176066, 176067, 176068, 176069, 176070, 176071, 176072, 176073, 176074, 176075, 176076, 176077, 176078, 176079, 176080, 176081, 176082, 176083, 176084, 176085, 176086, 176087, 176088, 176089, 176090, 176091, 176092, 176093, 176094, 176095, 176096, 176097, 176098, 176099, 176100, 176101, 176102, 176103, 176104, 176105, 176106, 176107, 176108, 176109, 176110, 176111, 176112, 176113, 176114, 176115, 176116, 176117, 176118, 176119, 176120, 176121, 176122, 176123, 176124, 176125, 176126, 176127, 176128, 176129, 176130, 176131, 176132, 176133, 176134, 176135, 176136, 176137, 176138, 176139, 176140, 176141, 176142, 176143, 176144, 176145, 176146, 176147, 176148, 176149, 176150, 176152, 176154, 176159, 176161, 176163, 176165, 176168, 176170, 176171, 176174, 176178, 176184, 176187, 176191, 176195, 176199, 176281, 176282, 176283, 176284, 176286, 176287, 176288, 176289, 176290, 176291, 176292, 176294, 176295, 176296, 176297, 176298, 176299, 176300, 176301, 176302, 176303, 176306, 176308, 176326, 176328, 176330, 176331, 176333, 176334, 176335, 176337, 176338, 176339, 176340, 176341, 176343, 176345, 176346, 176347, 176348, 176349, 176350, 176351, 176353, 176354, 176355, 176356, 176357, 176358, 176360, 176361, 176362, 176363, 176365, 176367, 176369, 176371, 176374, 176375, 176376, 176377, 176378, 176379, 176380, 176381, 176383, 176385, 176386, 176389, 176390, 176393, 176394, 176395, 176396, 176398, 176399, 176400, 176402, 176404, 176406, 176408, 176410, 176411, 176412, 176413, 176415, 176416, 176417, 176418, 176419, 176420, 176422, 176423, 176424, 176425, 176426, 176427, 176429, 176433, 176435, 176436, 176437, 176438, 176439, 176440, 176441, 176443, 176446, 176448, 176450, 176451, 176452, 176453, 176455, 176457, 182730, 182731, 182732, 182733, 182734, 182735, 182736, 182738, 182759, 182762, 182766, 182768, 182771, 182774, 182775, 182776, 182779, 182804, 182805, 182810, 182814, 182818, 182822, 182825, 182829, 182832, 182851, 182855, 182856, 182858, 182866, 182869, 182904, 182906, 182908, 182910, 182911, 182913, 182914, 182916, 182922, 182924, 182926, 182928, 182930, 182932, 182934, 182935, 182944, 182946, 182948, 182950, 182951, 182952, 182954, 182955, 182957, 182960, 182963, 182965, 182967, 182970, 182972, 182974, 182977, 182979, 182980, 182983, 182984, 182987]
2025-05-19 20:38:51.013 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 20:38:51.014 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 857 topics available for GA (input size was 857). MinReuseIntervalDays: null
2025-05-19 20:38:51.014 [http-nio-8081-exec-10] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-19 20:38:51.014 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 857 topics remain.
2025-05-19 20:38:51.014 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 857 candidate topics...
2025-05-19 20:38:51.015 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-19 20:38:51.015 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-19 20:38:51.148 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 0.7315592948717948, Type Distribution = 1248
2025-05-19 20:38:51.297 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 0.7315592948717948, Type Distribution = 1248
2025-05-19 20:38:51.380 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 0.7315592948717948, Type Distribution = 1248
2025-05-19 20:38:51.445 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 0.7315592948717948, Type Distribution = 1248
2025-05-19 20:38:51.445 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 0.7315592948717948.
2025-05-19 20:38:51.446 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 431ms. Best solution fitness: {:.4f}, Selected 0.7315592948717948 topics with total score: 416.
2025-05-19 20:38:51.448 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 416 topics with type distribution: {singleChoice=232, judgment=179, multipleChoice=5}
2025-05-19 20:38:51.449 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 53, 同时保持题型分布
2025-05-19 20:38:51.449 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {singleChoice=232, judgment=179, multipleChoice=5}
2025-05-19 20:38:51.457 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 1248, Target score: 53. Number of topics: 416
2025-05-19 20:38:51.460 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=232, judgment=179, multipleChoice=5}, Target type counts: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-19 20:38:51.460 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Starting strict type-preserving optimization
2025-05-19 20:38:51.461 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Initial selection with strict type constraints: 0 topics, score=0, type distribution={SHORT_ANSWER=0, JUDGMENT=0, SINGLE_CHOICE=0, FILL_IN_BLANKS=0, MULTIPLE_CHOICE=0}
2025-05-19 20:38:51.462 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: After type-preserving adjustment: score=0 (target=53), type distribution={SHORT_ANSWER=0, JUDGMENT=0, SINGLE_CHOICE=0, FILL_IN_BLANKS=0, MULTIPLE_CHOICE=0}
2025-05-19 20:38:51.462 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=232, judgment=179, multipleChoice=5}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 20:38:51.462 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-19 20:38:51.463 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-19 20:38:51.463 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type MULTIPLE_CHOICE: 3 topics needed
2025-05-19 20:38:51.463 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-19 20:38:51.463 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type FILL_IN_BLANKS: 3 topics needed
2025-05-19 20:38:51.463 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SHORT_ANSWER: 2 topics needed
2025-05-19 20:38:51.464 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 1248 but target is 53, attempting to adjust scores...
2025-05-19 20:38:51.465 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 1248, Target score: 53. Number of topics: 416
2025-05-19 20:38:51.465 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score (1248) is greater than target (53). Attempting to find a subset that sums to target score.
2025-05-19 20:38:51.468 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=1248
2025-05-19 20:38:51.468 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=416
2025-05-19 20:38:51.468 [http-nio-8081-exec-10] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 416 topics.
2025-05-19 20:38:51.766 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 39, Target score: 53. Number of topics: 13
2025-05-19 21:19:19.917 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-19 22:06:27.619 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
