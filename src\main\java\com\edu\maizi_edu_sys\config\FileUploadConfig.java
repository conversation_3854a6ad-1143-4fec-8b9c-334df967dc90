package com.edu.maizi_edu_sys.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.lang.NonNull;

import javax.annotation.PostConstruct;
import java.io.File;
import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class FileUploadConfig implements WebMvcConfigurer {
    
    @Value("${file.upload.base-path}")
    private String uploadPath;
    
    @Value("${file.upload.avatar.path}")
    private String avatarPath;
    
    @PostConstruct
    public void init() {
        try {
            // 获取项目根目录的绝对路径
            String projectRoot = new File(".").getAbsolutePath();
            String absoluteUploadPath = new File(projectRoot, uploadPath).getAbsolutePath();
            String absoluteAvatarPath = new File(projectRoot, avatarPath).getAbsolutePath();
            
            // 创建目录
            createDirectory(absoluteUploadPath);
            createDirectory(absoluteAvatarPath);
            
            log.info("Upload directories initialized: base={}, avatar={}", absoluteUploadPath, absoluteAvatarPath);
        } catch (Exception e) {
            log.error("Failed to initialize upload directories", e);
            throw new RuntimeException("Failed to initialize upload directories", e);
        }
    }
    
    private void createDirectory(String path) {
        File directory = new File(path);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (!created) {
                throw new RuntimeException("Failed to create directory: " + path);
            }
            log.info("Created directory: {}", path);
        }
    }
    
    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 获取项目根目录的绝对路径
        String projectRoot = new File(".").getAbsolutePath();
        String absoluteUploadPath = new File(projectRoot, uploadPath).getAbsolutePath();
        String uploadLocation = "file:" + absoluteUploadPath + File.separator;
        
        log.info("Configuring resource handler: path=/uploads/**, location={}", uploadLocation);
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations(uploadLocation)
                .setCachePeriod(3600)
                .resourceChain(true);
    }
} 