package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.ApiResponse;
import com.edu.maizi_edu_sys.dto.KnowledgeDTO;
import com.edu.maizi_edu_sys.dto.KnowledgeGroupDTO;
import com.edu.maizi_edu_sys.dto.KnowledgePointDto;
import com.edu.maizi_edu_sys.entity.Knowledge;
import com.edu.maizi_edu_sys.service.KnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识点管理控制器
 */
@RestController
@RequestMapping("/api/knowledge")
@Slf4j
public class KnowledgeController {

    private final KnowledgeService knowledgeService;

    @Autowired
    public KnowledgeController(KnowledgeService knowledgeService) {
        this.knowledgeService = knowledgeService;
    }

    /**
     * 获取所有知识点
     * 支持按名称搜索、按分类筛选和分页
     */
    @GetMapping("/all")
    public ApiResponse<List<Map<String, Object>>> getAllKnowledgePoints(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String filter,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "30") Integer limit) {
        log.info("获取知识点列表: search={}, filter={}, page={}, limit={}", search, filter, page, limit);

        try {
            List<Map<String, Object>> result;

            if (StringUtils.isNotBlank(filter) && !filter.equals("all")) {
                if (filter.equals("recent")) {
                    // 获取最近使用的知识点
                    result = knowledgeService.getRecentKnowledgePoints(limit);
                } else if (filter.equals("popular")) {
                    // 获取常用知识点（基于题目数量）
                    result = knowledgeService.getPopularKnowledgePoints(limit);
                } else if (filter.startsWith("group:")) {
                    // 按分类ID筛选
                    String groupIdStr = filter.substring(6);
                    try {
                        // 尝试解析为整数，如果失败就使用默认查询
                        if (StringUtils.isNumeric(groupIdStr)) {
                            Integer groupId = Integer.parseInt(groupIdStr);
                            result = knowledgeService.getKnowledgePointsByGroup(groupId, search, page, limit);
                        } else {
                            log.warn("无法解析组ID '{}' 为整数，使用默认查询", groupIdStr);
                            result = knowledgeService.getAllKnowledgePoints(search, page, limit);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析组ID出错: {}", e.getMessage());
                        result = knowledgeService.getAllKnowledgePoints(search, page, limit);
                    }
                } else {
                    // 默认返回所有
                    result = knowledgeService.getAllKnowledgePoints(search, page, limit);
                }
            } else {
                // 默认返回所有
                result = knowledgeService.getAllKnowledgePoints(search, page, limit);
            }

            return new ApiResponse<>(true, "获取知识点列表成功", result);
        } catch (Exception e) {
            log.error("获取知识点列表失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取知识点列表失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取常用知识点（按题目数量排序）
     */
    @GetMapping("/popular")
    public ApiResponse<List<Map<String, Object>>> getPopularKnowledgePoints(
            @RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<Map<String, Object>> result = knowledgeService.getPopularKnowledgePoints(limit);
            return new ApiResponse<>(true, "获取常用知识点成功", result);
        } catch (Exception e) {
            log.error("获取常用知识点失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取常用知识点失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取最近使用的知识点
     */
    @GetMapping("/recent")
    public ApiResponse<List<Map<String, Object>>> getRecentKnowledgePoints(
            @RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<Map<String, Object>> result = knowledgeService.getRecentKnowledgePoints(limit);
            return new ApiResponse<>(true, "获取最近使用知识点成功", result);
        } catch (Exception e) {
            log.error("获取最近使用知识点失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取最近使用知识点失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取所有知识点分类
     */
    @GetMapping("/groups")
    public ApiResponse<List<Map<String, Object>>> getAllKnowledgeGroups() {
        log.info("获取所有知识点分类");

        try {
            // 使用正确的方法获取知识点分组
            List<KnowledgeGroupDTO> groups = knowledgeService.getKnowledgeGroups();

            // 转换为前端需要的格式
            List<Map<String, Object>> result = groups.stream()
                .map(group -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", group.getId());
                    map.put("groupName", group.getGroupName());
                    map.put("count", group.getCount());
                    map.put("sort", group.getSort());
                    return map;
                })
                .collect(Collectors.toList());

            return new ApiResponse<>(true, "获取知识点分类成功", result);
        } catch (Exception e) {
            log.error("获取知识点分类失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取知识点分类失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取知识点分类及其知识点
     * 用于自由组卷功能
     */
    @GetMapping("/groups/with-points")
    public ApiResponse<List<Map<String, Object>>> getKnowledgeGroupsWithPoints() {
        log.info("获取知识点分类及其知识点");

        try {
            List<Map<String, Object>> result = new ArrayList<>();

            // 获取所有分组
            List<KnowledgeGroupDTO> groups = knowledgeService.getKnowledgeGroups();

            // 遍历每个分组，获取其知识点
            for (KnowledgeGroupDTO group : groups) {
                Map<String, Object> groupMap = new HashMap<>();
                groupMap.put("id", group.getId());
                groupMap.put("groupName", group.getGroupName());
                groupMap.put("count", group.getCount());

                // 获取该分组下的知识点
                List<Map<String, Object>> knowledgePoints = knowledgeService.getKnowledgePointsByGroup(group.getId(), null, 1, 100);

                groupMap.put("knowledgePoints", knowledgePoints);
                result.add(groupMap);
            }

            return new ApiResponse<>(true, "获取知识点分类及其知识点成功", result);
        } catch (Exception e) {
            log.error("获取知识点分类及其知识点失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取知识点分类及其知识点失败: " + e.getMessage(), null);
        }
    }

    /**
     * 根据分类ID获取知识点
     */
    @GetMapping("/points")
    public ApiResponse<List<KnowledgePointDto>> getKnowledgePointsByGroupId(
            @RequestParam(required = true) Integer groupId) {
        try {
            // 获取原始数据
            List<Map<String, Object>> rawData = knowledgeService.getKnowledgePointMapsByGroupId(groupId);

            // 转换为标准化的DTO对象
            List<KnowledgePointDto> result = rawData.stream()
                    .map(KnowledgePointDto::fromMap)
                    .filter(dto -> dto != null && dto.getId() != null && dto.getKnowledgeId() != null) // 过滤无效数据，确保有真实的知识点ID
                    .collect(Collectors.toList());

            // 记录日志，验证是否包含knowledgeId字段
            if (!result.isEmpty()) {
                KnowledgePointDto firstDto = result.get(0);
                log.info("API响应示例: id={}, knowledgeId={}, name={}",
                        firstDto.getId(), firstDto.getKnowledgeId(), firstDto.getName());

                // 临时调试：检查原始Map数据
                if (!rawData.isEmpty()) {
                    Map<String, Object> firstMap = rawData.get(0);
                    log.info("原始Map数据: id={}, knowledgeId={}, name={}",
                            firstMap.get("id"), firstMap.get("knowledgeId"), firstMap.get("knowledgeName"));
                }
            }

            return new ApiResponse<>(true, "获取分类 '" + groupId + "' 的知识点成功", result);
        } catch (Exception e) {
            log.error("获取分类 '" + groupId + "' 的知识点失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取分类 '" + groupId + "' 的知识点失败: " + e.getMessage(), null);
        }
    }

    /**
     * 根据分类名称获取知识点
     */
    @GetMapping("/by-group-name")
    public ApiResponse<List<Map<String, Object>>> getKnowledgePointsByGroupName(
            @RequestParam(required = true) String groupName) {
        try {
            List<Map<String, Object>> result = knowledgeService.getKnowledgePointsByGroupName(groupName);
            return new ApiResponse<>(true, "获取知识点成功", result);
        } catch (Exception e) {
            log.error("获取知识点失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取知识点失败: " + e.getMessage(), null);
        }
    }

    /**
     * 根据ID获取知识点详情
     */
    @GetMapping("/{id}")
    public ApiResponse<KnowledgeDTO> getKnowledgeById(@PathVariable Integer id) {
        try {
            KnowledgeDTO knowledge = knowledgeService.getKnowledgeById(id);
            if (knowledge != null) {
                return new ApiResponse<>(true, "获取知识点详情成功", knowledge);
            } else {
                return new ApiResponse<>(false, "未找到ID为 " + id + " 的知识点", null);
            }
        } catch (Exception e) {
            log.error("获取知识点详情失败: {}", e.getMessage(), e);
            return new ApiResponse<>(false, "获取知识点详情失败: " + e.getMessage(), null);
        }
    }

    /**
     * 更新分类排序
     */
    @PostMapping("/groups/sort")
    public ResponseEntity<ApiResponse<Void>> updateGroupOrder(@RequestBody List<KnowledgeGroupDTO> groups) {
        try {
            knowledgeService.updateGroupOrder(groups);
            return ResponseEntity.ok(new ApiResponse<>(true, "更新排序成功", null));
        } catch (Exception e) {
            log.error("更新分类排序失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "更新排序失败: " + e.getMessage(), null));
        }
    }

    /**
     * 创建知识点
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Void>> createKnowledge(@RequestBody KnowledgeDTO knowledgeDTO) {
        try {
            boolean success = knowledgeService.createKnowledge(knowledgeDTO);
            if (success) {
                return ResponseEntity.ok(new ApiResponse<>(true, "创建成功", null));
            } else {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>(false, "创建失败", null));
            }
        } catch (Exception e) {
            log.error("创建知识点失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "创建失败: " + e.getMessage(), null));
        }
    }

    /**
     * 更新知识点
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> updateKnowledge(
            @PathVariable Integer id,
            @RequestBody KnowledgeDTO knowledgeDTO) {
        try {
            knowledgeDTO.setId(id); // 确保ID正确
            boolean success = knowledgeService.updateKnowledge(knowledgeDTO);
            if (success) {
                return ResponseEntity.ok(new ApiResponse<>(true, "更新成功", null));
            } else {
                return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, "未找到该知识点", null));
            }
        } catch (Exception e) {
            log.error("更新知识点失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "更新失败: " + e.getMessage(), null));
        }
    }

    /**
     * 删除知识点
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteKnowledge(@PathVariable Integer id) {
        try {
            boolean success = knowledgeService.deleteKnowledge(id);
            if (success) {
                return ResponseEntity.ok(new ApiResponse<>(true, "删除成功", null));
            } else {
                return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, "未找到该知识点", null));
            }
        } catch (Exception e) {
            log.error("删除知识点失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "删除失败: " + e.getMessage(), null));
        }
    }

    /**
     * 批量删除知识点
     */
    @DeleteMapping("/batch")
    public ResponseEntity<ApiResponse<Void>> batchDeleteKnowledge(@RequestBody List<Integer> ids) {
        try {
            boolean success = knowledgeService.batchDeleteKnowledge(ids);
            if (success) {
                return ResponseEntity.ok(new ApiResponse<>(true, "批量删除成功", null));
            } else {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>(false, "批量删除失败", null));
            }
        } catch (Exception e) {
            log.error("批量删除知识点失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "批量删除失败: " + e.getMessage(), null));
        }
    }

    /**
     * 根据条件搜索知识点
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<KnowledgeDTO>>> searchKnowledge(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer isFree) {
        try {
            log.info("搜索知识点: keyword={}, groupName={}, isFree={}", keyword, groupName, isFree);

            List<Knowledge> knowledgeList = knowledgeService.searchKnowledge(keyword, groupName, isFree);
            log.info("搜索到 {} 个知识点", knowledgeList.size());

            // 搜索时不包含题目数量统计以提高性能
            List<KnowledgeDTO> dtoList = knowledgeService.convertToDTOList(knowledgeList, false);

            return ResponseEntity.ok(new ApiResponse<>(true, "搜索成功", dtoList));
        } catch (Exception e) {
            log.error("搜索知识点失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "搜索失败: " + e.getMessage(), null));
        }
    }

    /**
     * 统计分组数量
     */
    @GetMapping("/stats/groups")
    public ResponseEntity<ApiResponse<List<KnowledgeGroupDTO>>> countByGroups() {
        try {
            List<KnowledgeGroupDTO> stats = knowledgeService.countByGroups();
            return ResponseEntity.ok(new ApiResponse<>(true, "统计成功", stats));
        } catch (Exception e) {
            log.error("统计知识点分组失败: ", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "统计失败: " + e.getMessage(), null));
        }
    }
}