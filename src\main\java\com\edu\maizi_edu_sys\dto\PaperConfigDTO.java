package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 试卷配置DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperConfigDTO {

    private Long id;

    @NotBlank(message = "配置名称不能为空")
    private String configName;

    private String description;

    private Long userId;

    private String titleTemplate;

    private String paperType = "standard";

    @Min(value = 1, message = "生成套数至少为1")
    @Max(value = 10, message = "生成套数最多为10")
    private Integer paperCount = 1;

    // 题型配置
    @Min(value = 0, message = "题目数量不能为负数")
    private Integer singleChoiceCount = 0;

    @Min(value = 0, message = "分值不能为负数")
    private Integer singleChoiceScore = 0;

    @Min(value = 0, message = "题目数量不能为负数")
    private Integer multipleChoiceCount = 0;

    @Min(value = 0, message = "分值不能为负数")
    private Integer multipleChoiceScore = 0;

    @Min(value = 0, message = "题目数量不能为负数")
    private Integer judgmentCount = 0;

    @Min(value = 0, message = "分值不能为负数")
    private Integer judgmentScore = 0;

    @Min(value = 0, message = "题目数量不能为负数")
    private Integer fillCount = 0;

    @Min(value = 0, message = "分值不能为负数")
    private Integer fillScore = 0;

    @Min(value = 0, message = "题目数量不能为负数")
    private Integer shortAnswerCount = 0;

    @Min(value = 0, message = "分值不能为负数")
    private Integer shortAnswerScore = 0;

    // 难度分布
    private DifficultyDistribution difficultyDistribution;

    // 知识点配置
    private List<KnowledgePointConfigRequest> knowledgePointConfigs;

    private Boolean isDefault = false;

    private Boolean isPublic = false;

    private Integer usageCount = 0;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private LocalDateTime lastUsedAt;

    /**
     * 难度分布配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifficultyDistribution {
        @Min(value = 0, message = "难度比例不能为负数")
        @Max(value = 1, message = "难度比例不能超过1")
        private Double easy = 0.3;

        @Min(value = 0, message = "难度比例不能为负数")
        @Max(value = 1, message = "难度比例不能超过1")
        private Double medium = 0.5;

        @Min(value = 0, message = "难度比例不能为负数")
        @Max(value = 1, message = "难度比例不能超过1")
        private Double hard = 0.2;
    }

    /**
     * 计算总分
     */
    public Integer getTotalScore() {
        return (singleChoiceCount * singleChoiceScore) +
               (multipleChoiceCount * multipleChoiceScore) +
               (judgmentCount * judgmentScore) +
               (fillCount * fillScore) +
               (shortAnswerCount * shortAnswerScore);
    }

    /**
     * 计算总题数
     */
    public Integer getTotalQuestions() {
        return singleChoiceCount + multipleChoiceCount + judgmentCount + fillCount + shortAnswerCount;
    }

    /**
     * 转换为试卷生成请求
     */
    public PaperGenerationRequest toPaperGenerationRequest() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        request.setTitle(this.titleTemplate);
        request.setKnowledgePointConfigs(this.knowledgePointConfigs);
        request.setTotalScore(this.getTotalScore());

        // 设置题型数量
        Map<String, Integer> topicTypeCounts = new HashMap<>();
        topicTypeCounts.put("SINGLE_CHOICE", this.singleChoiceCount);
        topicTypeCounts.put("MULTIPLE_CHOICE", this.multipleChoiceCount);
        topicTypeCounts.put("JUDGE", this.judgmentCount);
        topicTypeCounts.put("FILL", this.fillCount);
        topicTypeCounts.put("SHORT", this.shortAnswerCount);
        request.setTopicTypeCounts(topicTypeCounts);

        // 设置题型分值
        Map<String, Integer> typeScoreMap = new HashMap<>();
        typeScoreMap.put("SINGLE_CHOICE", this.singleChoiceScore);
        typeScoreMap.put("MULTIPLE_CHOICE", this.multipleChoiceScore);
        typeScoreMap.put("JUDGE", this.judgmentScore);
        typeScoreMap.put("FILL", this.fillScore);
        typeScoreMap.put("SHORT", this.shortAnswerScore);
        request.setTypeScoreMap(typeScoreMap);

        // 设置难度分布
        if (this.difficultyDistribution != null) {
            Map<String, Double> difficultyCriteria = new HashMap<>();
            difficultyCriteria.put("easy", this.difficultyDistribution.getEasy());
            difficultyCriteria.put("medium", this.difficultyDistribution.getMedium());
            difficultyCriteria.put("hard", this.difficultyDistribution.getHard());
            request.setDifficultyCriteria(difficultyCriteria);
        }

        return request;
    }

    /**
     * 转换为批量生成请求
     */
    public BatchPaperGenerationRequest toBatchPaperGenerationRequest() {
        BatchPaperGenerationRequest request = new BatchPaperGenerationRequest();
        request.setTitle(this.titleTemplate);
        request.setPaperCount(this.paperCount);
        request.setPaperType(this.paperType);
        request.setKnowledgePointConfigs(this.knowledgePointConfigs);
        request.setTotalScore(this.getTotalScore().doubleValue());

        // 设置题型数量
        Map<String, Integer> topicTypeCounts = new HashMap<>();
        topicTypeCounts.put("SINGLE_CHOICE", this.singleChoiceCount);
        topicTypeCounts.put("MULTIPLE_CHOICE", this.multipleChoiceCount);
        topicTypeCounts.put("JUDGE", this.judgmentCount);
        topicTypeCounts.put("FILL", this.fillCount);
        topicTypeCounts.put("SHORT", this.shortAnswerCount);
        request.setTopicTypeCounts(topicTypeCounts);

        // 设置题型分值
        Map<String, Integer> typeScoreMap = new HashMap<>();
        typeScoreMap.put("SINGLE_CHOICE", this.singleChoiceScore);
        typeScoreMap.put("MULTIPLE_CHOICE", this.multipleChoiceScore);
        typeScoreMap.put("JUDGE", this.judgmentScore);
        typeScoreMap.put("FILL", this.fillScore);
        typeScoreMap.put("SHORT", this.shortAnswerScore);
        request.setTypeScoreMap(typeScoreMap);

        // 设置难度分布
        if (this.difficultyDistribution != null) {
            BatchPaperGenerationRequest.DifficultyDistribution difficulty =
                new BatchPaperGenerationRequest.DifficultyDistribution();
            difficulty.setEasy(this.difficultyDistribution.getEasy());
            difficulty.setMedium(this.difficultyDistribution.getMedium());
            difficulty.setHard(this.difficultyDistribution.getHard());
            request.setDifficultyCriteria(difficulty);
        }

        return request;
    }
}
