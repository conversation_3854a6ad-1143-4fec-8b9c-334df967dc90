package com.edu.maizi_edu_sys.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 试卷实体类
 */
@Data
@Entity
@Table(name = "papers")
@TableName("papers")
public class Paper {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 试卷标题
     */
    @Column(nullable = false)
    private String title;
    
    /**
     * 创建用户ID
     */
    @Column(name = "user_id")
    @TableField("user_id")
    private Long userId;
    
    /**
     * 试卷类型: regular-普通试卷, teacher-教师试卷, standard-标准试卷
     */
    @Column(name = "paper_type")
    @TableField("paper_type")
    private String paperType = "regular";
    
    /**
     * 文件格式: pdf, docx
     */
    @Column(name = "file_format")
    @TableField("file_format")
    private String fileFormat = "pdf";
    
    /**
     * 下载次数
     */
    @Column(name = "download_count")
    @TableField("download_count")
    private Integer downloadCount = 0;
    
    /**
     * 最后下载时间
     */
    @Column(name = "last_download_time")
    @TableField("last_download_time")
    private LocalDateTime lastDownloadTime;
    
    /**
     * 知识点ID
     */
    @Column(name = "knowledge_id")
    @TableField("knowledge_id")
    private Integer knowledgeId;

    /**
     * 知识点名称
     */
    @Column(name = "knowledge_name")
    @TableField("knowledge_name")
    private String knowledgeName;

    /**
     * 试卷总分（目标分数）
     */
    @Column(name = "total_score", nullable = false)
    @TableField("total_score")
    private Integer totalScore;
    
    /**
     * 实际总分
     */
    @Column(name = "actual_total_score")
    @TableField("actual_total_score")
    private Integer actualTotalScore;

    /**
     * 试卷难度
     */
    private Double difficulty;
    
    /**
     * 难度分布JSON，格式：[{"1": 0.3, "2": 0.5, "3": 0.2}] 表示简单30%，中等50%，困难20%
     */
    @Column(name = "difficulty_distribution", columnDefinition = "JSON")
    @TableField("difficulty_distribution")
    private String difficultyDistribution;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    @TableField("is_deleted")
    private Boolean isDeleted = false;

    /**
     * 试卷内容JSON (包含题目列表)
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;
    
    /**
     * 试卷生成配置JSON
     */
    @Column(name = "config", columnDefinition = "TEXT")
    private String config;
    
    /**
     * 根据paperType获取试卷类型的整数值
     * 
     * @return 试卷类型值 (0-普通试卷, 1-教师试卷, 2-标准试卷)
     */
    public Integer getType() {
        return convertDbValueToTypeValue(this.paperType);
    }
    
    /**
     * 设置试卷类型整数值并自动转换为paperType
     * 
     * @param typeValue 试卷类型值 (0-普通试卷, 1-教师试卷, 2-标准试卷)
     */
    public void setType(Integer typeValue) {
        this.paperType = convertTypeValueToDbValue(typeValue);
    }
    
    /**
     * 转换前端试卷类型值为数据库存储值
     * @param typeValue 前端试卷类型值 (0-普通试卷, 1-教师试卷, 2-标准试卷)
     * @return 数据库试卷类型值 (regular, teacher, standard)
     */
    public static String convertTypeValueToDbValue(Integer typeValue) {
        if (typeValue == null) return "regular";
        
        switch (typeValue) {
            case 0: return "regular";
            case 1: return "teacher";
            case 2: return "standard";
            default: return "regular";
        }
    }
    
    /**
     * 转换数据库试卷类型值为前端展示值
     * @param dbValue 数据库试卷类型值 (regular, teacher, standard)
     * @return 前端试卷类型值 (0-普通试卷, 1-教师试卷, 2-标准试卷)
     */
    public static Integer convertDbValueToTypeValue(String dbValue) {
        if (dbValue == null) return 0;
        
        switch (dbValue) {
            case "regular": return 0;
            case "teacher": return 1;
            case "standard": return 2;
            default: return 0;
        }
    }
} 