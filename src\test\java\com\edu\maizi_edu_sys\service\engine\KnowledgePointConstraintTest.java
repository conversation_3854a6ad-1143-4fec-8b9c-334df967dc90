package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 知识点约束功能测试
 * 测试知识点级别的题目数量控制和精确题型配置
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("知识点约束功能测试")
public class KnowledgePointConstraintTest {

    private KnowledgePointConstraintChecker constraintChecker;
    private List<Topic> testTopics;
    private List<KnowledgePointConfigRequest> testConfigs;

    @BeforeEach
    void setUp() {
        constraintChecker = new KnowledgePointConstraintChecker();
        createTestData();
    }

    private void createTestData() {
        testTopics = new ArrayList<>();

        // 知识点206的题目
        for (int i = 1; i <= 15; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(206);

            // 分配不同题型
            if (i <= 5) {
                topic.setType("choice");  // 5道单选题
            } else if (i <= 8) {
                topic.setType("multiple"); // 3道多选题
            } else if (i <= 10) {
                topic.setType("judge");   // 2道判断题
            } else if (i <= 12) {
                topic.setType("fill");    // 2道填空题
            } else {
                topic.setType("short");   // 3道简答题
            }

            topic.setTitle("题目 " + i);
            topic.setScore(5);
            topic.setDifficulty(0.3);
            testTopics.add(topic);
        }

        // 知识点207的题目
        for (int i = 16; i <= 25; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(207);
            topic.setType("choice");  // 10道单选题
            topic.setTitle("题目 " + i);
            topic.setScore(3);
            topic.setDifficulty(0.2);
            testTopics.add(topic);
        }

        // 创建测试配置
        testConfigs = new ArrayList<>();
    }

    @Test
    @DisplayName("测试精确题型配置功能")
    void testExactTypeConfiguration() {
        // 创建简答题配置
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setQuestionCount(5); // 基础题量
        config.setIncludeShortAnswer(true);
        config.setShortAnswerCount(2);

        testConfigs.add(config);

        // 验证配置方法
        assertTrue(config.hasShortAnswerConfiguration());
        assertEquals(7, config.getTotalQuestionCount()); // 5基础题 + 2简答题
        assertEquals(5, config.getBasicQuestionCount()); // 基础题量
        assertEquals(2, config.getShortAnswerCount()); // 简答题数量
    }

    @Test
    @DisplayName("测试知识点约束检查 - 满足约束")
    void testConstraintCheckSatisfied() {
        // 配置：知识点206需要5道基础题、2道简答题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setQuestionCount(5); // 基础题量
        config.setIncludeShortAnswer(true);
        config.setShortAnswerCount(2);
        testConfigs.add(config);

        // 选择符合要求的题目
        List<Topic> selectedTopics = Arrays.asList(
            testTopics.get(0), testTopics.get(1), testTopics.get(2), testTopics.get(3), testTopics.get(4), // 5道基础题
            testTopics.get(12), testTopics.get(13) // 2道简答题
        );

        // 检查约束
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result =
            constraintChecker.checkConstraints(selectedTopics, testConfigs);

        assertTrue(result.isValid());
        assertTrue(result.getMessage().contains("满足约束"));
    }

    @Test
    @DisplayName("测试知识点约束检查 - 不满足约束")
    void testConstraintCheckNotSatisfied() {
        // 配置：知识点206需要5道基础题、3道简答题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setQuestionCount(5); // 基础题量
        config.setIncludeShortAnswer(true);
        config.setShortAnswerCount(3);
        testConfigs.add(config);

        // 选择不符合要求的题目（只有3道基础题、2道简答题）
        List<Topic> selectedTopics = Arrays.asList(
            testTopics.get(0), testTopics.get(1), testTopics.get(2), // 3道基础题（不足）
            testTopics.get(12), testTopics.get(13) // 2道简答题（不足）
        );

        // 检查约束
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result =
            constraintChecker.checkConstraints(selectedTopics, testConfigs);

        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("不满足约束"));
    }

    @Test
    @DisplayName("测试总题目数量约束")
    void testTotalQuestionCountConstraint() {
        // 配置：知识点206总共需要10道题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setQuestionCount(10);
        testConfigs.add(config);

        // 选择10道题目
        List<Topic> selectedTopics = testTopics.subList(0, 10);

        // 检查约束
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result =
            constraintChecker.checkConstraints(selectedTopics, testConfigs);

        assertTrue(result.isValid());
    }

    @Test
    @DisplayName("测试简答题约束")
    void testShortAnswerConstraints() {
        // 配置：知识点206需要2道简答题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setIncludeShortAnswer(true);
        config.setShortAnswerCount(2);
        testConfigs.add(config);

        // 选择符合要求的题目（2道简答题）
        List<Topic> selectedTopics = Arrays.asList(
            testTopics.get(12), testTopics.get(13) // 2道简答题
        );

        // 检查约束
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result =
            constraintChecker.checkConstraints(selectedTopics, testConfigs);

        assertTrue(result.isValid());
        assertTrue(result.getMessage().contains("满足约束"));
    }

    @Test
    @DisplayName("测试调整建议生成")
    void testAdjustmentSuggestions() {
        // 配置：知识点206需要2道简答题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setIncludeShortAnswer(true);
        config.setShortAnswerCount(2);
        testConfigs.add(config);

        // 当前只选择了1道简答题
        List<Topic> selectedTopics = Arrays.asList(
            testTopics.get(12) // 1道简答题
        );

        // 生成调整建议
        List<KnowledgePointConstraintChecker.KnowledgePointAdjustmentSuggestion> suggestions =
            constraintChecker.generateAdjustmentSuggestions(selectedTopics, testConfigs, testTopics);

        assertFalse(suggestions.isEmpty());

        KnowledgePointConstraintChecker.KnowledgePointAdjustmentSuggestion suggestion = suggestions.get(0);
        assertEquals(206, suggestion.getKnowledgeId());
        assertTrue(suggestion.getSuggestion().contains("增加"));
    }

    @Test
    @DisplayName("测试空配置处理")
    void testEmptyConfiguration() {
        // 空的题目列表
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result1 =
            constraintChecker.checkConstraints(Collections.emptyList(), testConfigs);
        assertFalse(result1.isValid());

        // 空的配置列表
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result2 =
            constraintChecker.checkConstraints(testTopics, Collections.emptyList());
        assertTrue(result2.isValid());

        // 都为空
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result3 =
            constraintChecker.checkConstraints(Collections.emptyList(), Collections.emptyList());
        assertFalse(result3.isValid());
    }

    @Test
    @DisplayName("测试容差机制")
    void testToleranceMechanism() {
        // 配置：知识点206需要10道题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setQuestionCount(10);
        testConfigs.add(config);

        // 选择9道题目（在容差范围内）
        List<Topic> selectedTopics = testTopics.subList(0, 9);

        // 检查约束（应该通过，因为有±1的容差）
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result =
            constraintChecker.checkConstraints(selectedTopics, testConfigs);

        assertTrue(result.isValid());
    }

    @Test
    @DisplayName("测试题型标准化")
    void testTopicTypeNormalization() {
        // 创建不同格式的简答题
        Topic topic1 = new Topic();
        topic1.setId(100);
        topic1.setKnowId(206);
        topic1.setType("SHORT_ANSWER"); // 前端格式

        Topic topic2 = new Topic();
        topic2.setId(101);
        topic2.setKnowId(206);
        topic2.setType("short"); // 数据库格式

        List<Topic> mixedTopics = Arrays.asList(topic1, topic2);

        // 配置：需要2道简答题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(206L);
        config.setIncludeShortAnswer(true);
        config.setShortAnswerCount(2);
        testConfigs.add(config);

        // 检查约束（应该能正确识别两种格式的题型）
        KnowledgePointConstraintChecker.KnowledgePointConstraintResult result =
            constraintChecker.checkConstraints(mixedTopics, testConfigs);

        assertTrue(result.isValid());
    }
}
