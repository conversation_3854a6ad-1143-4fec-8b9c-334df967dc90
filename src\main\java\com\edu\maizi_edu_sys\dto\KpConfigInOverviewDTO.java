package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class KpConfigInOverviewDTO {
    private Long knowledgeId;       // Matches KnowledgePointConfigRequest
    private String knowledgeName;   // We'll need to fetch this based on knowledgeId
    private Integer questionCount;  // The target number of questions for this KP
    private Boolean includeShortAnswer; // Whether short answers were included for this KP
    // You could add actualQuestionCount if you want to compare target vs actual in the overview
    // private Integer actualQuestionCount;
} 