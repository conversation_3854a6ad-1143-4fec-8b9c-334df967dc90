<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能组卷系统 - 麦子教育</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Add jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Add SweetAlert2 for better dialogs -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Add Markdown-it for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@12.0.6/dist/markdown-it.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Add MathJax for LaTeX rendering - Local fallback for polyfill -->
     <!-- 引入官方推荐的 Polyfill -->
    <script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"></script>
    <script>
        // Polyfill for missing ES6 features
        if (!window.Promise) {
            console.log('Using local ES6 polyfill');
            // Basic Promise polyfill
            window.Promise = function(executor) {
                var self = this;
                self.status = 'pending';
                self.value = null;
                self.reason = null;
                self.onFulfilledCallbacks = [];
                self.onRejectedCallbacks = [];

                function resolve(value) {
                    if (self.status === 'pending') {
                        self.status = 'fulfilled';
                        self.value = value;
                        self.onFulfilledCallbacks.forEach(function(callback) {
                            callback(value);
                        });
                    }
                }

                function reject(reason) {
                    if (self.status === 'pending') {
                        self.status = 'rejected';
                        self.reason = reason;
                        self.onRejectedCallbacks.forEach(function(callback) {
                            callback(reason);
                        });
                    }
                }

                try {
                    executor(resolve, reject);
                } catch (e) {
                    reject(e);
                }
            };
        }
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script th:inline="none">
        // Configure MathJax for inline and display formulas - improved config
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true // Allow \$ to escape
            },
            options: {
                ignoreHtmlClass: 'tex-ignore',
                processHtmlClass: 'tex-process'
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        .knowledge-points-container {
            max-height: 60vh;
            overflow: hidden;
        }

        .knowledge-categories {
            max-height: 60vh;
            overflow-y: auto;
        }

        #knowledge-points-container {
            max-height: 60vh;
            overflow-y: auto;
        }

        .knowledge-point-item {
            padding: 8px 15px;
            border-left: 3px solid transparent;
            transition: all 0.2s;
        }

        .knowledge-point-item:hover {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }

        .knowledge-point-item.active {
            background-color: #007bff;
            border-left-color: #0056b3;
            color: white;
            font-weight: 500;
        }
    </style>
    <style>
        /* Fix select element height issues - improved */
        select.form-control {
            height: auto !important;
            padding: 0.5rem 0.75rem !important;
            line-height: 1.5;
            appearance: menulist; /* Ensure dropdown arrow displays */
        }

        /* Optimize for small screens */
        @media (max-width: 768px) {
            select.form-control {
                font-size: 14px;
                padding: 0.375rem 0.5rem !important;
            }
        }

        :root {
            --primary-color: #3466f6;
            --primary-hover: #2750c9;
            --secondary-color: #ff4081;
            --success-color: #00c853;
            --success-hover: #00a045;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --info-color: #03a9f4;
            --light-bg: #f8f9fa;
            --card-bg: #ffffff;
            --dark-text: #2d3748;
            --light-text: #718096;
            --gray-100: #edf2f7;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e0;
            --border-radius: 12px;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        /* Enhanced Body Typography */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: var(--light-bg);
            color: var(--dark-text);
            font-size: 16px; /* Slightly larger base font size */
            line-height: 1.65; /* Improved line height for readability */
        }

        /* Headings - Improved Hierarchy & Spacing */
        h1, h2, h3, h4, h5, h6 {
            color: var(--dark-text);
            font-weight: 600; /* Bolder headings */
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        h1 { font-size: 2.25rem; line-height: 1.2; }
        h2 { font-size: 1.875rem; line-height: 1.25; }
        h3 { font-size: 1.5rem; line-height: 1.3; }
        h4 { font-size: 1.25rem; line-height: 1.35; }
        h5 { font-size: 1.125rem; line-height: 1.4; }
        h6 { font-size: 1rem; line-height: 1.45; }

        /* Adjusting first heading margin */
        h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
            margin-top: 0;
        }

        /* Navbar Styling */
        .navbar {
            background-color: var(--card-bg);
            color: var(--dark-text);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            height: 64px;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-brand a {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: bold;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
        }

        .nav-item {
            color: var(--dark-text);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--gray-100);
            text-decoration: none; /* Ensure no underline on hover */
        }

        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--gray-100);
            font-weight: 600;
        }

        .nav-user {
            position: relative;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .user-info:hover {
            background-color: var(--gray-100);
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--gray-200);
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            right: 0;
            top: 46px;
            background: var(--card-bg);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius);
            min-width: 180px;
            z-index: 1001;
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .dropdown-menu a {
            color: var(--dark-text);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            transition: var(--transition);
            font-weight: 500;
        }

        .dropdown-menu a:hover {
            background-color: var(--gray-100);
            color: var(--primary-color);
        }

        .user-info:hover + .dropdown-menu,
        .dropdown-menu:hover {
            display: block;
        }

        /* Content Layout */
        .container-fluid {
            padding: 24px;
        }

        .sidebar {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            height: calc(100vh - 120px);
            overflow-y: auto;
            position: sticky;
            top: 90px;
        }

        /* Card Styling */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            background-color: var(--card-bg);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background-color: var(--card-bg);
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 1.25rem; /* Consistent padding */
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-body {
            padding: 1.25rem; /* Consistent padding */
        }

        /* Group Items Styling */
        .group-item {
            cursor: pointer;
            padding: 14px 16px;
            border-left: 3px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
            margin-bottom: 8px;
            border-radius: 8px;
            background-color: var(--gray-100);
        }

        .group-item.active {
            border-left: 3px solid var(--primary-color);
            background-color: rgba(52, 102, 246, 0.1);
            font-weight: 600;
            color: var(--primary-color);
        }

        .group-item:hover {
            background-color: rgba(52, 102, 246, 0.05);
        }

        .handle {
            cursor: move;
            color: var(--light-text);
            margin-right: 10px;
        }

        /* Badges */
        .badge {
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .badge-free {
            background-color: var(--success-color);
            color: white;
        }

        .badge-paid {
            background-color: var(--secondary-color);
            color: white;
        }

        .badge-count {
            background-color: var(--gray-300);
            color: var(--dark-text);
            border-radius: 20px;
            padding: 3px 10px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 8px;
        }

        .group-actions {
            visibility: hidden;
            white-space: nowrap;
            display: flex;
            gap: 8px;
        }

        .group-item:hover .group-actions {
            visibility: visible;
        }

        /* Button Styling */
        .btn {
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: none;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
            border-radius: 6px;
        }

        .btn-lg {
            padding: 12px 24px;
            font-size: 1.1rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            color: white; /* Ensure text color remains white */
            transform: translateY(-1px); /* Subtle lift effect */
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--success-hover);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-info {
            background-color: var(--info-color);
            color: white;
        }

        .btn-info:hover {
            background-color: #0288d1;
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background-color: #f57c00;
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-primary {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        /* Table Styling */
        .table-container {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            margin-top: 10px;
        }

        .table {
            width: 100%;
            margin-bottom: 0;
            background-color: var(--card-bg);
        }

        .table th {
            border-top: none;
            color: var(--light-text);
            font-weight: 600;
            padding: 16px;
            font-size: 0.9rem;
            /* text-transform: uppercase; */ /* Removing uppercase for a softer look */
            letter-spacing: 0.5px;
            background-color: var(--gray-100);
        }

        .table td {
            vertical-align: middle;
            padding: 1rem; /* Consistent padding */
            border-top: 1px solid var(--gray-200);
        }

        .table tr:hover {
            background-color: var(--gray-100); /* Keep hover effect */
        }

        /* Form Controls */
        .form-control {
            border-radius: 8px;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            transition: var(--transition);
            font-size: 0.95rem; /* Slightly adjust form control font size */
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(52, 102, 246, 0.15);
        }

        .form-group label {
            color: var(--dark-text);
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
            font-size: 0.9rem; /* Slightly smaller label */
        }

        .form-group { /* Add overflow visible */
            overflow: visible;
        }

        /* Modal Styling */
        .modal-content {
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            background-color: var(--card-bg);
            color: var(--dark-text);
            padding: 20px;
            border-bottom: 1px solid var(--gray-200); /* Keep border */
            align-items: center;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            border-top: 1px solid var(--gray-200);
            padding: 16px 24px;
            background-color: var(--gray-100);
        }

        .modal-title {
            color: var(--dark-text);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Range Input Styling */
        .form-control-range {
            height: 8px;
            border-radius: 4px;
            background-color: var(--gray-200);
            appearance: none;
            width: 100%;
        }

        .form-control-range::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: var(--shadow-sm);
        }

        .range-value {
            font-weight: 600;
            color: var(--primary-color);
            margin-top: 8px;
            font-size: 14px;
        }

        .range-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 12px;
            color: var(--light-text);
        }

        /* Tabs Styling */
        .tab-animation {
            animation: fadeIn 0.3s;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                height: auto;
                position: static;
                margin-bottom: 20px;
            }

            .navbar {
                padding: 0 16px;
            }

            .nav-item {
                padding: 6px 10px;
                font-size: 0.9rem;
            }

            .container-fluid {
                padding: 16px;
            }
        }

        /* Custom checkbox and radio buttons */
        .custom-control {
            position: relative;
            min-height: 1.5rem;
            padding-left: 1.8rem;
        }

        .custom-control-input {
            position: absolute;
            z-index: -1;
            opacity: 0;
        }

        .custom-control-label {
            margin-bottom: 0;
            vertical-align: top;
            position: relative;
            cursor: pointer;
        }

        .custom-control-label::before {
            position: absolute;
            left: -1.8rem;
            top: 0.2rem;
            display: block;
            width: 1.2rem;
            height: 1.2rem;
            content: "";
            background-color: var(--card-bg);
            border: 1px solid var(--gray-300);
            transition: var(--transition);
        }

        .custom-checkbox .custom-control-label::before {
            border-radius: 4px;
        }

        .custom-radio .custom-control-label::before {
            border-radius: 50%;
        }

        .custom-control-input:checked ~ .custom-control-label::before {
            border-color: var(--primary-color);
            background-color: var(--primary-color);
        }

        .custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
            position: absolute;
            top: 0.35rem;
            left: -1.6rem;
            display: block;
            width: 0.8rem;
            height: 0.8rem;
            content: "";
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
            background-size: contain;
            background-repeat: no-repeat;
        }

        .custom-radio .custom-control-input:checked ~ .custom-control-label::after {
            position: absolute;
            top: 0.35rem;
            left: -1.6rem;
            display: block;
            width: 0.8rem;
            height: 0.8rem;
            content: "";
            border-radius: 50%;
            background-color: white;
            transform: scale(0.5);
        }

        /* Quantity input */
        .quantity-input {
            display: flex;
            align-items: center;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            overflow: hidden;
        }

        .quantity-btn {
            background-color: var(--gray-100);
            border: none;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: var(--dark-text);
            transition: var(--transition);
        }

        .quantity-btn:hover {
            background-color: var(--gray-200);
        }

        .quantity-input input {
            width: 50px;
            text-align: center;
            border: none;
            border-left: 1px solid var(--gray-300);
            border-right: 1px solid var(--gray-300);
            height: 36px;
            padding: 0;
        }

        .quantity-input input:focus {
            outline: none;
        }

        /* Make paper generation modal body scrollable */
        #paperGenerationModal .modal-body {
            max-height: calc(100vh - 220px); /* Adjust 220px based on your modal's header/footer height */
            overflow-y: auto;
        }

        /* Attempt to fix select dropdown text clipping */
        select.form-control {
            height: auto !important;
            padding-top: .5rem;
            padding-bottom: .5rem;
        }
        select.form-control:not([size]):not([multiple]) {
            height: calc(1.5em + 1rem + 2px); /* Bootstrap standard, ensure it applies */
        }
    </style>
</head>
<body>
    <!-- Navbar section is now fully managed by common.js -->

    <div class="container-fluid">
        <div class="row">
            <!-- 页面标题和说明 -->
            <div class="col-12 mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">智能组卷系统</h2>
                        <p class="text-muted mb-0">从知识点库中智能生成符合教学需求的标准试卷，支持多种题型和难度配置。</p>
                    </div>
                    <div class="d-flex align-items-center">
                        <button id="customPaperBtn" class="btn btn-primary btn-lg mr-3">
                            <i class="fas fa-magic mr-2"></i>自由组卷
                        </button>
                        <div class="text-primary" style="font-size: 3rem;">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 左侧知识点分类列表 -->
            <div class="col-md-3 col-lg-3 mb-4">
                <div class="card sidebar h-100">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group mr-2"></i>知识点分类
                            </h5>
                            <button class="btn btn-sm btn-primary" id="addGroupBtn">
                                <i class="fas fa-plus mr-1"></i> 新增分类
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-3">
                        <div class="form-group">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                                <input type="text" id="groupSearchInput" class="form-control" placeholder="搜索分类..." autocomplete="off">
                                <div class="input-group-append search-actions">
                                    <button id="groupSearchClearBtn" class="btn btn-outline-secondary d-none" type="button" title="清除搜索">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button id="groupSearchBtn" class="btn btn-primary" type="button">
                                        <i class="fas fa-search mr-1"></i> 搜索
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">提示: 您可以搜索分类名称</small>
                        </div>
                        <div id="groupList" class="mt-2">
                            <!-- 知识点分类列表将由JS动态加载 -->
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-spinner fa-spin mb-2" style="font-size: 1.5rem;"></i>
                                <p class="mb-0">正在加载分类...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧知识点详情 -->
            <div class="col-md-9 col-lg-9">
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0" id="selectedGroupName">
                                <i class="fas fa-folder-open mr-2"></i>请选择知识点分类
                            </h5>
                            <div>
                                <button class="btn btn-primary mr-2" id="generatePaperBtn" disabled>
                                    <i class="fas fa-file-alt mr-1"></i> 智能组卷
                                </button>
                                <button class="btn btn-success" id="addKnowledgeBtn" disabled>
                                    <i class="fas fa-plus mr-1"></i> 添加知识点
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bg-white border-right-0">
                                            <i class="fas fa-search text-muted"></i>
                                        </span>
                                    </div>
                                    <input type="text" class="form-control border-left-0" id="knowledgeSearchInput" placeholder="搜索知识点...">
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <div class="btn-group btn-group-sm" role="group" aria-label="知识点筛选">
                                    <button type="button" class="btn btn-outline-secondary active" data-filter="all">全部</button>
                                    <button type="button" class="btn btn-outline-secondary" data-filter="free">免费</button>
                                    <button type="button" class="btn btn-outline-secondary" data-filter="paid">付费</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-container">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th width="5%"><input type="checkbox" id="selectAllCheckbox" title="全选/取消全选"></th>
                                        <th width="10%">知识点ID</th>
                                        <th width="20%">名称</th>
                                        <th width="10%">状态</th>
                                        <th width="10%">题目数</th>
                                        <th width="10%">排序</th>
                                        <th width="15%">创建时间</th>
                                        <th width="20%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="knowledgeList">
                                    <tr>
                                        <td colspan="8" class="text-center">请选择知识点分类</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 试卷历史记录 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-history mr-2"></i>试卷历史记录
                            </h5>
                            <button class="btn btn-sm btn-info" id="refreshPaperHistoryBtn">
                                <i class="fas fa-sync-alt mr-1"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-container">
                            <div class="p-3 bg-light border-bottom">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text bg-white border-right-0">
                                                    <i class="fas fa-search text-muted"></i>
                                                </span>
                                            </div>
                                            <input type="text" class="form-control border-left-0" id="paperSearchInput" placeholder="搜索试卷标题或内容..." autocomplete="off">
                                            <div class="input-group-append">
                                                <button id="paperSearchClearBtn" class="btn btn-outline-secondary d-none" type="button">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted mt-1">
                                            <i class="fas fa-info-circle mr-1"></i> 输入关键词搜索试卷标题或内容
                                        </small>
                                    </div>
                                    <div class="col-md-6 d-flex justify-content-end">
                                        <div class="btn-group btn-group-sm mr-2" role="group" aria-label="试卷类型筛选">
                                            <button type="button" class="btn btn-outline-secondary active" data-paper-filter="all">全部</button>
                                            <button type="button" class="btn btn-outline-secondary" data-paper-filter="0">普通试卷</button>
                                            <button type="button" class="btn btn-outline-secondary" data-paper-filter="1">教师试卷</button>
                                            <button type="button" class="btn btn-outline-secondary" data-paper-filter="2">标准试卷</button>
                                        </div>
                                        <select class="form-control form-control-sm w-auto" id="paperSortSelect">
                                            <option value="time-desc">时间降序</option>
                                            <option value="time-asc">时间升序</option>
                                            <option value="score-desc">总分降序</option>
                                            <option value="score-asc">总分升序</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th width="5%">ID</th>
                                        <th width="20%">试卷标题</th>
                                        <th width="10%">类型</th>
                                        <th width="10%">总分</th>
                                        <th width="10%">难度</th>
                                        <th width="15%">创建时间</th>
                                        <th width="30%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="paperHistoryTable">
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>正在加载试卷历史...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <nav id="paperHistoryPagination" aria-label="试卷历史分页"></nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 知识点编辑模态框 -->
    <div class="modal fade" id="knowledgeEditModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit mr-2"></i><span id="editKnowledgeTitle">编辑知识点</span>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="knowledgeEditForm">
                        <input type="hidden" id="editKnowledgeId">
                        <div class="form-group">
                            <label for="editKnowledgeName">知识点名称</label>
                            <input type="text" class="form-control" id="editKnowledgeName" required>
                        </div>
                        <div class="form-group">
                            <label for="editGroupName">所属分类</label>
                            <select class="form-control" id="editGroupName" required>
                                <option value="">请选择分类</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editIsFree">状态</label>
                            <select class="form-control" id="editIsFree">
                                <option value="1">免费</option>
                                <option value="0">付费</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editSort">排序值</label>
                            <input type="number" class="form-control" id="editSort" min="1" value="1">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveKnowledgeBtn">
                        <i class="fas fa-save mr-1"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 自由组卷按钮已移至页面顶部 -->

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">正在加载...</h5>
                </div>
                <div class="modal-body text-center">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <p id="loadingMessage">正在处理您的请求...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 自由组卷模态框 -->
    <div class="modal fade" id="customPaperModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-puzzle-piece mr-2"></i>自由组卷
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
                    <form id="customPaperForm">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customPaperTitle">试卷标题</label>
                                    <input type="text" class="form-control" id="customPaperTitle" required placeholder="例如：高一数学期末测试">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="customPaperType">试卷类型</label>
                                    <select class="form-control" id="customPaperType">
                                        <option value="0">普通试卷</option>
                                        <option value="1">教师专用</option>
                                        <option value="2">标准考试</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="customPaperFormat">输出格式</label>
                                    <select class="form-control" id="customPaperFormat">
                                        <option value="pdf">PDF文档</option>
                                        <option value="word">Word文档</option>
                                        <option value="html">网页版</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 难度分布设置 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie mr-2"></i>难度分布
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="easyPercentage">简单题比例 (%)</label>
                                            <input type="number" class="form-control difficulty-input" id="easyPercentage" min="0" max="100" value="30">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="mediumPercentage">中等题比例 (%)</label>
                                            <input type="number" class="form-control difficulty-input" id="mediumPercentage" min="0" max="100" value="50">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="hardPercentage">困难题比例 (%)</label>
                                            <input type="number" class="form-control difficulty-input" id="hardPercentage" min="0" max="100" value="20">
                                        </div>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 30px;">
                                    <div class="progress-bar bg-success" id="easyProgressBar" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">简单 30%</div>
                                    <div class="progress-bar bg-warning" id="mediumProgressBar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">中等 50%</div>
                                    <div class="progress-bar bg-danger" id="hardProgressBar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">困难 20%</div>
                                </div>
                                <div id="difficultyError" class="text-danger mt-2" style="display: none;"></div>
                            </div>
                        </div>

                        <!-- 题型配置 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-list-ol mr-2"></i>题型配置
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>题型</th>
                                                <th width="150">数量</th>
                                                <th width="150">每题分值</th>
                                                <th width="150">总分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>单选题</td>
                                                <td>
                                                    <input type="number" class="form-control question-count" id="customSingleChoiceCount" min="0" value="10">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control question-score" id="customSingleChoiceScore" min="0" value="3">
                                                </td>
                                                <td class="type-total-score">30</td>
                                            </tr>
                                            <tr>
                                                <td>多选题</td>
                                                <td>
                                                    <input type="number" class="form-control question-count" id="customMultipleChoiceCount" min="0" value="5">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control question-score" id="customMultipleChoiceScore" min="0" value="4">
                                                </td>
                                                <td class="type-total-score">20</td>
                                            </tr>
                                            <tr>
                                                <td>判断题</td>
                                                <td>
                                                    <input type="number" class="form-control question-count" id="customJudgmentCount" min="0" value="5">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control question-score" id="customJudgmentScore" min="0" value="2">
                                                </td>
                                                <td class="type-total-score">10</td>
                                            </tr>
                                            <tr>
                                                <td>填空题</td>
                                                <td>
                                                    <input type="number" class="form-control question-count" id="customFillCount" min="0" value="3">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control question-score" id="customFillScore" min="0" value="3">
                                                </td>
                                                <td class="type-total-score">9</td>
                                            </tr>
                                            <tr>
                                                <td>简答题</td>
                                                <td>
                                                    <input type="number" class="form-control question-count" id="customShortAnswerCount" min="0" value="2">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control question-score" id="customShortAnswerScore" min="0" value="10">
                                                </td>
                                                <td class="type-total-score">20</td>
                                            </tr>
                                            <tr>
                                                <td>主观题</td>
                                                <td>
                                                    <input type="number" class="form-control question-count" id="customSubjectiveCount" min="0" value="1">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control question-score" id="customSubjectiveScore" min="0" value="15">
                                                </td>
                                                <td class="type-total-score">15</td>
                                            </tr>
                                        </tbody>
                                        <tfoot class="bg-light">
                                            <tr>
                                                <th colspan="3" class="text-right">总分：</th>
                                                <th id="customTotalScore">104</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 知识点配置 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-brain mr-2"></i>知识点配置
                                </h5>
                                <button type="button" class="btn btn-primary btn-sm" id="addKnowledgePointBtn">
                                    <i class="fas fa-plus mr-1"></i>添加知识点
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>请为每个知识点设置题目数量，并选择是否包含简答题。
                                </div>

                                <!-- 知识点列表容器 -->
                                <div id="knowledgePointsContainer" class="mb-3">
                                    <!-- 知识点项将通过JavaScript动态添加 -->
                                    <div class="text-center text-muted py-5" id="noKnowledgePointsMessage">
                                        <i class="fas fa-brain fa-3x mb-3"></i>
                                        <p>暂无知识点，请点击"添加知识点"按钮添加</p>
                                    </div>
                                </div>

                                <!-- 知识点搜索结果 -->
                                <div id="knowledgeSearchResults" class="mt-3 d-none">
                                    <h6 class="border-bottom pb-2">搜索结果：</h6>
                                    <div id="knowledgeSearchResultsList" class="list-group">
                                        <!-- 搜索结果将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="generateCustomPaperBtn">
                        <i class="fas fa-magic mr-1"></i>生成试卷
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 知识点选择模态框 -->
    <div class="modal fade" id="knowledgePointSelectionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-brain mr-2"></i>选择知识点
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle mr-1"></i> 提示：您可以选择多个知识点一起添加
                    </div>
                    <div class="form-group">
                        <input type="text" class="form-control" id="knowledgePointSearchInput" placeholder="搜索知识点...">
                    </div>
                    <div id="selected-counter-container" class="mb-3" style="display: none;">
                        <span class="badge badge-primary p-2">
                            <i class="fas fa-check-circle mr-1"></i> 已选择 <span id="selected-counter">0</span> 个知识点
                        </span>
                    </div>
                    <div class="knowledge-point-list">
                        <div class="text-center py-4" id="loadingKnowledgePoints">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载知识点...</p>
                        </div>
                        <div id="knowledgePointsList" class="list-group">
                            <!-- 知识点将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
                <!-- Modal footer removed as requested -->
            </div>
        </div>
    </div>

    <!-- 知识点项模板 -->
    <template id="knowledgePointItemTemplate">
        <div class="card mb-3 knowledge-point-item" data-id="{id}">
            <div class="card-header bg-light py-2 px-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-primary">
                        <i class="fas fa-brain mr-2"></i>{name}
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-knowledge-point" title="移除此知识点">
                            <i class="fas fa-times mr-1"></i>移除
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body py-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>
                                <i class="fas fa-hashtag mr-1"></i>题目数量
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <button type="button" class="btn btn-outline-secondary decrease-count">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                                <input type="number" class="form-control knowledge-question-count" min="0" value="5">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary increase-count">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>
                                <i class="fas fa-file-alt mr-1"></i>题型设置
                            </label>
                            <div class="custom-control custom-switch mt-2">
                                <input type="checkbox" class="custom-control-input include-short-answer" id="includeShortAnswer{id}">
                                <label class="custom-control-label" for="includeShortAnswer{id}">包含简答题</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 试卷生成模态框 -->
    <div class="modal fade" id="paperGenerationModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-alt mr-2"></i>智能组卷
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="paperGenerationForm">
                        <input type="hidden" id="knowledgeId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="paperTitle">试卷标题</label>
                                    <input type="text" class="form-control" id="paperTitle" required placeholder="例如：高一数学期末测试">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="paperType">试卷类型</label>
                                    <select class="form-control" id="paperType">
                                        <option value="0">普通试卷</option>
                                        <option value="1">教师专用</option>
                                        <option value="2">标准考试</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                               <div class="form-group">
                                   <label for="paperFormat">输出格式</label>
                                   <select class="form-control" id="paperFormat">
                                       <option value="pdf">PDF文档</option>
                                       <option value="word">Word文档</option>
                                       <option value="html">网页版</option>
                                   </select>
                               </div>
                           </div>
                            <div class="col-md-6">
                                <!-- Potentially other fields here -->
                            </div>
                        </div>

                        <ul class="nav nav-tabs mb-3" id="paperConfigTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="difficulty-tab" data-toggle="tab" href="#difficulty" role="tab" aria-controls="difficulty" aria-selected="true">难度配置</a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="questiontypes-tab" data-toggle="tab" href="#questiontypes" role="tab" aria-controls="questiontypes" aria-selected="false">题型配置</a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="preview-tab" data-toggle="tab" href="#preview" role="tab" aria-controls="preview" aria-selected="false">试卷预览</a>
                            </li>
                        </ul>

                        <div class="tab-content" id="paperConfigTabContent">
                            <!-- 难度配置标签页 -->
                            <div class="tab-pane fade show active" id="difficulty" role="tabpanel" aria-labelledby="difficulty-tab">
                        <div class="card mb-3">
                            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-sliders-h mr-2 text-primary"></i>
                                    难度配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="easyPercentage">简单题比例:</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="easyPercentage" min="0" max="100" value="30">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="mediumPercentage">中等题比例:</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="mediumPercentage" min="0" max="100" value="50">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="hardPercentage">困难题比例:</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="hardPercentage" min="0" max="100" value="20">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="difficultyError" class="text-danger mt-2" style="display: none;"></div>

                                        <div class="row">
                                            <div class="col-12">
                                                <div class="difficulty-distribution-chart mt-3">
                                                    <div class="progress" style="height: 30px;">
                                                        <div class="progress-bar bg-success" id="easyBar" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">简单 30%</div>
                                                        <div class="progress-bar bg-warning" id="mediumBar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">中等 50%</div>
                                                        <div class="progress-bar bg-danger" id="hardBar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">困难 20%</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                        </div>

                            <!-- 题型配置标签页 -->
                            <div class="tab-pane fade" id="questiontypes" role="tabpanel" aria-labelledby="questiontypes-tab">
                        <!-- 题型可用性警告区 -->
                        <div class="alert alert-info mb-3" id="questionTypeAvailabilityWarning" style="display:none;">
                            <div class="d-flex">
                                <div class="mr-3">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">题型可用性提示</h5>
                                    <p class="mb-1">系统将尝试根据您的配置生成试卷，但请注意以下事项：</p>
                                    <ul class="mb-0">
                                        <li>如果知识点的题库中某种题型的题目数量不足，系统将使用所有可用题目</li>
                                        <li>题型完全缺失时，该题型将被跳过，并在生成后显示详细警告</li>
                                        <li>为获得最佳结果，建议确保题库中有足够的各类题型</li>
                                    </ul>
                                    <div class="mt-2 p-2 bg-light rounded small" id="typeAvailabilityDetails">
                                        <p class="mb-1"><strong>正在分析当前知识点的题库容量...</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">题型配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle mr-1"></i> 请输入各类题目的数量和分值，系统将根据您的设置生成试卷。
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="thead-light">
                                            <tr>
                                                <th width="40%">题型</th>
                                                <th width="30%">数量</th>
                                                <th width="30%">分值</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>单选题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="singleChoiceCount" min="0" value="10" data-type="singleChoice"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="singleChoiceScore" min="0" value="3" data-type="singleChoice"></td>
                                            </tr>
                                            <tr>
                                                <td>多选题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="multipleChoiceCount" min="0" value="5" data-type="multipleChoice"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="multipleChoiceScore" min="0" value="4" data-type="multipleChoice"></td>
                                            </tr>
                                            <tr>
                                                <td>判断题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="judgmentCount" min="0" value="10" data-type="judgment"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="judgmentScore" min="0" value="2" data-type="judgment"></td>
                                            </tr>
                                            <tr>
                                                <td>填空题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="fillCount" min="0" value="0" data-type="fillBlank"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="fillScore" min="0" value="3" data-type="fillBlank"></td>
                                            </tr>
                                            <tr>
                                                <td>简答题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="shortAnswerCount" min="0" value="3" data-type="shortAnswer"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="shortAnswerScore" min="0" value="10" data-type="shortAnswer"></td>
                                            </tr>
                                            <tr>
                                                <td>主观题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="subjectiveCount" min="0" value="0" data-type="subjective"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="subjectiveScore" min="0" value="10" data-type="subjective"></td>
                                            </tr>
                                            <tr>
                                                <td>组合题</td>
                                                <td><input type="number" class="form-control form-control-sm question-count" id="groupCount" min="0" value="0" data-type="groupQuestion"></td>
                                                <td><input type="number" class="form-control form-control-sm question-score" id="groupScore" min="0" value="10" data-type="groupQuestion"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                        <div class="text-right mt-3">
                                            <h5>总分: <span id="totalScore" class="text-primary">100</span></h5>
                                </div>
                                </div>
                                </div>
                            </div>

                            <!-- 试卷预览标签页 -->
                            <div class="tab-pane fade" id="preview" role="tabpanel" aria-labelledby="preview-tab">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">试卷预览</h6>
                        </div>
                                    <div class="card-body">
                                        <div class="paper-preview">
                                            <div class="text-center mb-4">
                                                <h4 id="previewTitle">试卷标题</h4>
                                                <p class="mb-0">总分: <span id="previewTotalScore">100</span> | 题目数量: <span id="previewQuestionCount">28</span></p>
                    </div>

                                            <div class="table-responsive">
                                                <table class="table table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th width="15%">题型</th>
                                                            <th width="15%">数量</th>
                                                            <th width="15%">单题分数</th>
                                                            <th width="15%">总分</th>
                                                            <th width="40%">说明</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="previewTable">
                                                        <!-- 将由JS动态生成 -->
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="mt-4">
                                                <h5>难度分布:</h5>
                                                <div class="progress mb-3" style="height: 25px;">
                                                    <div class="progress-bar bg-success" id="previewEasyBar" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">简单 30%</div>
                                                    <div class="progress-bar bg-warning" id="previewMediumBar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">中等 50%</div>
                                                    <div class="progress-bar bg-danger" id="previewHardBar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">困难 20%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                                </div>
                        </div>
                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                    <button type="button" class="btn btn-primary" id="submitPaperGenerationBtn"><i class="fas fa-cogs mr-1"></i>生成试卷</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-5">
                    <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <h5 class="mt-3" id="loadingMessage">正在智能组卷中，请稍候...</h5>
                    <p class="text-muted mt-2" id="loadingSubmessage">这可能需要一点时间，取决于试卷复杂度和服务器负载</p>
                    <div class="progress mt-4">
                        <div id="loadingProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:inline="none">
        // Utility functions (showSuccess, showError, formatDate, padZero, etc.)
        // These should be defined here if not already present from previous steps.
        // For brevity, assuming they are present. If not, they need to be added.

        function showSuccess(title, message) {
            if (!$('#successModal').length) {
                $('body').append(`
                    <div class="modal fade" id="successModal" tabindex="-1">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header bg-success text-white">
                                    <h5 class="modal-title"><i class="fas fa-check-circle mr-2"></i><span id="successTitle"></span></h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                </div> <div class="modal-body"><p id="successMessage"></p></div>
                                <div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button></div>
                            </div></div></div>`);
            }
            $('#successTitle').text(title); $('#successMessage').html(message); // Use .html() for message to allow simple HTML
            $('#successModal').modal('show');
        }

        function showError(title, message) {
            // Use SweetAlert2 for error messages with better UX
            Swal.fire({
                title: title,
                text: message,
                icon: 'error',
                confirmButtonText: '确定',
                confirmButtonColor: '#d33'
            });
        }

        function showLoadingModal(message = '处理中...', subMessage = '请稍候...') {
            $('#loadingMessage').text(message);
            $('#loadingSubmessage').text(subMessage);
            $('#loadingProgressBar').css('width', '0%').removeClass('bg-success bg-danger').addClass('progress-bar-animated');
            $('#loadingModal').modal('show');

            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                if (progress <= 75) { // Simulate progress but don't complete fully until explicitly told
                    $('#loadingProgressBar').css('width', progress + '%');
                }
            }, 200);

            return function completeLoading(success) {
                clearInterval(interval);
                $('#loadingProgressBar').css('width', '100%').removeClass('progress-bar-animated');
                if (success) {
                    $('#loadingProgressBar').addClass('bg-success');
                } else {
                    $('#loadingProgressBar').addClass('bg-danger');
                }
                setTimeout(() => {
                    $('#loadingModal').modal('hide');
                }, success ? 500 : 1500); // Hide quicker on success
            };
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            var date = new Date(dateString);
            return date.getFullYear() + '-' + padZero(date.getMonth() + 1) + '-' + padZero(date.getDate()) + ' ' + padZero(date.getHours()) + ':' + padZero(date.getMinutes());
        }
        // 改进的padZero函数，确保返回字符串
        function padZero(num) { return num < 10 ? '0' + num : num.toString(); }
        // 改进的难度文本转换函数
        function getDifficultyText(difficulty) {
            // 确保同时处理小数(0-1)和整数(1-5)格式
            if (difficulty === null || difficulty === undefined) return 'N/A';

            // 处理 0-1 的浮点数格式
            if (difficulty >= 0 && difficulty <= 1) {
                if (difficulty <= 0.2) return '简单';
                if (difficulty <= 0.4) return '较简单';
                if (difficulty <= 0.6) return '中等';
                if (difficulty <= 0.8) return '较难';
                return '困难';
            }
            // 处理 1-5 的整数格式
            else {
                switch(parseInt(difficulty)) {
                    case 1: return '简单';
                    case 2: return '较简单';
                    case 3: return '中等';
                    case 4: return '较难';
                    case 5: return '困难';
                    default: return `未知 (${difficulty})`;
                }
            }
        }

        // Placeholder for updateDifficultyChart
        // 增强的难度分布图表更新函数
        function updateDifficultyChart() {
            const easyPercent = parseInt($('#easyPercentage').val()) || 0;
            const mediumPercent = parseInt($('#mediumPercentage').val()) || 0;
            const hardPercent = parseInt($('#hardPercentage').val()) || 0;
            const total = easyPercent + mediumPercent + hardPercent;

            // 更新难度分布条形图
            $('.difficulty-percentage-chart .easy-bar').css('width', easyPercent + '%')
                .attr('title', '简单: ' + easyPercent + '%')
                .attr('aria-valuenow', easyPercent);

            $('.difficulty-percentage-chart .medium-bar').css('width', mediumPercent + '%')
                .attr('title', '中等: ' + mediumPercent + '%')
                .attr('aria-valuenow', mediumPercent);

            $('.difficulty-percentage-chart .hard-bar').css('width', hardPercent + '%')
                .attr('title', '困难: ' + hardPercent + '%')
                .attr('aria-valuenow', hardPercent);

            // 更新显示的百分比值
            $('#easyPercent').text(easyPercent + '%');
            $('#mediumPercent').text(mediumPercent + '%');
            $('#hardPercent').text(hardPercent + '%');

            // 同步更新预览面板中的进度条（如果存在）
            $('#previewEasyBar').css('width', `${easyPercent}%`);
            $('#previewMediumBar').css('width', `${mediumPercent}%`);
            $('#previewHardBar').css('width', `${hardPercent}%`);

            // 校验总和是否为100%
            if (total !== 100) {
                $('#difficultyWarning').show();
                $('#difficultyWarningText').text(`当前难度分布总和为${total}%，应等于100%`);
            } else {
                $('#difficultyWarning').hide();
            }
        }

                // 计算总分并更新显示
        // 验证题型配置
        function validateQuestionCounts() {
            let isValid = true;
            let totalQuestions = 0;

            // 检查每个题型的数量是否为有效数字
            $('.question-count').each(function() {
                const count = parseInt($(this).val());
                if (isNaN(count) || count < 0) {
                    $(this).addClass('is-invalid');
                    isValid = false;
                } else {
                    $(this).removeClass('is-invalid');
                    totalQuestions += count;
                }
            });

            // 检查总题目数量是否为0
            if (totalQuestions === 0) {
                $('.question-count').first().addClass('is-invalid');
                Swal.fire({
                    icon: 'error',
                    title: '配置错误',
                    text: '至少需要一道题目才能生成试卷！',
                    confirmButtonText: '确定'
                });
                return false;
            }

            // 检查每个题型的分值是否为有效数字
            $('.question-score').each(function() {
                const score = parseInt($(this).val());
                if (isNaN(score) || score < 0) {
                    $(this).addClass('is-invalid');
                    isValid = false;
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                Swal.fire({
                    icon: 'error',
                    title: '配置错误',
                    text: '请检查题型配置，确保所有数量和分值都是有效的非负数！',
                    confirmButtonText: '确定'
                });
                return false;
            }

            return true;
        }

        // 更新总分显示
        function updateTotalScore() {
            let total = 0;
            total += parseInt($('#singleChoiceCount').val() || 0) * parseInt($('#singleChoiceScore').val() || 0);
            total += parseInt($('#multipleChoiceCount').val() || 0) * parseInt($('#multipleChoiceScore').val() || 0);
            total += parseInt($('#judgmentCount').val() || 0) * parseInt($('#judgmentScore').val() || 0);
            total += parseInt($('#fillCount').val() || 0) * parseInt($('#fillScore').val() || 0);
            total += parseInt($('#shortAnswerCount').val() || 0) * parseInt($('#shortAnswerScore').val() || 0);
            total += parseInt($('#subjectiveCount').val() || 0) * parseInt($('#subjectiveScore').val() || 0);
            total += parseInt($('#groupCount').val() || 0) * parseInt($('#groupScore').val() || 0);
            $('#totalScore').text(total);

            // 高亮显示总分，提供视觉反馈
            $('#totalScore').addClass('font-weight-bold');
            setTimeout(() => {
                $('#totalScore').removeClass('font-weight-bold');
            }, 500);
        }

        function calculateTotalScore() {
            // 获取题型数据
            const typeData = [
                { type: '单选题', count: parseInt($('#singleChoiceCount').val()) || 0, score: parseFloat($('#singleChoiceScore').val()) || 0 },
                { type: '多选题', count: parseInt($('#multipleChoiceCount').val()) || 0, score: parseFloat($('#multipleChoiceScore').val()) || 0 },
                { type: '判断题', count: parseInt($('#judgmentCount').val()) || 0, score: parseFloat($('#judgmentScore').val()) || 0 },
                { type: '填空题', count: parseInt($('#fillCount').val()) || 0, score: parseFloat($('#fillScore').val()) || 0 },
                { type: '简答题', count: parseInt($('#shortAnswerCount').val()) || 0, score: parseFloat($('#shortAnswerScore').val()) || 0 },
                { type: '主观题', count: parseInt($('#subjectiveCount').val()) || 0, score: parseFloat($('#subjectiveScore').val()) || 0 },
                { type: '综合题', count: parseInt($('#groupCount').val()) || 0, score: parseFloat($('#groupScore').val()) || 0 }
            ];

            // 计算总分
            let totalScore = 0;
            typeData.forEach(item => {
                totalScore += item.count * item.score;
            });

            // 更新总分显示
            $('#totalScore').text(totalScore);

            return typeData;
        }

        // 更新题型分布图表
        function updateQuestionDistributionChart(inputData) {
            // 获取题型数据 - 使用传入的参数或重新计算
            const typeData = inputData || calculateTotalScore();

            // 筛选出数量大于0的题型
            const validTypes = typeData.filter(item => item.count > 0);

            // 获取图表容器
            const $chartContainer = $('#questionDistributionChart');
            $chartContainer.empty();

            if (validTypes.length === 0) {
                $chartContainer.html('<div class="text-center text-muted py-5">暂无题型数据</div>');
                return;
            }

            // 创建简单的柱状图
            const $chart = $('<div class="d-flex align-items-end justify-content-around h-100 pb-4"></div>');
            $chartContainer.append($chart);

            // 定义颜色映射
            const colors = ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#6f42c1', '#fd7e14'];

            // 最大值用于计算高度比例
            const maxCount = Math.max(...validTypes.map(item => item.count));

            // 创建每个柱子
            validTypes.forEach((item, index) => {
                const heightPercent = (item.count / maxCount) * 80; // 最高80%，留出空间显示标签
                const color = colors[index % colors.length];

                $chart.append(`
                    <div class="text-center" style="width: ${100 / validTypes.length}%; max-width: 100px;">
                        <div class="mx-auto" style="height: ${heightPercent}%; background-color: ${color}; width: 30px;"></div>
                        <div class="mt-2 small">${item.type}</div>
                        <div class="small text-muted">${item.count}道</div>
                    </div>
                `);
            });
        }

        // 更新试卷预览
        function updatePaperPreview() {

            // 获取题型数据和计算总分
            const typeData = calculateTotalScore();

            // 更新题型分布图表
            updateQuestionDistributionChart(typeData);

            // 更新试卷预览内容
            const $preview = $('#preview');
            if ($preview.find('.preview-content').length === 0) {
                $preview.html('<div class="preview-content p-3"></div>');
            }
            const $previewContent = $preview.find('.preview-content');

            $previewContent.empty();

            // 添加试卷标题
            const paperTitle = $('#paperTitle').val() || '无标题试卷';
            $previewContent.append(`<h3 class="text-center mb-4">${paperTitle}</h3>`);

            // 添加难度分布
            const easyPercent = parseInt($('#easyPercentage').val()) || 0;
            const mediumPercent = parseInt($('#mediumPercentage').val()) || 0;
            const hardPercent = parseInt($('#hardPercentage').val()) || 0;

            $previewContent.append(`
                <div class="mb-4">
                    <h5>难度分布</h5>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: ${easyPercent}%" title="简单: ${easyPercent}%">简单: ${easyPercent}%</div>
                        <div class="progress-bar bg-warning" style="width: ${mediumPercent}%" title="中等: ${mediumPercent}%">中等: ${mediumPercent}%</div>
                        <div class="progress-bar bg-danger" style="width: ${hardPercent}%" title="困难: ${hardPercent}%">困难: ${hardPercent}%</div>
                    </div>
                </div>
            `);

            // 计算总题数和总分
            const totalQuestions = typeData.reduce((sum, item) => sum + item.count, 0);
            const totalScoreValue = typeData.reduce((sum, item) => sum + (item.count * item.score), 0);

            // 添加题型预览表格
            $previewContent.append(`
                <div class="mb-4">
                    <h5>题型分布</h5>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>题型</th>
                                <th>数量</th>
                                <th>每题分数</th>
                                <th>总分</th>
                            </tr>
                        </thead>
                        <tbody id="previewTableBody">
                        </tbody>
                        <tfoot>
                            <tr class="font-weight-bold bg-light">
                                <td>总计</td>
                                <td>${totalQuestions}道题</td>
                                <td></td>
                                <td>${formatNumber(totalScoreValue)}分</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            `);

            // 格式化数字的辅助函数
            function formatNumber(num) {
                // 如果是整数，显示为整数；否则保留一位小数
                return Number.isInteger(num) ? num : num.toFixed(1);
            }

            // 填充题型表格
            const $tableBody = $('#previewTableBody');
            $tableBody.empty(); // 确保表格体为空再填充

            // 检查是否有有效题型数据
            if (typeData.filter(item => item.count > 0).length === 0) {
                $tableBody.append(`
                    <tr>
                        <td colspan="4" class="text-center text-muted py-3">
                            <i class="fas fa-info-circle mr-1"></i> 请添加题目数量
                        </td>
                    </tr>
                `);
            } else {
                // 填充有效的题型数据
                typeData.forEach(item => {
                    if (item.count > 0) {
                        const subtotal = item.count * item.score;
                        $tableBody.append(`
                            <tr>
                                <td>${item.type}</td>
                                <td>${item.count}道</td>
                                <td>${formatNumber(item.score)}分</td>
                                <td>${formatNumber(subtotal)}分</td>
                            </tr>
                        `);
                    }
                });
            }

            // 更新题型分布图表
            updateQuestionDistributionChart(typeData);
        }

        // Function to reset the paper generation form
        function resetPaperGenerationForm() {
            console.log("Resetting paper generation form...");
            const form = $('#paperGenerationForm');
            form[0].reset(); // Resets most standard form fields

            // Manually reset fields not covered by form.reset() or to set specific defaults
            $('#knowledgeId').val(''); // Clear hidden knowledge ID
            $('#paperTitle').val('');
            $('#paperType').val('0'); // Default to '普通试卷'
            $('#paperFormat').val('pdf'); // Default to 'PDF'

            // Reset difficulty
            $('#easyPercentage').val('30');
            $('#mediumPercentage').val('50');
            $('#hardPercentage').val('20');
            updateDifficultyChart();

            // Reset question types (example for one, repeat for all)
            $('#singleChoiceCount').val('10'); $('#singleChoiceScore').val('3');
            $('#multipleChoiceCount').val('5'); $('#multipleChoiceScore').val('4');
            $('#judgmentCount').val('10'); $('#judgmentScore').val('2');
            $('#fillCount').val('0'); $('#fillScore').val('3');
            $('#shortAnswerCount').val('3'); $('#shortAnswerScore').val('10');
            $('#subjectiveCount').val('0'); $('#subjectiveScore').val('10');
            $('#groupCount').val('0'); $('#groupScore').val('10');

            updatePaperPreview(); // Update preview based on reset values

            // Reset tabs to the first one (Difficulty)
            $('#paperConfigTabs a[href="#difficulty"]').tab('show');
            $('#difficultyError').hide().text('');
        }

        // 全局函数：显示试卷生成模态框
        function openPaperGenerationModal(knowledgeConfigs, paperTitle) {
            console.log('全局函数openPaperGenerationModal被调用:', knowledgeConfigs, paperTitle);

            // 保存知识点配置到全局变量
            window.currentKnowledgeConfigs = knowledgeConfigs;

            // 填充表单字段
            $('#paperTitle').val(paperTitle || '自定义试卷');

            // 设置默认值
            $('#easyPercentage').val(30);
            $('#mediumPercentage').val(50);
            $('#hardPercentage').val(20);

            $('#singleChoiceCount').val(5);
            $('#singleChoiceScore').val(3);

            $('#multipleChoiceCount').val(3);
            $('#multipleChoiceScore').val(3);

            $('#judgmentCount').val(5);
            $('#judgmentScore').val(2);

            $('#fillCount').val(3);
            $('#fillScore').val(3);

            $('#shortAnswerCount').val(2);
            $('#shortAnswerScore').val(5);

            // 显示模态框
            $('#paperGenerationModal').modal('show');

            // 更新图表和预览
            setTimeout(function() {
                updatePaperPreview();
                updateDifficultyChart();
            }, 200);

            // 返回知识点配置便于链式调用
            return knowledgeConfigs;
        }

        // 将函数挂载到全局作用域
        window.openPaperGenerationModal = openPaperGenerationModal;

        $(document).ready(function() {
            // Initialize variables
            let currentPage = 0; // For paper history pagination
            const pageSize = 10; // For paper history pagination

            /**
             * Function to show paper templates modal with given knowledge configs
             * @param {Array} knowledgeConfigs - Array of knowledge point configurations
             * @param {String} defaultTitle - Default title for the paper
             */
            function showPaperTemplates(knowledgeConfigs, defaultTitle) {
                // Set the title
                $('#paperTitle').val(defaultTitle || "自定义试卷");

                // Store the knowledge configs for later use
                window.currentKnowledgeConfigs = knowledgeConfigs;

                // Set default values for fields if empty
                if ($('#easyPercentage').val() === '') $('#easyPercentage').val(30);
                if ($('#mediumPercentage').val() === '') $('#mediumPercentage').val(50);
                if ($('#hardPercentage').val() === '') $('#hardPercentage').val(20);

                if ($('#singleChoiceCount').val() === '') $('#singleChoiceCount').val(5);
                if ($('#singleChoiceScore').val() === '') $('#singleChoiceScore').val(3);

                if ($('#multipleChoiceCount').val() === '') $('#multipleChoiceCount').val(5);
                if ($('#multipleChoiceScore').val() === '') $('#multipleChoiceScore').val(3);

                if ($('#judgmentCount').val() === '') $('#judgmentCount').val(5);
                if ($('#judgmentScore').val() === '') $('#judgmentScore').val(2);

                if ($('#fillCount').val() === '') $('#fillCount').val(3);
                if ($('#fillScore').val() === '') $('#fillScore').val(3);

                if ($('#shortAnswerCount').val() === '') $('#shortAnswerCount').val(2);
                if ($('#shortAnswerScore').val() === '') $('#shortAnswerScore').val(5);

                // Show the modal
                $('#paperGenerationModal').modal('show');

                // Update preview
                updatePaperPreview();
            }

            // Ensure the showPaperTemplates function is globally accessible
            window.showPaperTemplates = showPaperTemplates;

            // Paper history and knowledge groups are loaded at the end of the script.

            // 分类搜索功能
            $('#groupSearchInput').on('input', function() {
                const searchTerm = $(this).val().toLowerCase().trim();

                $('.group-item').each(function() {
                    const groupName = $(this).text().toLowerCase();
                    const matches = groupName.includes(searchTerm);
                    $(this).toggle(matches);
                });

                // 显示或隐藏无结果提示
                if ($('.group-item:visible').length === 0 && searchTerm !== '') {
                    if ($('#noGroupResults').length === 0) {
                        $('#groupList').append(`<div id="noGroupResults" class="alert alert-info mt-2">没有匹配"${escapeHtml(searchTerm)}"的分类</div>`);
                    }
                } else {
                    $('#noGroupResults').remove();
                }
            });

            // 知识点筛选功能
            $('.btn-group[aria-label="知识点筛选"] .btn').on('click', function() {
                const $btn = $(this);
                const filter = $btn.data('filter');

                // 更新按钮样式
                $('.btn-group[aria-label="知识点筛选"] .btn').removeClass('active');
                $btn.addClass('active');

                const selectedGroup = $('.group-item.active').data('name') || '';

                // 如果选中了分类，则通过API进行筛选
                if (selectedGroup) {
                    loadKnowledgePoints(selectedGroup, filter);
                } else {
                    // 如果没有选中分类，则在当前列表中筛选
                    $('.knowledge-row').each(function() {
                        const type = $(this).data('type') || 'free';
                        $(this).toggle(filter === 'all' || filter === type);
                    });

                    // 更新计数
                    const visibleRows = $('.knowledge-row:visible').length;
                    $('#knowledgeCount').text(visibleRows);

                    // 没有结果时显示提示
                    if (visibleRows === 0) {
                        $('#knowledgeList .no-results').remove();
                        $('#knowledgeList').append(`<tr class="no-results"><td colspan="8" class="text-center py-3">没有${filter === 'paid' ? '付费' : '免费'}知识点</td></tr>`);
                    } else {
                        $('#knowledgeList .no-results').remove();
                    }
                }
            });

            // 知识点搜索功能
            $('#knowledgeSearchInput').on('input', function() {
                const searchTerm = $(this).val().toLowerCase().trim();

                // 如果搜索框为空，重新加载当前选中的知识点分组
                if (!searchTerm) {
                    const groupName = $('.group-item.active').data('name') || '';
                    if (groupName) {
                        loadKnowledgePoints(groupName);
                    }
                    return;
                }

                $('.knowledge-row').each(function() {
                    const knowledgeId = $(this).find('td:nth-child(2)').text().toLowerCase();
                    const knowledgeName = $(this).find('td:nth-child(3)').text().toLowerCase();
                    const groupName = $(this).find('td:nth-child(4)').text().toLowerCase();

                    const matches = knowledgeId.includes(searchTerm) ||
                                  knowledgeName.includes(searchTerm) ||
                                  groupName.includes(searchTerm);

                    $(this).toggle(matches);
                });

                // 更新计数并显示无结果提示
                const visibleRows = $('.knowledge-row:visible').length;
                $('#knowledgeCount').text(visibleRows);

                if (visibleRows === 0) {
                    $('#knowledgeList .no-results').remove();
                    $('#knowledgeList').append(`<tr class="no-results"><td colspan="8" class="text-center py-3">未找到包含"${escapeHtml(searchTerm)}"的知识点</td></tr>`);
                } else {
                    $('#knowledgeList .no-results').remove();
                }
            });


    function escapeHtml(text) {
        if (text === null || typeof text === 'undefined') return '';
        return String(text).replace(/[&<>"']/g, function (match) {
            return {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            }[match];
        });
    }

    function renderKnowledgeList(knowledgePoints, groupName) {
        let html = '';
        if (!knowledgePoints || knowledgePoints.length === 0) {
            html = `<tr><td colspan="8" class="text-center py-3">${escapeHtml(groupName)} 分类下暂无知识点</td></tr>`;
        } else {
            // Set the category name in the header
            $('#selectedGroupName').html(`<i class="fas fa-folder-open mr-2"></i>${escapeHtml(groupName)} <span class="badge badge-info ml-1">${knowledgePoints.length}</span>`);

            knowledgePoints.forEach(function(kp) {
                // Get a badge class based on the topic count
                let badgeClass = 'badge-secondary';
                if (kp.topicCount > 500) badgeClass = 'badge-success';
                else if (kp.topicCount > 300) badgeClass = 'badge-primary';
                else if (kp.topicCount > 100) badgeClass = 'badge-info';
                else if (kp.topicCount == 0) badgeClass = 'badge-danger';

                // Format the free/paid status
                const statusBadge = kp.isFree === 1 ?
                    '<span class="badge badge-success">免费</span>' :
                    '<span class="badge badge-warning">收费</span>';

                html += `
                    <tr class="knowledge-row" data-knowledge-id="${kp.knowledgeId}">
                        <td><input type="checkbox" class="knowledge-checkbox" data-id="${kp.knowledgeId}"></td>
                        <td>${kp.knowledgeId}</td>
                        <td>${escapeHtml(kp.knowledgeName || '未命名知识点')}</td>
                        <td>${statusBadge}</td>
                        <td><span class="badge ${badgeClass}">${kp.topicCount || 0}</span></td>
                        <td>${kp.sort || 0}</td>
                        <td>${kp.createdAt || '-'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary view-knowledge-btn" data-id="${kp.knowledgeId}" title="查看知识点">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-info edit-knowledge-btn" data-id="${kp.knowledgeId}" title="编辑知识点">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger delete-knowledge-btn" data-id="${kp.knowledgeId}" title="删除知识点">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <button class="btn btn-primary generate-paper-btn-single" data-id="${kp.knowledgeId}" data-name="${escapeHtml(kp.knowledgeName)}" title="从此知识点生成试卷">
                                    <i class="fas fa-file-alt mr-1"></i>出题
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }
        $('#knowledgeList').html(html);

        // Enable the select all checkbox functionality
        $('#selectAllCheckbox').on('change', function() {
            $('.knowledge-checkbox').prop('checked', $(this).prop('checked'));
            updateSelectionCounter();
        });

        // Initial count update
        updateSelectionCounter();

        // Add change event to all knowledge checkboxes
        $(document).on('change', '.knowledge-checkbox', function() {
            updateSelectionCounter();
        });

        // Add floating action button for batch generation if not exists
        if ($('#batchGenerateBtn').length === 0) {
            $('body').append(`
                <div id="batchGenerateContainer" class="position-fixed" style="bottom: 30px; right: 30px; z-index: 1030; display: none;">
                    <button id="batchGenerateBtn" class="btn btn-primary btn-lg rounded-circle shadow-lg"
                        style="width: 60px; height: 60px;" data-toggle="tooltip" data-placement="left" title="从选中知识点生成试卷">
                        <i class="fas fa-magic"></i>
                        <span class="badge badge-light badge-pill position-absolute" style="top: -5px; right: -5px;">0</span>
                    </button>
                </div>
            `);

            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
        }
    }

    function renderGroupList(groups) {
        let html = '';
        if (!groups || groups.length === 0) {
            html = `
                <div class="empty-state text-center text-muted py-3">
                    <i class="fas fa-info-circle mr-1"></i>
                    暂无分类。
                </div>
            `;
        } else {
            groups.forEach(function(group) {
                html += `
                    <div class="group-item list-group-item list-group-item-action" style="cursor: pointer;" data-id="${group.id}" data-name="${escapeHtml(group.groupName)}">
                        <i class="fas fa-folder mr-2"></i>
                        <span>${escapeHtml(group.groupName)}</span>
                        <span class="badge badge-secondary float-right">${group.count || 0}</span>
                    </div>
                `;
            });
        }
        $('#groupList').html(html);

        $('#groupList .group-item').on('click', function() {
            $('#groupList .group-item').removeClass('active');
            $(this).addClass('active');
            const groupName = $(this).data('name');
            const groupId = $(this).data('id');
            $('#selectedGroupName').html(`<i class="fas fa-folder-open mr-2"></i>${escapeHtml(groupName)}`);
            // Enable relevant buttons if they exist and are managed elsewhere
            // $('#generatePaperBtn').prop('disabled', false);
            // $('#addKnowledgeBtn').prop('disabled', false);
            loadKnowledgePoints(groupId, groupName);
        });
    }

            // Paper history and knowledge group setup is already initialized at the top of document.ready

            // Function to load user profile
            // function loadUserProfile() { // REMOVE THIS ENTIRE FUNCTION
            //     $.ajax({
            //         url: '/api/users/profile', // Assuming this is your user profile endpoint
            //         method: 'GET',
            //         dataType: 'json',
            //         success: function(response) {
            //             if (response.success && response.data) {
            //                 $('.nav-user .username').text(response.data.nickname || response.data.username || '用户');
            //                 if (response.data.avatarUrl) {
            //                     $('.nav-user .avatar').attr('src', response.data.avatarUrl);
            //                 }
            //             } else {
            //                 $('.nav-user .username').text('用户');
            //                 console.warn('Failed to load user profile or data missing:', response.message);
            //             }
            //         },
            //         error: function(xhr) {
            //             $('.nav-user .username').text('用户');
            //             console.error('Error loading user profile:', xhr.statusText);
            //         }
            //     });
            // }

            // Knowledge Group functions
            function loadKnowledgeGroups() {
                // Enhanced loading message for groups
                $('#groupList').html(`
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-spinner fa-spin mb-2" style="font-size: 1.5rem;"></i>
                        <p class="mb-0">正在加载知识点分类...</p>
                    </div>
                `);

                $.ajax({
                    url: '/api/knowledge/groups',
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.success && Array.isArray(response.data)) {
                            renderGroupList(response.data);
                        } else {
                            const errorMessage = response && response.message ? response.message : '知识点分类数据格式错误或加载失败';
                            $('#groupList').html(`<div class="alert alert-warning">${escapeHtml(errorMessage)}</div>`);
                        }
                    },
                    error: function() { $('#groupList').html('<div class="alert alert-danger">无法连接服务器加载分类</div>');}
                });
            }

            // Paper History functions
            function loadPaperHistory() {
                // 显示加载状态和分页等控件的加载状态
                $('#paperHistoryTable').html(`
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="spinner-border text-primary mr-3" role="status">
                                    <span class="sr-only">加载中...</span>
                                </div>
                                <div>
                                    <h5 class="mb-1">正在加载试卷历史</h5>
                                    <small class="text-muted">请稍候...</small>
                                </div>
                            </div>
                        </td>
                    </tr>
                `);

                // 清空分页并显示加载状态
                $('#paperHistoryPagination').html(`
                    <div class="d-flex justify-content-center mt-2">
                        <div class="spinner-border spinner-border-sm text-secondary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                    </div>
                `);

                // 获取排序和筛选参数，设置默认值
                const sortValue = $('#paperSortSelect').val() || 'time-desc';
                const filterType = $('.btn-group[aria-label="试卷类型筛选"] button.active').data('paper-filter') || 'all';
                const searchTerm = $('#paperSearchInput').val() || '';

                // 更新筛选状态指示器（如果有筛选条件）
                const hasFilters = searchTerm || filterType !== 'all' || sortValue !== 'time-desc';
                const $refreshBtn = $('#refreshPaperHistoryBtn');

                // 如果有筛选条件，添加小微的视觉提示
                if (hasFilters) {
                    if (!$refreshBtn.find('.filter-indicator').length) {
                        $refreshBtn.append(' <small class="filter-indicator badge badge-info">*</small>');
                    }
                } else {
                    $refreshBtn.find('.filter-indicator').remove();
                }
                const page = currentPage || 0;

                // 准备请求参数
                const requestData = {
                    page: page,
                    size: pageSize,
                    sort: sortValue,
                    search: searchTerm
                };

                // 只有当过滤器不是"all"时才添加type参数
                if (filterType !== 'all') {
                    requestData.type = parseInt(filterType);
                }
                // Make AJAX request to get paper history
                $.ajax({
                    url: '/api/papers', // Correct API endpoint based on PaperController.getAllPapers
                    method: 'GET',
                    data: requestData,
                    dataType: 'json',
                    success: function(response) {
                    // 尝试检查API返回的数据结构 - 使用success而不是status
                    if (response && response.success === true && response.data) {
                        // 处理ApiResponse包装的数据，匹配后端PaperController的结构
                        renderPaperHistory(response.data);
                    } else if (response && Array.isArray(response.content)) {
                        console.log('找到response.content数组');
                        // 直接数据模式
                        renderPaperHistory(response);
                    } else {
                        console.warn('未能识别的响应格式:', response);
                        // 检查是否有应用筛选条件
                        if (hasFilters) {
                            $('#paperHistoryTable').html(`
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-filter fa-3x text-muted mb-3"></i>
                                            <p class="lead">没有找到符合条件的试卷</p>
                                            <small class="text-muted">尝试修改搜索条件或清除筛选</small>
                                            <div class="mt-3">
                                                <button class="btn btn-sm btn-outline-secondary" onclick="$('#refreshPaperHistoryBtn').click()">
                                                    <i class="fas fa-times mr-1"></i> 清除筛选
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        } else {
                            $('#paperHistoryTable').html(`
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                            <p class="lead">暂无试卷历史记录</p>
                                            <small class="text-muted">生成的试卷将显示在这里</small>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        }
                        $('#paperHistoryPagination').empty();
                    }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to load paper history:', status, error);
                        $('#paperHistoryTable').html(`
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>
                                        无法加载试卷历史
                                        <div class="mt-2">
                                            <small>${error || '未知错误'}</small>
                                        </div>
                                        <button class="btn btn-sm btn-outline-danger mt-3" onclick="loadPaperHistory()">
                                            <i class="fas fa-sync-alt mr-1"></i> 重试
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `);
                        $('#paperHistoryPagination').empty();
                    }
                });
            }

            // Function to render paper history data with enhanced styling and information
            function renderPaperHistory(data) {
                // 确保能适配不同的数据结构
                const papers = data.content || data.papers || [];
                const totalElements = data.totalElements || data.total || 0;

                // Update history count in header
                $('#historyCount').text(totalElements);

                if (!papers || papers.length === 0) {
                    $('#paperHistoryTable').html(`
                      <tr>
                          <td colspan="7" class="text-center py-5">
                              <div class="empty-state">
                                  <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                  <p class="lead">暂无试卷历史记录</p>
                                  <small class="text-muted">生成的试卷将显示在这里</small>
                              </div>
                          </td>
                      </tr>
                    `);
                    $('#paperHistoryPagination').empty();
                    return;
                }

                let html = '';
                const paperTypeLabels = {
                    '0': '<span class="badge badge-secondary">普通试卷</span>',
                    '1': '<span class="badge badge-primary">教师试卷</span>',
                    '2': '<span class="badge badge-success">标准试卷</span>'
                };

                papers.forEach(function(paper) {
                    // Format difficulty with colored badges
                    let difficultyHtml = '';
                    if (paper.difficultyDistribution) {
                      const easy = paper.difficultyDistribution.easy || 0;
                      const medium = paper.difficultyDistribution.medium || 0;
                      const hard = paper.difficultyDistribution.hard || 0;

                      difficultyHtml = `
                        <span class="badge badge-success" title="简单题目比例">简:${easy}%</span>
                        <span class="badge badge-primary" title="中等题目比例">中:${medium}%</span>
                        <span class="badge badge-danger" title="困难题目比例">难:${hard}%</span>
                      `;
                    } else {
                      difficultyHtml = '<span class="badge badge-secondary">未知难度</span>';
                    }

                    // Format creation date
                    const createdAt = paper.createdAt ? new Date(paper.createdAt).toLocaleString() : '未知时间';

                    // Build paper row with enhanced styling
                    html += `
                        <tr>
                            <td><small class="text-muted">${paper.id || '-'}</small></td>
                            <td><strong class="text-primary">${paper.title || '未命名试卷'}</strong></td>
                            <td>${paperTypeLabels[paper.type] || '<span class="badge badge-secondary">未知</span>'}</td>
                            <td>${paper.totalScore || 0}分</td>
                            <td>${difficultyHtml}</td>
                            <td>${paper.createTime}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary view-paper-btn" data-id="${paper.id}">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                                <button class="btn btn-sm btn-outline-success download-paper-btn" data-id="${paper.id}">
                                    <i class="fas fa-download mr-1"></i>下载
                                </button>
                                <button class="btn btn-sm btn-outline-info clone-paper-btn" data-id="${paper.id}">
                                    <i class="fas fa-copy mr-1"></i>复制
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-paper-btn" data-id="${paper.id}">
                                    <i class="fas fa-trash-alt mr-1"></i>删除
                                </button>
                            </td>
                        </tr>
                    `;
                });

                $('#paperHistoryTable').html(html);

                // Handle pagination
                renderPaperHistoryPagination(data);

                // Attach event handlers for paper actions
                attachPaperHistoryEvents();
            }

            // Function to render pagination for paper history
            function renderPaperHistoryPagination(data) {
                const totalPages = data.totalPages || 1;
                const currentPageNum = data.number || 0;

                if (totalPages <= 1) {
                    $('#paperHistoryPagination').empty();
                    return;
                }

                let paginationHtml = `
                    <ul class="pagination justify-content-center">
                        <li class="page-item ${currentPageNum === 0 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${currentPageNum - 1}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                `;

                // Display page numbers
                for (let i = 0; i < totalPages; i++) {
                    if (totalPages > 7) {
                        // For many pages, use ellipsis
                        if (i === 0 || i === totalPages - 1 || (i >= currentPageNum - 1 && i <= currentPageNum + 1)) {
                            paginationHtml += `
                                <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                                    <a class="page-link" href="#" data-page="${i}">${i + 1}</a>
                                </li>
                            `;
                        } else if (i === currentPageNum - 2 || i === currentPageNum + 2) {
                            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                        }
                    } else {
                        // For fewer pages, show all
                        paginationHtml += `
                            <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i + 1}</a>
                            </li>
                        `;
                    }
                }

                paginationHtml += `
                        <li class="page-item ${currentPageNum === totalPages - 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${currentPageNum + 1}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                `;

                $('#paperHistoryPagination').html(paginationHtml);

                // Add event listener for pagination links
                $('#paperHistoryPagination .page-link').on('click', function(e) {
                    e.preventDefault();
                    if (!$(this).parent().hasClass('disabled')) {
                        currentPage = parseInt($(this).data('page'));
                        loadPaperHistory();
                    }
                });

            }

            // Function to attach event handlers to paper history actions
            function attachPaperHistoryEvents() {
                // View paper - using event delegation with preview modal first
                // 视图按钮仅触发预览模态框，不需要修改，保留现有行为
                $('#paperHistoryTable').on('click', '.view-paper-btn', function() {
                    const paperId = $(this).data('id');
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // Show loading state on button
                    $btn.html('<span class="spinner-border spinner-border-sm mr-1" role="status"></span>加载中...');
                    $btn.prop('disabled', true);

                    // Fetch paper details for preview
                    $.ajax({
                        url: `/api/papers/${paperId}`,
                        method: 'GET',
                        success: function(response) {
                            let paper;
                            if (response && response.data) {
                                paper = response.data; // ApiResponse wrapper
                                console.log('Full paper object from API (paper ID: ' + paperId + '):', JSON.parse(JSON.stringify(paper))); // Cascade Debug Log
                            } else {
                                paper = response; // Direct response
                                console.log('Full paper object from API (paper ID: ' + paperId + ', direct response):', JSON.parse(JSON.stringify(paper))); // Cascade Debug Log
                            }

                            if (!paper) {
                                showError('数据错误', '无法加载试卷详情');
                                return;
                            }

                            // Debug paper object structure
                            console.log('Paper object structure:', Object.keys(paper));
                            console.log('Paper topics:', paper.topics ? paper.topics.length : 'none');
                            console.log('Paper difficulty:', paper.difficulty);
                            console.log('Paper difficultyDistribution:', paper.difficultyDistribution);

                            // Populate modal with paper details
                            $('#paperPreviewModal .modal-title').text(paper.title || '未命名试卷');

                            // Format paper details for preview
                            const paperTypeMap = {
                                '0': '普通试卷',
                                '1': '教师试卷',
                                '2': '标准试卷'
                            };

                            // Format creation date
                            const createdAt = paper.createTime ? new Date(paper.createTime).toLocaleString() : '未知时间';
                            const updatedAt = paper.updatedAt ? new Date(paper.updatedAt).toLocaleString() : '未知时间';

                            // Get topic count from topics array if available
                            let topicCount = '未知';
                            if (paper.topics && Array.isArray(paper.topics)) {
                                topicCount = paper.topics.length;
                            } else if (paper.content) {
                                try {
                                    const content = JSON.parse(paper.content);
                                    topicCount = content.length || 0;
                                } catch (e) {
                                    console.error('解析试卷内容出错:', e);
                                }
                            }

                            console.log('Calculated topicCount for modal preview:', topicCount); // Cascade Debug Log
                            // Build preview HTML
                            let previewHtml = `
                                <div class="paper-preview-container">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5>基本信息</h5>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    试卷编号
                                                    <span class="badge badge-primary badge-pill">${paper.id}</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    试卷类型
                                                    <span class="badge badge-info">${paperTypeMap[paper.type] || '未知'}</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    总分数
                                                    <span class="badge badge-success">${paper.totalScore || 0}分</span>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    题目数量
                                                    <span class="badge badge-secondary">${topicCount} 题</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>时间信息</h5>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    创建时间
                                                    <small>${createdAt}</small>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    更新时间
                                                    <small>${updatedAt}</small>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- 难度分布 -->
                                    <div class="mt-4">
                                        <h5>难度分布</h5>
                                        <div class="difficulty-distribution-chart">
                                            ${ (function() {
                                                let mappedDifficultyDistribution = null;
                                                // Try to get difficulty distribution from different possible structures
                                                if (paper.difficultyDistribution) {
                                                    console.log('Found paper.difficultyDistribution:', paper.difficultyDistribution);

                                                    // 处理数组格式的难度分布 [{ "1": 0.3, "2": 0.5, "3": 0.2 }]
                                                    if (Array.isArray(paper.difficultyDistribution)) {
                                                        if (paper.difficultyDistribution.length > 0) {
                                                            const distributionObj = paper.difficultyDistribution[0];
                                                            mappedDifficultyDistribution = {
                                                                easy: Math.round((distributionObj['1'] || 0) * 100),
                                                                medium: Math.round((distributionObj['2'] || 0) * 100),
                                                                hard: Math.round((distributionObj['3'] || 0) * 100)
                                                            };
                                                            console.log('Mapped difficulty distribution from array:', mappedDifficultyDistribution);
                                                        }
                                                    }
                                                    // 处理对象格式的难度分布 (兼容旧格式) { "1": 0.3, "2": 0.5, "3": 0.2 }
                                                    else if (typeof paper.difficultyDistribution === 'object') {
                                                        mappedDifficultyDistribution = {
                                                            easy: Math.round((paper.difficultyDistribution['1'] || 0) * 100),
                                                            medium: Math.round((paper.difficultyDistribution['2'] || 0) * 100),
                                                            hard: Math.round((paper.difficultyDistribution['3'] || 0) * 100)
                                                        };
                                                        console.log('Mapped difficulty distribution from object:', mappedDifficultyDistribution);
                                                    }
                                                }

                                                // If we still don't have a distribution but have a difficulty value
                                                if (!mappedDifficultyDistribution && paper.difficulty) {
                                                    // If we have an overall difficulty value, create a simple distribution
                                                    console.log('Using paper.difficulty instead:', paper.difficulty);
                                                    const difficulty = parseFloat(paper.difficulty) || 0.5;
                                                    console.log('Parsed difficulty value:', difficulty);

                                                    // Create a more nuanced distribution based on how close the difficulty is to boundaries
                                                    if (difficulty <= 0.4) {
                                                        // Easy range
                                                        const easyPercent = Math.round(100 - ((difficulty / 0.4) * 30)); // 70-100% easy
                                                        const mediumPercent = 100 - easyPercent;
                                                        mappedDifficultyDistribution = {
                                                            easy: easyPercent,
                                                            medium: mediumPercent,
                                                            hard: 0
                                                        };
                                                    } else if (difficulty <= 0.7) {
                                                        // Medium range
                                                        const mediumPercent = 70;
                                                        const remainingPercent = 30;
                                                        const ratio = (difficulty - 0.4) / 0.3; // 0-1 within medium range
                                                        const easyPercent = Math.round(remainingPercent * (1 - ratio));
                                                        const hardPercent = Math.round(remainingPercent * ratio);
                                                        mappedDifficultyDistribution = {
                                                            easy: easyPercent,
                                                            medium: mediumPercent,
                                                            hard: hardPercent
                                                        };
                                                    } else {
                                                        // Hard range
                                                        const hardPercent = Math.round(((difficulty - 0.7) / 0.3) * 30 + 70); // 70-100% hard
                                                        const mediumPercent = 100 - hardPercent;
                                                        mappedDifficultyDistribution = {
                                                            easy: 0,
                                                            medium: mediumPercent,
                                                            hard: hardPercent
                                                        };
                                                    }
                                                    console.log('Created distribution from difficulty:', mappedDifficultyDistribution);
                                                } else if (paper.topics && Array.isArray(paper.topics) && paper.topics.length > 0) {
                                                    // If we have topics with difficulty values, calculate distribution
                                                    console.log('Calculating from topics array');
                                                    let easyCount = 0, mediumCount = 0, hardCount = 0;
                                                    paper.topics.forEach(topic => {
                                                        const difficulty = parseFloat(topic.difficulty) || 0.5;
                                                        if (difficulty <= 0.4) easyCount++;
                                                        else if (difficulty <= 0.7) mediumCount++;
                                                        else hardCount++;
                                                    });
                                                    const total = easyCount + mediumCount + hardCount;
                                                    if (total > 0) {
                                                        mappedDifficultyDistribution = {
                                                            easy: Math.round((easyCount / total) * 100),
                                                            medium: Math.round((mediumCount / total) * 100),
                                                            hard: Math.round((hardCount / total) * 100)
                                                        };
                                                    }
                                                }

                                                // Fallback to a default distribution if we couldn't determine one
                                                if (!mappedDifficultyDistribution) {
                                                    console.log('Using default difficulty distribution');
                                                    mappedDifficultyDistribution = { easy: 30, medium: 50, hard: 20 };
                                                }

                                                console.log('Final mapped difficulty distribution:', mappedDifficultyDistribution);

                                                // Group topics by difficulty
                                                const easyTopics = [];
                                                const mediumTopics = [];
                                                const hardTopics = [];

                                                // Try to get topics from different possible structures
                                                let topicsArray = null;

                                                if (paper.topics && Array.isArray(paper.topics)) {
                                                    console.log('Using paper.topics array with length:', paper.topics.length);
                                                    topicsArray = paper.topics;
                                                } else if (paper.content) {
                                                    // Try to parse content if it's a JSON string
                                                    try {
                                                        console.log('Trying to parse paper.content');
                                                        const content = typeof paper.content === 'string' ? JSON.parse(paper.content) : paper.content;
                                                        if (Array.isArray(content)) {
                                                            console.log('Successfully parsed paper.content into array with length:', content.length);
                                                            topicsArray = content;
                                                        }
                                                    } catch (e) {
                                                        console.error('Error parsing paper.content:', e);
                                                    }
                                                }

                                                // Process topics if we found any
                                                if (topicsArray && topicsArray.length > 0) {
                                                    console.log('Processing topics array:', topicsArray.length);
                                                    // Log a sample topic to see its structure
                                                    if (topicsArray[0]) {
                                                        console.log('Sample topic structure:', topicsArray[0]);
                                                    }

                                                    topicsArray.forEach((topic, index) => {
                                                        // Ensure topic is an object
                                                        if (typeof topic !== 'object') return;

                                                        // Get difficulty, defaulting to the paper's overall difficulty if available
                                                        let difficulty;
                                                        if (topic.difficulty !== undefined) {
                                                            difficulty = parseFloat(topic.difficulty);
                                                        } else if (paper.difficulty !== undefined) {
                                                            difficulty = parseFloat(paper.difficulty);
                                                        } else {
                                                            difficulty = 0.5; // Default to medium
                                                        }

                                                        // Create topic object with all possible properties
                                                        const topicObj = {
                                                            id: topic.id || topic.topicId || index,
                                                            index: index + 1,
                                                            type: topic.type || topic.questionType || topic.topicType || 'unknown'
                                                        };

                                                        // Group by difficulty
                                                        if (difficulty <= 0.4) {
                                                            easyTopics.push(topicObj);
                                                        } else if (difficulty <= 0.7) {
                                                            mediumTopics.push(topicObj);
                                                        } else {
                                                            hardTopics.push(topicObj);
                                                        }
                                                    });
                                                    console.log('Grouped topics - Easy:', easyTopics.length, 'Medium:', mediumTopics.length, 'Hard:', hardTopics.length);
                                                } else {
                                                    console.log('No topics array found or it was empty');
                                                }

                                                return formatDifficultyChart(mappedDifficultyDistribution, easyTopics, mediumTopics, hardTopics);
                                            })() }
                                        </div>
                                    </div>
                                </div>
                            `;

                            $('#paperPreviewModalBody').html(previewHtml);

                            // Set action buttons with paper ID
                            $('#paperPreviewDownloadBtn').data('paper-id', paperId);
                            $('#paperPreviewViewBtn').data('paper-id', paperId);

                            // Show the modal
                            $('#paperPreviewModal').modal('show');
                        },
                        error: function() {
                            showError('加载失败', '无法连接服务器');
                        },
                        complete: function() {
                            // Reset button state
                            $btn.html(originalText);
                            $btn.prop('disabled', false);
                        }
                    });
                });

                // Handle paper view action from preview modal
                $('#paperPreviewViewBtn').on('click', function() {
                    const paperId = $(this).data('paper-id');
                    window.open(`/papers/preview/${paperId}`, '_blank'); // 修正为controller中存在的preview路径
                });

                // Handle paper download action from preview modal
                $('#paperPreviewDownloadBtn').on('click', function() {
                    const paperId = $(this).data('paper-id');
                    window.location.href = `/api/papers/download/${paperId}`; // 修正为controller中存在的下载路径
                });

                // Download paper - using event delegation
                $('#paperHistoryTable').on('click', '.download-paper-btn', function() {
                    const paperId = $(this).data('id');
                    window.location.href = `/api/papers/download/${paperId}`; // 修正为controller中存在的下载路径
                });

                // Clone paper (copy settings to new paper) - using event delegation
                $('#paperHistoryTable').on('click', '.clone-paper-btn', function() {
                    const paperId = $(this).data('id');
                    $.ajax({
                        url: `/api/papers/${paperId}`,
                        method: 'GET',
                        dataType: 'json',
                        success: function(response) {
                            if (response) {
                                populatePaperForm(response);
                                $('#paperGenerationModal').modal('show');
                            } else {
                                showError('操作失败', '无法获取试卷信息');
                            }
                        },
                        error: function() {
                            showError('操作失败', '无法连接服务器');
                        }
                    });
                });

                // Delete paper - using event delegation with improved modal confirmation
                $('#paperHistoryTable').on('click', '.delete-paper-btn', function() {
                    const paperId = $(this).data('id');
                    const paperTitle = $(this).closest('tr').find('td:nth-child(2)').text().trim();

                    // Set paper ID and title in the confirmation modal
                    $('#confirmDeleteModal .paper-title').text(paperTitle);
                    $('#confirmDeleteBtn').data('paper-id', paperId);

                    // Show the confirmation modal
                    $('#confirmDeleteModal').modal('show');
                });

                // Handle delete confirmation from modal
                $('#confirmDeleteBtn').on('click', function() {
                    const paperId = $(this).data('paper-id');
                    // Show loading state
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // Fetch paper details for preview
                    $.ajax({
                        url: `/api/papers/${paperId}`,
                        method: 'DELETE',
                        success: function(response) {
                            $('#confirmDeleteModal').modal('hide');
                            showSuccess('删除成功', '试卷已成功删除');
                            loadPaperHistory(); // Reload history
                        },
                        error: function(xhr) {
                            let errorMsg = '删除试卷失败，请重试';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMsg = xhr.responseJSON.message;
                            }
                            showError('操作失败', errorMsg);
                        },
                        complete: function() {
                            // Reset button state
                            $btn.html(originalText);
                            $btn.prop('disabled', false);
                        }
                    });
                });

                // Export PDF - using event delegation
                $('#paperHistoryTable').on('click', '.export-pdf-btn', function() {
                    const paperId = $(this).data('id');
                    window.location.href = `/papers/export/pdf/${paperId}`;
                });
            }

            // Function to populate paper form with existing paper data
            function populatePaperForm(paper) {
                // Reset form first to avoid old data
                $('#paperGenerationForm')[0].reset();

                // Set basic paper info with more descriptive copy title
                $('#paperTitle').val(paper.title + ' (复制 ' + new Date().toLocaleDateString() + ')');
                $('#paperType').val(paper.type || '0');
                $('#paperFormat').val(paper.format || 'pdf');

                // Set difficulty distribution
                if (paper.difficultyDistribution) {
                    $('#easyPercentage').val(paper.difficultyDistribution.easy || 30);
                    $('#mediumPercentage').val(paper.difficultyDistribution.medium || 50);
                    $('#hardPercentage').val(paper.difficultyDistribution.hard || 20);
                }

                // Set question type counts and scores
                if (paper.questionTypeSettings) {
                    const settings = paper.questionTypeSettings;
                    if (settings.singleChoice) {
                        $('#singleChoiceCount').val(settings.singleChoice.count || 10);
                        $('#singleChoiceScore').val(settings.singleChoice.score || 3);
                    }
                    if (settings.multipleChoice) {
                        $('#multipleChoiceCount').val(settings.multipleChoice.count || 5);
                        $('#multipleChoiceScore').val(settings.multipleChoice.score || 4);
                    }
                    if (settings.judgment) {
                        $('#judgmentCount').val(settings.judgment.count || 10);
                        $('#judgmentScore').val(settings.judgment.score || 2);
                    }
                    if (settings.fill) {
                        $('#fillCount').val(settings.fill.count || 0);
                        $('#fillScore').val(settings.fill.score || 3);
                    }
                    if (settings.shortAnswer) {
                        $('#shortAnswerCount').val(settings.shortAnswer.count || 3);
                        $('#shortAnswerScore').val(settings.shortAnswer.score || 10);
                    }
                    if (settings.subjective) {
                        $('#subjectiveCount').val(settings.subjective.count || 0);
                        $('#subjectiveScore').val(settings.subjective.score || 10);
                    }
                    if (settings.group) {
                        $('#groupCount').val(settings.group.count || 0);
                        $('#groupScore').val(settings.group.score || 10);
                    }
                }

                // If the paper has knowledge configs, use them
                if (paper.knowledgeConfigs && Array.isArray(paper.knowledgeConfigs)) {
                    $('#paperGenerationModal').data('knowledgeConfigs', paper.knowledgeConfigs);
                }

                // Update UI based on loaded data
                updateDifficultyChart();
                updatePaperPreview();
            }

            // Function to load knowledge points for a specific group
            function loadKnowledgePoints(groupId, groupName) { // Signature changed
                // Show loading indicator
                $('#knowledgeList').html(`
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-spinner fa-spin mb-2" style="font-size: 1.5rem;"></i>
                        <p class="mb-0">正在加载"${escapeHtml(groupName)}"知识点...</p>
                    </div>
                `);

                // Make AJAX request to get knowledge points for the selected group
                $.ajax({
                    url: '/api/knowledge/points', // Corrected URL
                    method: 'GET',
                    data: {
                        groupId: groupId // Corrected parameter
                    },
                    dataType: 'json',
                    success: function(response) { // Success callback logic changed
                        if (response && response.success && Array.isArray(response.data)) {
                            renderKnowledgeList(response.data, groupName);
                        } else {
                            const errorMessage = response && response.message ? response.message : `无法加载"${escapeHtml(groupName)}"的知识点列表`;
                            $('#knowledgeList').html(`<div class="alert alert-warning">${escapeHtml(errorMessage)}</div>`);
                        }
                    },
                    error: function() {
                        $('#knowledgeList').html(`<div class="alert alert-danger">加载"${escapeHtml(groupName)}"知识点时发生错误</div>`);
                    }
                });
            }

            // Helper function to handle null values in API responses
            function safeJsonParse(jsonStr) {
                if (!jsonStr) return null;
                try {
                    return JSON.parse(jsonStr);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    return null;
                }
            }

            // 格式化难度分布图表
            function formatDifficultyChart(difficultyDistribution, easyTopics, mediumTopics, hardTopics) {
                if (!difficultyDistribution) {
                    return '<div class="alert alert-secondary">暂无难度分布数据</div>';
                }

                const easy = difficultyDistribution.easy || 0;
                const medium = difficultyDistribution.medium || 0;
                const hard = difficultyDistribution.hard || 0;

                // 获取题目详情
                let topicDetails = '';
                if (easyTopics || mediumTopics || hardTopics) {
                    // 创建题目详情表格
                    topicDetails = `
                        <div class="card mt-3">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0">题目难度分布详情</h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered mb-0">
                                        <thead>
                                            <tr>
                                                <th class="bg-success text-white" style="width: 33%">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>简单题</span>
                                                        <span class="badge badge-light">${easyTopics ? easyTopics.length : 0}题</span>
                                                    </div>
                                                </th>
                                                <th class="bg-primary text-white" style="width: 33%">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>中等题</span>
                                                        <span class="badge badge-light">${mediumTopics ? mediumTopics.length : 0}题</span>
                                                    </div>
                                                </th>
                                                <th class="bg-danger text-white" style="width: 33%">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>困难题</span>
                                                        <span class="badge badge-light">${hardTopics ? hardTopics.length : 0}题</span>
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="p-2" style="vertical-align: top">
                                                    ${easyTopics && easyTopics.length > 0 ? easyTopics.map(t => {
                                                        const typeLabel = getTopicTypeLabel(t.type);
                                                        return `<div class="badge badge-light mr-1 mb-1 p-2" style="display: inline-block">
                                                            <span class="badge badge-success mr-1">${t.index}</span>
                                                            ${typeLabel ? `<small class="text-muted">${typeLabel}</small>` : ''}
                                                        </div>`;
                                                    }).join('') : '<div class="text-muted text-center py-2">无简单题目</div>'}
                                                </td>
                                                <td class="p-2" style="vertical-align: top">
                                                    ${mediumTopics && mediumTopics.length > 0 ? mediumTopics.map(t => {
                                                        const typeLabel = getTopicTypeLabel(t.type);
                                                        return `<div class="badge badge-light mr-1 mb-1 p-2" style="display: inline-block">
                                                            <span class="badge badge-primary mr-1">${t.index}</span>
                                                            ${typeLabel ? `<small class="text-muted">${typeLabel}</small>` : ''}
                                                        </div>`;
                                                    }).join('') : '<div class="text-muted text-center py-2">无中等题目</div>'}
                                                </td>
                                                <td class="p-2" style="vertical-align: top">
                                                    ${hardTopics && hardTopics.length > 0 ? hardTopics.map(t => {
                                                        const typeLabel = getTopicTypeLabel(t.type);
                                                        return `<div class="badge badge-light mr-1 mb-1 p-2" style="display: inline-block">
                                                            <span class="badge badge-danger mr-1">${t.index}</span>
                                                            ${typeLabel ? `<small class="text-muted">${typeLabel}</small>` : ''}
                                                        </div>`;
                                                    }).join('') : '<div class="text-muted text-center py-2">无困难题目</div>'}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // Create a horizontal stacked bar chart
                return `
                    <div class="difficulty-chart">
                        <div class="progress" style="height: 30px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                style="width: ${easy}%" aria-valuenow="${easy}" aria-valuemin="0" aria-valuemax="100"
                                data-toggle="tooltip" title="简单: ${easy}%">
                                简单 ${easy}%
                            </div>
                            <div class="progress-bar bg-primary" role="progressbar"
                                style="width: ${medium}%" aria-valuenow="${medium}" aria-valuemin="0" aria-valuemax="100"
                                data-toggle="tooltip" title="中等: ${medium}%">
                                中等 ${medium}%
                            </div>
                            <div class="progress-bar bg-danger" role="progressbar"
                                style="width: ${hard}%" aria-valuenow="${hard}" aria-valuemin="0" aria-valuemax="100"
                                data-toggle="tooltip" title="困难: ${hard}%">
                                困难 ${hard}%
                            </div>
                        </div>
                        <div class="difficulty-legend mt-2 d-flex justify-content-between">
                            <span><i class="fas fa-circle text-success mr-1"></i> 简单 ${easy}%</span>
                            <span><i class="fas fa-circle text-primary mr-1"></i> 中等 ${medium}%</span>
                            <span><i class="fas fa-circle text-danger mr-1"></i> 困难 ${hard}%</span>
                        </div>
                        ${topicDetails}
                    </div>
                `;
            }

            // 获取题目类型标签
            function getTopicTypeLabel(type) {
                const typeMap = {
                    'choice': '单选题',
                    'singleChoice': '单选题',
                    'multiple': '多选题',
                    'multipleChoice': '多选题',
                    'judge': '判断题',
                    'judgment': '判断题',
                    'fill': '填空题',
                    'shortAnswer': '简答题',
                    'subjective': '主观题'
                };
                return typeMap[type] || '';
            }

            // Form validation for Paper Generation
            function checkPaperFormValidity() {
                let errors = [];
                let formValid = true;

                // 检查试卷标题
                const title = $('#paperTitle').val().trim();
                if (!title) {
                    errors.push('请输入试卷标题');
                    $('#paperTitle').addClass('is-invalid');
                    formValid = false;
                } else if (title.length > 50) {
                    errors.push('试卷标题不能超过50个字符');
                    $('#paperTitle').addClass('is-invalid');
                    formValid = false;
                } else {
                    $('#paperTitle').removeClass('is-invalid').addClass('is-valid');
                }

                // 检查知识点配置
                const knowledgeConfigs = $('#paperGenerationForm').data('knowledgeConfigs') || window.currentKnowledgeConfigs || [];
                if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
                    errors.push('请至少选择一个知识点');
                    formValid = false;
                }

                // 检查至少有一种题型
                const types = ['singleChoice', 'multipleChoice', 'judgment', 'fill', 'shortAnswer', 'subjective', 'group'];
                let hasQuestions = false;
                let typeScoreErrors = [];

                for (const type of types) {
                    const count = parseInt($('#' + type + 'Count').val()) || 0;
                    const score = parseInt($('#' + type + 'Score').val()) || 0;

                    // 验证有数量的题型必须有分值
                    if (count > 0) {
                        hasQuestions = true;
                        if (score <= 0) {
                            typeScoreErrors.push(getTypeDisplayName(type));
                            $(`#${type}Score`).addClass('is-invalid');
                            formValid = false;
                        } else {
                            $(`#${type}Score`).removeClass('is-invalid').addClass('is-valid');
                        }
                    }
                }

                if (!hasQuestions) {
                    errors.push('请至少为一种题型设置大于0的出题量');
                    $('.question-type-input').addClass('is-invalid');
                    formValid = false;
                }

                if (typeScoreErrors.length > 0) {
                    errors.push(`以下题型的分值必须大于0：${typeScoreErrors.join('、')}`);
                }

                // 检查难度分布总和是否为100%
                const easyPercent = parseInt($('#easyPercentage').val()) || 0;
                const mediumPercent = parseInt($('#mediumPercentage').val()) || 0;
                const hardPercent = parseInt($('#hardPercentage').val()) || 0;
                const totalPercent = easyPercent + mediumPercent + hardPercent;

                if (totalPercent !== 100) {
                    errors.push(`难度分布总和需为100%，当前为${totalPercent}%`);
                    $('.difficulty-slider').addClass('is-invalid');
                    formValid = false;
                } else {
                    $('.difficulty-slider').removeClass('is-invalid');
                }

                // 显示错误信息
                if (!formValid) {
                    // 确保错误容器存在
                    let errorContainer = $('#formValidationErrors');
                    if (errorContainer.length === 0) {
                        $('#paperGenerationForm').prepend(`
                            <div id="formValidationErrors" class="alert alert-danger mb-3">
                                <h6 class="mb-2"><i class="fas fa-exclamation-triangle mr-2"></i>请修正以下错误：</h6>
                                <ul class="mb-0 pl-3"></ul>
                            </div>
                        `);
                        errorContainer = $('#formValidationErrors');
                    } else {
                        errorContainer.show();
                    }

                    // 清除并添加错误消息
                    const errorList = errorContainer.find('ul');
                    errorList.empty();
                    errors.forEach(error => {
                        errorList.append(`<li>${error}</li>`);
                    });

                    // 确保错误消息可见
                    errorContainer[0].scrollIntoView({behavior: 'smooth'});

                    // 延迟展示错误提示，增强用户体验
                    setTimeout(() => {
                        // 添加抖动效果吸引注意
                        errorContainer.addClass('animate__animated animate__headShake');
                        setTimeout(() => {
                            errorContainer.removeClass('animate__animated animate__headShake');
                        }, 1000);
                    }, 100);
                } else {
                    // 隐藏错误容器
                    $('#formValidationErrors').hide();
                }

                return formValid;
            }

            // 获取题型的显示名称
            function getTypeDisplayName(type) {
                const typeNames = {
                    'singleChoice': '单选题',
                    'multipleChoice': '多选题',
                    'judgment': '判断题',
                    'fill': '填空题',
                    'shortAnswer': '简答题',
                    'subjective': '主观题',
                    'group': '复合题'
                };
                return typeNames[type] || type;
            }

            // Title validation function
            function validateTitle(title) {
                if (!title) {
                    showError('验证失败', '请输入试卷标题。');
                    console.warn("Validation Failed: Title is missing.");
                    return false;
                }
                return true;
            }

            // Paper template search handler
            $('#templateSearchInput').on('input', function() {
                showPaperTemplates(
                    $('.btn-group[aria-label="试卷类型筛选"] button.active').data('paper-filter') || 'all',
                    $(this).val(),
                    0
                );
            });
            // 切换到预览选项卡时更新试卷预览
            $('#paperConfigTabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                if($(e.target).attr('href') === '#preview') {
                    updatePaperPreview();
                }
            });
            $('#knowledgeSearchInput, #groupSearchInput').on('input', function() { /* ... (search logic) ... */ });
            $('.btn-group[aria-label="知识点筛选"] button').on('click', function() { /* ... (filter logic) ... */ });
            // Main generate paper button (for group or general)
            $('#generatePaperBtn').on('click', function() {
                const activeGroupItem = $('.group-item.active');

                // Check if a knowledge point group is selected
                if (!activeGroupItem.length) {
                    showError('操作无效', '请先选择一个知识点分类。');
                    return;
                }
                const groupName = activeGroupItem.data('name');

                let collectedKnowledgeConfigs = []; // Renamed to avoid conflict if any global exists
                $('#knowledgeList .knowledge-row:visible').each(function() {
                    const row = $(this);
                    // Ensure knowledgeId is parsed as a number (Long on backend)
                    const knowledgeId = parseInt(row.data('knowledge-id'));
                    if (isNaN(knowledgeId)) {
                        console.warn("Skipping row, invalid or missing data-knowledge-id:", row.data('knowledge-id'));
                        return; // Skip this row if knowledgeId is not a valid number
                    }
                    const questionCount = parseInt(row.find('.knowledge-item-count').val()) || 0;
                    // 简答题选项已移除，默认为包含简答题
                    const includeShortAnswer = true;

                    if (questionCount > 0) {
                        collectedKnowledgeConfigs.push({
                            knowledgeId: knowledgeId, // Will be treated as Long by backend
                            questionCount: questionCount,
                            includeShortAnswer: includeShortAnswer
                        });
                    }
                });

                if (collectedKnowledgeConfigs.length === 0) {
                    showError('没有可选题', '请为分类中的至少一个知识点设置大于0的出题量。');
                    return;
                }

                const defaultPaperTitle = `${groupName || '自定义'} 组卷 (${new Date().toLocaleDateString()})`;

                console.log("Group KPoint Configs collected by #generatePaperBtn:", collectedKnowledgeConfigs, "Paper Title:", defaultPaperTitle);
                // Call showPaperTemplates with explicit arguments
                showPaperTemplates(collectedKnowledgeConfigs, defaultPaperTitle);
            });

            // Re-attach event handlers for new/modified elements if needed
            // (generate-paper-btn-single is dynamically added, so its handler should be delegated or re-attached if renderKnowledgeList is recalled)
            // For this refactor, ensure it's correctly handled. If it's delegated like $('body').on('click', '.generate-paper-btn-single', ...) it's fine.
            // Assuming it's delegated or renderKnowledgeList re-binds.

            // Modify single knowledge point generation button handler
            // 单个知识点出题按钮处理 - 使用全局函数
            $(document).on('click', '.generate-paper-btn-single', function(e) {
                e.preventDefault(); // 防止默认行为

                const $button = $(this);
                const knowledgeId = parseInt($button.data('id'));
                const knowledgeName = $button.data('name');

                console.log('出题按钮被点击:', knowledgeId, knowledgeName);

                if (isNaN(knowledgeId) || !knowledgeName) {
                    showError('错误', '无法获取知识点信息，请刷新页面后重试。');
                    return;
                }

                // 创建知识点配置
                const singleKnowledgeConfig = [{
                    knowledgeId: knowledgeId,
                    questionCount: 5, // 添加默认题目数量
                    includeShortAnswer: true
                }];

                // 创建默认标题
                const paperTitle = knowledgeName + " 专项练习";

                // 打印信息便于调试
                console.log('Single KPoint Config collected by .generate-paper-btn-single:', singleKnowledgeConfig, 'Paper Title:', paperTitle);

                // 使用全局函数显示模态框(定义在document.ready之外)
                window.openPaperGenerationModal(singleKnowledgeConfig, paperTitle);

                // 视觉反馈
                $button.addClass('btn-success').removeClass('btn-primary');
                setTimeout(function() {
                    $button.addClass('btn-primary').removeClass('btn-success');
                }, 1000);
            });

            // Function to submit paper generation form
        /**
         * 提交试卷生成表单 - 增强版
         * 包含了改进的表单验证、错误处理和超时检测
         */
        function submitPaperGenerationForm() {
            console.log('submitPaperGenerationForm called');

            // 验证表单
            if (!checkPaperFormValidity()) {
                return;
            }

            // 验证题型配置
            if (!validateQuestionCounts()) {
                return;
            }

            // 显示加载提示
            const loadingModal = $('#loadingModal');
            loadingModal.find('.modal-title').text('正在生成试卷...');
            loadingModal.find('.progress-bar').css('width', '0%');
            loadingModal.modal('show');

            // 获取并验证知识点配置 - 优先从全局变量获取
            let configs = window.currentKnowledgeConfigs;

            // 输出调试信息
            console.log('开始生成试卷，知识点配置来源:', configs);

            // 防止没有配置情况
            if (!configs || !Array.isArray(configs) || configs.length === 0) {
                console.error('无法获取知识点配置，尝试更多来源...');

                // 尝试其他来源
                configs = $('#generatePaperForm').data('knowledgeConfigs');
                if (!configs || !Array.isArray(configs) || configs.length === 0) {
                    // 尝试模态框数据
                    configs = $('#paperGenerationModal').data('knowledgeConfigs');
                }

                // 如果还是没有，显示错误并返回
                if (!configs || !Array.isArray(configs) || configs.length === 0) {
                    showError('配置错误', '未找到知识点配置，请先选择知识点');
                    loadingModal.modal('hide');
                    return;
                }
            }

            console.log('Knowledge configs for submission:', configs);

            // CRITICAL: For single knowledge point generation (regardless of title), ensure questionCount is null
            // This ensures single knowledge point papers use global type counts rather than specific counts
            if (configs.length === 1) {
                // Check if this config came from a single knowledge point button
                const isSingleKnowledge = configs[0] && (configs[0].source === 'single' ||
                                         configs[0].fromSingleButton ||
                                         $('#generatePaperForm').data('singleKnowledge'));

                if (configs[0]) { // Ensure the first element exists
                    configs[0].questionCount = null;
                    console.log("单知识点生成试卷: Setting questionCount to null for knowledge point ID:", configs[0].knowledgeId);
                }
            }

            if (!configs || configs.length === 0) {
                showError('配置错误', '未找到知识点配置');
                loadingModal.modal('hide');
                return;
            }

            // 收集难度分布
            const difficultyDistribution = {
                easy: parseInt($('#easyPercentage').val()) || 30,
                medium: parseInt($('#mediumPercentage').val()) || 50,
                hard: parseInt($('#hardPercentage').val()) || 20
            };

            // 收集题型配置
            const questionTypeConfig = {
                singleChoice: {
                    count: parseInt($('#singleChoiceCount').val()) || 5,
                    score: parseInt($('#singleChoiceScore').val()) || 3
                },
                multipleChoice: {
                    count: parseInt($('#multipleChoiceCount').val()) || 3,
                    score: parseInt($('#multipleChoiceScore').val()) || 3
                },
                judgment: {
                    count: parseInt($('#judgmentCount').val()) || 5,
                    score: parseInt($('#judgmentScore').val()) || 2
                },
                fill: {
                    count: parseInt($('#fillCount').val()) || 3,
                    score: parseInt($('#fillScore').val()) || 3
                },
                shortAnswer: {
                    count: parseInt($('#shortAnswerCount').val()) || 2,
                    score: parseInt($('#shortAnswerScore').val()) || 5
                }
            };

            // Calculate totalScore and create properly formatted topicTypeCounts and typeScoreMap
            let calculatedTotalScore = 0;
            const typeScoreMap = {};
            const topicTypeCounts = {}; // This should be a map of question type -> count (integer)

            if (questionTypeConfig.singleChoice && questionTypeConfig.singleChoice.count > 0 && questionTypeConfig.singleChoice.score > 0) {
                calculatedTotalScore += (questionTypeConfig.singleChoice.count * questionTypeConfig.singleChoice.score);
                typeScoreMap['SINGLE_CHOICE'] = questionTypeConfig.singleChoice.score;
                topicTypeCounts['SINGLE_CHOICE'] = questionTypeConfig.singleChoice.count;
            }
            if (questionTypeConfig.multipleChoice && questionTypeConfig.multipleChoice.count > 0 && questionTypeConfig.multipleChoice.score > 0) {
                calculatedTotalScore += (questionTypeConfig.multipleChoice.count * questionTypeConfig.multipleChoice.score);
                typeScoreMap['MULTIPLE_CHOICE'] = questionTypeConfig.multipleChoice.score;
                topicTypeCounts['MULTIPLE_CHOICE'] = questionTypeConfig.multipleChoice.count;
            }
            if (questionTypeConfig.judgment && questionTypeConfig.judgment.count > 0 && questionTypeConfig.judgment.score > 0) {
                calculatedTotalScore += (questionTypeConfig.judgment.count * questionTypeConfig.judgment.score);
                typeScoreMap['JUDGMENT'] = questionTypeConfig.judgment.score;
                topicTypeCounts['JUDGMENT'] = questionTypeConfig.judgment.count;
            }
            if (questionTypeConfig.fill && questionTypeConfig.fill.count > 0 && questionTypeConfig.fill.score > 0) {
                calculatedTotalScore += (questionTypeConfig.fill.count * questionTypeConfig.fill.score);
                typeScoreMap['FILL_IN_BLANKS'] = questionTypeConfig.fill.score;
                topicTypeCounts['FILL_IN_BLANKS'] = questionTypeConfig.fill.count;
            }
            if (questionTypeConfig.shortAnswer && questionTypeConfig.shortAnswer.count > 0 && questionTypeConfig.shortAnswer.score > 0) {
                calculatedTotalScore += (questionTypeConfig.shortAnswer.count * questionTypeConfig.shortAnswer.score);
                typeScoreMap['SHORT_ANSWER'] = questionTypeConfig.shortAnswer.score;
                topicTypeCounts['SHORT_ANSWER'] = questionTypeConfig.shortAnswer.count;
            }

            // 构建请求数据 - 与后端 DTO 字段结构完全匹配
            const formData = {
                title: $('#paperTitle').val().trim(),
                type: parseInt($('#paperType').val()) || 1,
                totalScore: calculatedTotalScore,
                difficultyCriteria: difficultyDistribution,  // 难度标准
                topicTypeCounts: topicTypeCounts,           // 题型数量配置（整数）
                typeScoreMap: typeScoreMap,                 // 题型-分值映射
                knowledgePointConfigs: configs              // 知识点配置
            };

            // 记录请求内容用于调试
            console.log('Submitting paper generation request payload:', JSON.stringify(formData, null, 2));

            // 计算总题数，用于回汇用户
            const totalQuestions = (
                (formData.topicTypeCounts['SINGLE_CHOICE'] || 0) +
                (formData.topicTypeCounts['MULTIPLE_CHOICE'] || 0) +
                (formData.topicTypeCounts['JUDGMENT'] || 0) +
                (formData.topicTypeCounts['FILL_IN_BLANKS'] || 0) +
                (formData.topicTypeCounts['SHORT_ANSWER'] || 0)
            );

            // 显示进度条
            loadingModal.find('.progress-bar').css('width', '30%');

            // 发送AJAX请求
            $.ajax({
                url: '/api/papers/generate',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                timeout: 60000, // 60秒超时，适用于生成大型试卷
                success: function(response) {
                    // Mark this request as completed
                    window.currentPaperGenerationRequest = null;

                    loadingModal.find('.progress-bar').css('width', '100%');

                    // Reset submit button state
                    $('#submitPaperGenerationBtn').prop('disabled', false)
                                                .html('<i class="fas fa-cogs mr-1"></i>生成试卷');

                    setTimeout(function() {
                        loadingModal.modal('hide');

                        if(response && response.data) {
                            // 检查是否有警告信息
                            let warningMsg = '';
                            let warningDetail = '';
                            let actualQuestionCount = 0;

                            // 获取实际生成的题目数量
                            if (response.data.selectedTopics && Array.isArray(response.data.selectedTopics)) {
                                actualQuestionCount = response.data.selectedTopics.length;
                            } else if (response.data.typeCountMap) {
                                actualQuestionCount = Object.values(response.data.typeCountMap)
                                    .reduce((sum, count) => sum + (count || 0), 0);
                            }

                            // 判断是否有警告信息
                            if (response.warningMessage) {
                                warningMsg = response.warningMessage;

                                // 如果警告包含题库容量限制信息，展示更详细的提示
                                if (warningMsg.includes('题库中可用题目不足')) {
                                    warningDetail = '<div class="mt-3 text-left small p-3 bg-light rounded">';
                                    warningDetail += '<p class="mb-2"><i class="fas fa-info-circle mr-1 text-primary"></i> <strong>题库容量限制详情：</strong></p>';
                                    warningDetail += '<p class="mb-1">当前知识点的题库中可用题目数量不足以满足您的全部要求。</p>';

                                    // 尝试解析详细统计信息
                                    const statsMatch = warningMsg.match(/\n题型可用性详细统计：([\s\S]*?)(?=\n|$)/i);
                                    if (statsMatch && statsMatch[1]) {
                                        warningDetail += '<p class="mb-1"><strong>已使用全部可用题目，详细统计：</strong></p>';
                                        warningDetail += '<ul class="mb-0 pl-3">';

                                        const typeStats = statsMatch[1].split('\n').filter(line => line.trim().length > 0);
                                        typeStats.forEach(stat => {
                                            // 清理和格式化每行统计
                                            let cleanStat = stat.replace(/^-\s*/, '');

                                            // 给缺失的题型添加警示色
                                            if (cleanStat.includes('警告') || cleanStat.includes('缺少')) {
                                                cleanStat = `<span class="text-danger">${cleanStat}</span>`;
                                            }

                                            warningDetail += `<li>${cleanStat}</li>`;
                                        });

                                        warningDetail += '</ul>';
                                    }

                                    warningDetail += '</div>';
                                }
                            }

                            // 根据是否有警告信息调整显示
                            if (warningMsg) {
                                // 有警告信息，显示带警告的成功消息
                                Swal.fire({
                                    title: '组卷已完成',
                                    html: `<div class="text-success mb-3">试卷 "${formData.title}" 已生成${actualQuestionCount > 0 ? `，包含${actualQuestionCount}道题目` : ''}</div>
                                          <div class="alert alert-warning mb-0">
                                            <i class="fas fa-exclamation-triangle mr-1"></i> ${warningMsg}
                                            ${warningDetail}
                                          </div>`,
                                    icon: 'warning',
                                    confirmButtonText: '查看试卷',
                                    showCancelButton: true,
                                    cancelButtonText: '继续组卷',
                                    width: '36em'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        window.location.href = `/papers/preview/${response.data.id}`;
                                    } else {
                                        $('#paperGenerationModal').modal('hide');
                                        loadPaperHistory();
                                    }
                                });
                            } else {
                                // 无警告，显示纯成功消息
                                Swal.fire({
                                    title: '组卷成功',
                                    text: `试卷 "${formData.title}" 已生成，共${actualQuestionCount}道题目`,
                                    icon: 'success',
                                    confirmButtonText: '查看试卷',
                                    showCancelButton: true,
                                    cancelButtonText: '继续组卷'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        window.location.href = `/papers/preview/${response.data.id}`;
                                    } else {
                                        $('#paperGenerationModal').modal('hide');
                                        loadPaperHistory();
                                    }
                                });
                            }

                            // 强制刷新试卷历史，以防止未能触发对话框的情况
                            setTimeout(function() {
                                loadPaperHistory();
                            }, 1000);
                        } else {
                            showError('生成失败', response.message || '未知错误');
                            console.error('生成失败:', response);
                        }
                    }, 500);
                },
                error: function(xhr, status, error) {
                    // Mark this request as completed
                    window.currentPaperGenerationRequest = null;

                    loadingModal.modal('hide');

                    // Reset submit button state
                    $('#submitPaperGenerationBtn').prop('disabled', false)
                                                .html('<i class="fas fa-cogs mr-1"></i>生成试卷');

                    // 特殊处理超时错误
                    if (status === 'timeout') {
                        showError('请求超时', '生成试卷耗时过长，请减少知识点或题目数量后重试');
                    } else {
                        showError('服务器错误', `请求失败: ${xhr.status} ${xhr.statusText}. Server response: ${xhr.responseText}`);
                    }
                    console.error('生成试卷错误:', status, error, xhr);
                    console.error('Server Response Text:', xhr.responseText);
                }
            });
        }

        // Handle paper generation modal submit
            $('#submitPaperGenerationBtn').on('click', function() {
                const $button = $(this);

                // Add loading indicator
                $button.prop('disabled', true);
                $button.html('<i class="fas fa-spinner fa-spin mr-1"></i>生成中...');

                // Check for knowledge configs in all possible places
                const formConfigs = $('#generatePaperForm').data('knowledgeConfigs');
                const windowConfigs = window.currentKnowledgeConfigs;

                // Consolidate the configs - use the first available one
                let configsToUse = formConfigs;

                if (!configsToUse || configsToUse.length === 0) {
                    configsToUse = windowConfigs;
                    console.log("Using window.currentKnowledgeConfigs as fallback");
                    // Store on the form for consistency
                    $('#generatePaperForm').data('knowledgeConfigs', configsToUse);
                }

                console.log("Submitting paper generation with configs:", configsToUse);

                // Submit the form
                submitPaperGenerationForm();

                // Track if we're still processing
                const requestStartTime = new Date().getTime();
                window.currentPaperGenerationRequest = requestStartTime;

                // Monitor the request and reset button if it takes too long
                const monitorTimeout = setInterval(function() {
                    // Only reset if this is still the active request
                    if (window.currentPaperGenerationRequest === requestStartTime) {
                        const elapsedTime = new Date().getTime() - requestStartTime;

                        // If more than 30 seconds passed, reset button regardless
                        if (elapsedTime > 30000) {
                            $button.prop('disabled', false);
                            $button.html('<i class="fas fa-cogs mr-1"></i>生成试卷');
                            clearInterval(monitorTimeout);

                            // Alert user that the request might still be processing
                            Swal.fire({
                                title: '请求处理时间较长',
                                text: '服务器仍在处理您的请求，但界面已重置。您可以继续操作，试卷生成完成后会通知您。',
                                icon: 'info',
                                confirmButtonText: '确定'
                            });
                        }
                    } else {
                        // Request has been completed or superseded
                        clearInterval(monitorTimeout);
                    }
                }, 5000); // Check every 5 seconds
            });

            // For paper generation modal preview updates
            const previewUpdateFields = `
                #paperTitle,
                #easyPercentage, #mediumPercentage, #hardPercentage,
                #singleChoiceCount, #singleChoiceScore,
                #multipleChoiceCount, #multipleChoiceScore,
                #judgmentCount, #judgmentScore,
                #fillCount, #fillScore,
                #shortAnswerCount, #shortAnswerScore,
                #subjectiveCount, #subjectiveScore,
                #groupCount, #groupScore
            `;
            $(previewUpdateFields.split(',').map(s => s.trim()).join(', ')).on('input change', function() {
                updatePaperPreview();
            });

            // 增强表单验证 - 阻止负数和非数字输入
            $('input[type="number"]').each(function() {
                // 设置最小值为0
                $(this).attr('min', '0');

                // 防止非数字输入
                $(this).on('input', function() {
                    let val = $(this).val();
                    // 如果输入为空或非数字，设置为0
                    if (val === '' || isNaN(parseInt(val))) {
                        $(this).val('0');
                    } else if (parseInt(val) < 0) {
                        // 如果是负数，设置为0
                        $(this).val('0');
                    }
                });
            });

            // 增强难度百分比验证
            $('#easyPercentage, #mediumPercentage, #hardPercentage').on('input', function() {
                // 限制范围为0-100
                let val = parseInt($(this).val());
                if (isNaN(val)) val = 0;
                if (val < 0) val = 0;
                if (val > 100) val = 100;
                $(this).val(val);

                // 实时更新总和和错误信息
                updateDifficultyTotal();
            });

            // 难度百分比总和计算函数
            function updateDifficultyTotal() {
                const easyPercent = parseInt($('#easyPercentage').val()) || 0;
                const mediumPercent = parseInt($('#mediumPercentage').val()) || 0;
                const hardPercent = parseInt($('#hardPercentage').val()) || 0;
                const totalPercent = easyPercent + mediumPercent + hardPercent;

                // 更新总和显示
                $('#difficultyTotal').text(`${totalPercent}%`);

                // 如果总和不是100%，显示错误
                if (totalPercent !== 100) {
                    $('#difficultyTotal').removeClass('text-success').addClass('text-danger');
                    $('#difficultyError').text(`难度分布总和需为100%，当前为${totalPercent}%`).show();
                } else {
                    $('#difficultyTotal').removeClass('text-danger').addClass('text-success');
                    $('#difficultyError').hide();
                }

                // 更新难度分布图表
                updateDifficultyChart();
            }

            // 统一的AJAX错误处理函数
            function handleAjaxError(xhr, status, error, resourceName) {
                let errorMessage = '无法连接服务器';

                if (xhr.status === 401) {
                    errorMessage = '会话已过期，请重新登录';
                    // 重定向到登录页面
                    setTimeout(() => { window.location.href = '/login'; }, 2000);
                } else if (xhr.status === 403) {
                    errorMessage = '您没有执行此操作的权限';
                } else if (xhr.status === 404) {
                    errorMessage = `未找到${resourceName || '请求的资源'}`;
                } else if (xhr.status === 500) {
                    errorMessage = '服务器内部错误，请稍后再试';
                } else if (status === 'timeout') {
                    errorMessage = '请求超时，请检查网络连接';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                showError('请求失败', errorMessage);
                console.error(`AJAX错误(${resourceName || '未知资源'})`, status, error, xhr);
            }

            // 监听所有题型数量和分值输入框变化
            $('#singleChoiceCount, #singleChoiceScore, #multipleChoiceCount, #multipleChoiceScore, #judgmentCount, #judgmentScore, #fillCount, #fillScore, #shortAnswerCount, #shortAnswerScore, #subjectiveCount, #subjectiveScore, #groupCount, #groupScore').on('input', function() {
                calculateTotalScore();
                updateQuestionDistributionChart();
            });

            // 实现知识点编辑功能
            $(document).on('click', '.edit-knowledge-btn', function() {
                const $button = $(this);
                const knowledgeId = parseInt($button.data('id'));
                const knowledgeName = $button.data('name');
                const groupName = $button.data('group');

                if (isNaN(knowledgeId)) {
                    showError('错误', '无法获取知识点ID');
                    return;
                }

                // 添加加载效果
                $button.html('<i class="fas fa-spinner fa-spin"></i>');

                // 加载知识点详情
                $.ajax({
                    url: `/api/knowledge/${knowledgeId}`,
                    method: 'GET',
                    success: function(response) {
                        // 恢复按钮状态
                        $button.html('<i class="fas fa-edit mr-1"></i>编辑');

                        // 填充编辑表单
                        $('#editKnowledgeId').val(response.id);
                        $('#editKnowledgeName').val(response.name);
                        $('#editKnowledgeGroup').val(response.group);
                        $('#editKnowledgeDescription').val(response.description || '');

                        // 显示模态窗
                        $('#knowledgeEditModal').modal('show');
                    },
                    error: function(xhr, status, error) {
                        // 恢复按钮状态
                        $button.html('<i class="fas fa-edit mr-1"></i>编辑');
                        handleAjaxError(xhr, status, error, '知识点');
                    }
                });
            });

            // 知识点编辑保存功能
            $('#saveKnowledgeBtn').on('click', function() {
                const $button = $(this);
                const knowledgeId = $('#editKnowledgeId').val();
                const knowledgeName = $('#editKnowledgeName').val().trim();
                const knowledgeGroup = $('#editKnowledgeGroup').val().trim();
                const knowledgeDescription = $('#editKnowledgeDescription').val().trim();

                // 表单验证
                if (!knowledgeName) {
                    showError('验证失败', '知识点名称不能为空');
                    return;
                }

                // 添加加载效果
                $button.prop('disabled', true);
                $button.html('<i class="fas fa-spinner fa-spin mr-1"></i>保存中...');

                // 发送AJAX请求保存编辑
                $.ajax({
                    url: `/api/knowledge/${knowledgeId}`,
                    method: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        id: knowledgeId,
                        name: knowledgeName,
                        group: knowledgeGroup,
                        description: knowledgeDescription
                    }),
                    success: function(response) {
                        // 关闭模态窗
                        $('#knowledgeEditModal').modal('hide');

                        // 显示成功消息
                        showSuccess('成功', `已更新知识点 "${knowledgeName}"`);

                        // 重新加载当前选中的分类
                        const currentGroup = $('.group-item.active').data('name') || '';
                        if (currentGroup) {
                            loadKnowledgePoints(currentGroup);
                        }
                    },
                    error: function(xhr) {
                        handleAjaxError(xhr, status, error, '知识点编辑');
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $button.prop('disabled', false);
                        $button.html('<i class="fas fa-save mr-1"></i>保存');
                    }
                });
            });

            // 实现知识点删除功能
            $(document).on('click', '.delete-knowledge-btn', function() {
                const $button = $(this);
                const knowledgeId = parseInt($button.data('id'));
                const knowledgeName = $button.data('name');

                if (isNaN(knowledgeId)) {
                    showError('错误', '无法获取知识点ID');
                    return;
                }

                // 显示确认对话框
                Swal.fire({
                    title: '确认删除?',
                    text: `您确定要删除知识点 "${knowledgeName}" 吗？此操作无法撤销。`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: '是，删除',
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 添加加载效果
                        $button.html('<i class="fas fa-spinner fa-spin"></i>');

                        // 发送AJAX请求删除
                        $.ajax({
                            url: `/api/knowledge/${knowledgeId}`,
                            method: 'DELETE',
                            success: function(response) {
                                showSuccess('删除成功', `已删除知识点 "${knowledgeName}"`);

                                // 重新加载当前选中的分类
                                const currentGroup = $('.group-item.active').data('name') || '';
                                if (currentGroup) {
                                    loadKnowledgePoints(currentGroup);
                                }
                            },
                            error: function(xhr, status, error) {
                                // 恢复按钮状态
                                $button.html('<i class="fas fa-trash-alt mr-1"></i>删除');
                                handleAjaxError(xhr, status, error, '知识点删除');
                            }
                        });
                    }
                });
            });

            // 实现添加知识点分类功能
            $('#addNewGroupBtn').on('click', function() {
                const $button = $(this);
                const $form = $('#addGroupForm');

                // 表单验证
                if (!$form[0].checkValidity()) {
                    $form.addClass('was-validated');
                    return;
                }

                // 显示加载状态
                $button.prop('disabled', true);
                $button.html('<i class="fas fa-spinner fa-spin mr-1"></i>保存中...');

                // 收集表单数据
                const groupName = $('#newGroupName').val().trim();
                const groupDescription = $('#newGroupDescription').val().trim();

                // 发送AJAX请求
                $.ajax({
                    url: '/api/knowledge/groups',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        name: groupName,
                        description: groupDescription
                    }),
                    success: function(response) {
                        // 关闭模态窗
                        $('#addGroupModal').modal('hide');

                        // 显示成功消息
                        showSuccess('成功', `成功创建分类 "${groupName}"`);

                        // 重新加载分类列表
                        loadKnowledgeGroups();
                    },
                    error: function(xhr) {
                        handleAjaxError(xhr, status, error, '知识点分类');
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $button.prop('disabled', false);
                        $button.html('<i class="fas fa-save mr-1"></i>保存');
                    }
                });
            });

            // Function to update the selection counter and show/hide the batch generate button
            function updateSelectionCounter() {
                const selectedCount = $('.knowledge-checkbox:checked').length;

                // Update badge on batch generate button
                $('#batchGenerateBtn .badge').text(selectedCount);

                // Show/hide the batch generate container based on selection
                if (selectedCount > 0) {
                    $('#batchGenerateContainer').fadeIn(200);
                } else {
                    $('#batchGenerateContainer').fadeOut(200);
                }
            }

            // Handle batch generate button click
            $(document).on('click', '#batchGenerateBtn', function() {
                const selectedCheckboxes = $('.knowledge-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    showError('选择错误', '请至少选择一个知识点');
                    return;
                }

                // Collect knowledge point configs
                const knowledgeConfigs = [];
                let knowledgeNames = [];

                selectedCheckboxes.each(function() {
                    const $row = $(this).closest('tr');
                    const knowledgeId = parseInt($(this).val());
                    const knowledgeName = $row.find('td:nth-child(3)').text().trim();

                    knowledgeConfigs.push({
                        knowledgeId: knowledgeId,
                        questionCount: Math.max(2, Math.floor(10 / selectedCheckboxes.length)), // 根据所选知识点数量动态分配题目数量，确保至少为2
                        includeShortAnswer: true
                    });

                    knowledgeNames.push(knowledgeName);
                });

                // Create a default title for multi-selection
                let paperTitle = "";
                if (knowledgeNames.length === 1) {
                    paperTitle = knowledgeNames[0] + " 专项练习";
                } else if (knowledgeNames.length <= 3) {
                    paperTitle = knowledgeNames.join("+") + " 组合练习";
                } else {
                    paperTitle = knowledgeNames[0] + "等" + knowledgeNames.length + "个知识点 组合练习";
                }

                // Set defaults and open modal
                $('#paperTitle').val(paperTitle);
                $('#generatePaperForm').data('knowledgeConfigs', knowledgeConfigs);

                // Apply sensible defaults
                if ($('#singleChoiceCount').val() === '' || parseInt($('#singleChoiceCount').val()) <= 0) {
                    $('#singleChoiceCount').val(Math.min(5, 2 * knowledgeConfigs.length));
                    $('#singleChoiceScore').val(3);
                }

                if ($('#multipleChoiceCount').val() === '' || parseInt($('#multipleChoiceCount').val()) <= 0) {
                    $('#multipleChoiceCount').val(Math.min(5, 2 * knowledgeConfigs.length));
                    $('#multipleChoiceScore').val(3);
                }

                if ($('#judgmentCount').val() === '' || parseInt($('#judgmentCount').val()) <= 0) {
                    $('#judgmentCount').val(Math.min(5, 2 * knowledgeConfigs.length));
                    $('#judgmentScore').val(2);
                }

                // Show the modal
                $('#paperGenerationModal').modal({
                    backdrop: 'static',
                    keyboard: false,
                    show: true
                });

                // Update preview
                updatePaperPreview();

                // Apply visual feedback
                const $btn = $(this);
                $btn.addClass('btn-success').removeClass('btn-primary');
                setTimeout(function() {
                    $btn.addClass('btn-primary').removeClass('btn-success');
                }, 1000);
            });

            $('#paperGenerationModal').on('shown.bs.modal', function() {
                console.log("Paper generation modal shown.");
                // 记录当前表单数据状态
                console.log('当前表单配置:', {
                    singleChoiceCount: $('#singleChoiceCount').val(),
                    multipleChoiceCount: $('#multipleChoiceCount').val(),
                    judgmentCount: $('#judgmentCount').val(),
                    fillCount: $('#fillCount').val(),
                    shortAnswerCount: $('#shortAnswerCount').val(),
                    knowledgeConfigs: $('#paperGenerationForm').data('knowledgeConfigs') || window.currentKnowledgeConfigs || []
                });
                updatePaperPreview();
                updateDifficultyChart();
                // 检查题型可用性
                checkTopicTypeAvailability();
            });

            // 检查题型可用性并显示警告
            function checkTopicTypeAvailability() {
                // 获取当前选择的知识点配置
                const knowledgeConfigs = $('#paperGenerationForm').data('knowledgeConfigs') || window.currentKnowledgeConfigs || [];
                if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
                    // 没有知识点配置，隐藏警告
                    $('#questionTypeAvailabilityWarning').hide();
                    return;
                }

                // 收集所有知识点ID
                const knowledgeIds = knowledgeConfigs.map(config => config.knowledgeId);

                // 显示警告区域并更新状态为加载中
                $('#questionTypeAvailabilityWarning').show();
                $('#typeAvailabilityDetails').html(`
                    <div class="text-center py-2">
                        <i class="fas fa-spinner fa-spin mr-2"></i> 正在分析知识点题库容量...
                    </div>
                `);

                // 发送请求获取题型可用性信息
                $.ajax({
                    url: '/api/topics/availability',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        knowledgePointIds: knowledgeIds
                    }),
                    success: function(response) {
                        if (response && response.data) {
                            updateTypeAvailabilityWarning(response.data);
                        } else {
                            // 请求成功但无数据
                            $('#typeAvailabilityDetails').html(`
                                <p class="mb-1 text-muted"><i class="fas fa-info-circle mr-1"></i> 无法获取题型可用性信息</p>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('获取题型可用性失败:', error);
                        $('#typeAvailabilityDetails').html(`
                            <p class="mb-1 text-danger"><i class="fas fa-exclamation-triangle mr-1"></i> 获取题型可用性信息失败</p>
                        `);
                    }
                });
            }

            // 更新题型可用性警告区域
            function updateTypeAvailabilityWarning(availabilityData) {
                // 处理获取到的题型可用性数据
                let hasWarnings = false;
                let detailsHtml = '<p class="mb-2"><strong>题型可用性分析结果：</strong></p>';

                // 创建一个表格显示每种题型的可用数量
                detailsHtml += '<table class="table table-sm mb-1" style="font-size: 0.85rem;">';
                detailsHtml += '<thead><tr><th>题型</th><th>可用数量</th><th>需求数量</th><th>状态</th></tr></thead>';
                detailsHtml += '<tbody>';

                // 获取当前配置的题型数量
                const singleChoiceCount = parseInt($('#singleChoiceCount').val()) || 0;
                const multipleChoiceCount = parseInt($('#multipleChoiceCount').val()) || 0;
                const judgmentCount = parseInt($('#judgmentCount').val()) || 0;
                const fillCount = parseInt($('#fillCount').val()) || 0;
                const shortAnswerCount = parseInt($('#shortAnswerCount').val()) || 0;

                // 映射题型名称
                const typeNames = {
                    'singleChoice': '单选题',
                    'multipleChoice': '多选题',
                    'judgment': '判断题',
                    'fillInBlanks': '填空题',
                    'shortAnswer': '简答题'
                };

                // 映射题型与配置项的对应关系
                const typeCountMapping = {
                    'singleChoice': singleChoiceCount,
                    'multipleChoice': multipleChoiceCount,
                    'judgment': judgmentCount,
                    'fillInBlanks': fillCount,
                    'shortAnswer': shortAnswerCount
                };

                // 如果有可用性数据，构建表格行
                if (availabilityData.typeAvailability) {
                    // 处理每种题型的可用性
                    Object.keys(typeCountMapping).forEach(typeKey => {
                        const requestedCount = typeCountMapping[typeKey];
                        const availableCount = availabilityData.typeAvailability[typeKey] || 0;
                        let statusHtml = '';

                        if (requestedCount === 0) {
                            // 未配置该题型
                            statusHtml = '<span class="badge badge-secondary">未使用</span>';
                        } else if (availableCount === 0) {
                            // 题型完全缺失
                            statusHtml = '<span class="badge badge-danger">完全缺失</span>';
                            hasWarnings = true;
                        } else if (availableCount < requestedCount) {
                            // 题型数量不足
                            statusHtml = '<span class="badge badge-warning">数量不足</span>';
                            hasWarnings = true;
                        } else {
                            // 数量充足
                            statusHtml = '<span class="badge badge-success">充足</span>';
                        }

                        detailsHtml += `<tr>
                            <td>${typeNames[typeKey] || typeKey}</td>
                            <td>${availableCount}</td>
                            <td>${requestedCount}</td>
                            <td>${statusHtml}</td>
                        </tr>`;
                    });
                } else {
                    // 没有具体可用性数据
                    detailsHtml += `<tr><td colspan="4" class="text-center">无可用题型数据</td></tr>`;
                }

                detailsHtml += '</tbody></table>';

                // 显示总体提示信息
                if (hasWarnings) {
                    detailsHtml += `
                    <div class="alert alert-warning py-2 mb-0 mt-2" style="font-size: 0.85rem;">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <strong>警告：</strong> 部分题型可用数量不足，系统将尽量满足您的需求，但最终生成的试卷可能会有所不同。
                    </div>`;

                    // 修改警告框样式
                    $('#questionTypeAvailabilityWarning')
                        .removeClass('alert-info')
                        .addClass('alert-warning');
                } else {
                    detailsHtml += `
                    <div class="alert alert-success py-2 mb-0 mt-2" style="font-size: 0.85rem;">
                        <i class="fas fa-check-circle mr-1"></i>
                        <strong>良好：</strong> 所有已配置题型的可用数量充足。
                    </div>`;

                    // 修改回信息样式
                    $('#questionTypeAvailabilityWarning')
                        .removeClass('alert-warning')
                        .addClass('alert-info');
                }

                // 更新详情区域
                $('#typeAvailabilityDetails').html(detailsHtml);
            }

            // Add event listener to reset form when modal is hidden
            $('#paperGenerationModal').on('hidden.bs.modal', function () {
                console.log("Paper generation modal hidden. Resetting form.");
                resetPaperGenerationForm();
            });

            // 添加试卷类型筛选按钮事件
            $('.btn-group[aria-label="试卷类型筛选"] button').on('click', function() {
                // 更新活动状态
                $(this).siblings().removeClass('active');
                $(this).addClass('active');

                // 重置页码并加载数据
                currentPage = 0;
                loadPaperHistory();
            });

            // 添加排序选择事件
            $('#paperSortSelect').on('change', function() {
                // 重置页码并加载数据
                currentPage = 0;
                loadPaperHistory();
            });

            // 添加搜索输入事件
            // 搜索输入框事件处理
            $('#paperSearchInput').on('input', function() {
                const searchValue = $(this).val().trim();
                const $clearBtn = $('#paperSearchClearBtn');

                // 显示/隐藏清除按钮
                if (searchValue.length > 0) {
                    $clearBtn.removeClass('d-none');
                } else {
                    $clearBtn.addClass('d-none');
                }

                // 使用延时执行，降低服务器负荷
                clearTimeout(window.paperSearchTimeout);
                window.paperSearchTimeout = setTimeout(function() {
                    currentPage = 0;
                    loadPaperHistory();
                }, 500); // 500毫秒延迟再搜索
            });

            // 清除搜索按钮事件
            $('#paperSearchClearBtn').on('click', function() {
                $('#paperSearchInput').val('');
                $(this).addClass('d-none');
                currentPage = 0;
                loadPaperHistory(); // 重新加载试卷历史
            });

            // 添加刷新按钮事件处理
            $('#refreshPaperHistoryBtn').on('click', function() {
                const $btn = $(this);
                $btn.html('<i class="fas fa-spinner fa-spin mr-1"></i> 刷新中...');
                $btn.prop('disabled', true);

                // 清空当前页码，确保从第一页开始刷新
                currentPage = 0;

                // 取消所有筛选和搜索
                $('#paperSearchInput').val('');
                $('.btn-group[aria-label="试卷类型筛选"] button').removeClass('active')
                    .filter('[data-paper-filter="all"]').addClass('active');
                $('#paperSortSelect').val('time-desc');

                // 加载试卷历史
                loadPaperHistory();

                // 1秒后恢复按钮状态
                setTimeout(function() {
                    $btn.html('<i class="fas fa-sync-alt mr-1"></i> 刷新');
                    $btn.prop('disabled', false);
                }, 1000);
            });

            // 初始化全局变量
            window.currentPage = 0;
            window.pageSize = 10; // 默认每页显示10条记录

            // Initializations
            updatePaperPreview();
            updateDifficultyChart();
            loadKnowledgeGroups();
            loadPaperHistory(); // 页面加载时自动加载试卷历史
        });
    </script>
    <!-- Paper Preview Modal -->
    <div class="modal fade" id="paperPreviewModal" tabindex="-1" aria-labelledby="paperPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paperPreviewModalLabel">试卷预览</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="paperPreviewModalBody">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">正在加载...</span>
                        </div>
                        <p>正在加载试卷内容...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" id="paperPreviewViewBtn"><i class="fas fa-eye mr-1"></i>查看完整试卷</button>
                    <button type="button" class="btn btn-primary" id="paperPreviewDownloadBtn"><i class="fas fa-download mr-1"></i>下载</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/static/js/common.js}"></script> <!-- Common JS -->
    <!-- 自由组卷功能脚本 -->
    <script th:src="@{/static/js/custom-paper.js}"></script>

    <script>
        // 初始化自由组卷按钮点击事件
        $(document).ready(function() {
            $('#customPaperBtn').click(function() {
                $('#customPaperModal').modal('show');
            });

            // 刷新脚本按钮点击事件
            $('#refreshScriptsBtn').click(function() {
                // 显示加载中状态
                const $btn = $(this);
                const originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin mr-1"></i>刷新中...');
                $btn.prop('disabled', true);

                // 生成带随机参数的URL强制浏览器重新加载脚本
                const timestamp = new Date().getTime();
                const commonJsUrl = '/static/js/common.js?v=' + timestamp;
                const customPaperJsUrl = '/static/js/custom-paper.js?v=' + timestamp;

                // 先加载 common.js
                $.getScript(commonJsUrl)
                    .done(function() {
                        console.log('Successfully reloaded common.js');
                        // 然后加载 custom-paper.js
                        return $.getScript(customPaperJsUrl);
                    })
                    .done(function() {
                        console.log('Successfully reloaded custom-paper.js');
                        // 重新加载知识点
                        loadKnowledgePoints();
                        // 恢复按钮状态
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                        // 显示成功消息
                        Swal.fire({
                            icon: 'success',
                            title: '脚本已刷新',
                            text: '知识点功能已重新加载',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    })
                    .fail(function(jqxhr, settings, exception) {
                        console.error('Error reloading scripts:', exception);
                        // 恢复按钮状态
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                        // 显示错误消息
                        Swal.fire({
                            icon: 'error',
                            title: '脚本刷新失败',
                            text: '请刷新整个页面后重试',
                            confirmButtonText: '确定'
                        });
                    });
            });
        });
    </script>
</body>
</html>