-- Add difficulty_distribution column to papers table
ALTER TABLE `papers` 
ADD COLUMN `difficulty_distribution` JSON DEFAULT NULL COMMENT '难度分布JSON，格式：[{"1": 0.3, "2": 0.5, "3": 0.2}] 表示简单30%，中等50%，困难20%' AFTER `difficulty`;

-- Populate difficulty_distribution for existing papers based on their difficulty value
UPDATE `papers` SET `difficulty_distribution` = 
  CASE 
    WHEN `difficulty` <= 0.4 THEN JSON_ARRAY(JSON_OBJECT('1', 0.8, '2', 0.2, '3', 0))
    WHEN `difficulty` <= 0.7 THEN JSON_ARRAY(JSON_OBJECT('1', 0.2, '2', 0.7, '3', 0.1))
    ELSE JSON_ARRAY(JSON_OBJECT('1', 0, '2', 0.3, '3', 0.7))
  END
WHERE `difficulty` IS NOT NULL AND `is_deleted` = 0;

-- Create an index on the difficulty_distribution column for better query performance
ALTER TABLE `papers` ADD INDEX `idx_difficulty_distribution` ((CAST(`difficulty_distribution`->'$[0].1' AS DECIMAL(10,2))));

-- Add a trigger to automatically calculate difficulty_distribution when difficulty is updated
DELIMITER //
CREATE TRIGGER `tr_papers_difficulty_update` BEFORE UPDATE ON `papers`
FOR EACH ROW
BEGIN
  IF NEW.`difficulty` IS NOT NULL AND (OLD.`difficulty` <> NEW.`difficulty` OR OLD.`difficulty_distribution` IS NULL) THEN
    IF NEW.`difficulty` <= 0.4 THEN
      SET NEW.`difficulty_distribution` = JSON_ARRAY(JSON_OBJECT('1', 0.8, '2', 0.2, '3', 0));
    ELSEIF NEW.`difficulty` <= 0.7 THEN
      SET NEW.`difficulty_distribution` = JSON_ARRAY(JSON_OBJECT('1', 0.2, '2', 0.7, '3', 0.1));
    ELSE
      SET NEW.`difficulty_distribution` = JSON_ARRAY(JSON_OBJECT('1', 0, '2', 0.3, '3', 0.7));
    END IF;
  END IF;
END //
DELIMITER ;

-- Add a trigger to automatically calculate difficulty_distribution for new papers
DELIMITER //
CREATE TRIGGER `tr_papers_difficulty_insert` BEFORE INSERT ON `papers`
FOR EACH ROW
BEGIN
  IF NEW.`difficulty` IS NOT NULL AND NEW.`difficulty_distribution` IS NULL THEN
    IF NEW.`difficulty` <= 0.4 THEN
      SET NEW.`difficulty_distribution` = JSON_ARRAY(JSON_OBJECT('1', 0.8, '2', 0.2, '3', 0));
    ELSEIF NEW.`difficulty` <= 0.7 THEN
      SET NEW.`difficulty_distribution` = JSON_ARRAY(JSON_OBJECT('1', 0.2, '2', 0.7, '3', 0.1));
    ELSE
      SET NEW.`difficulty_distribution` = JSON_ARRAY(JSON_OBJECT('1', 0, '2', 0.3, '3', 0.7));
    END IF;
  END IF;
END //
DELIMITER ;
