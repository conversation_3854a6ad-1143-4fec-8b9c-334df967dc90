package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识点约束检查器
 * 负责检查和验证知识点级别的题目分配约束
 */
@Component
@Slf4j
public class KnowledgePointConstraintChecker {

    /**
     * 检查选中的题目是否满足知识点级别的约束
     *
     * @param selectedTopics 选中的题目列表
     * @param knowledgePointConfigs 知识点配置列表
     * @return 约束检查结果
     */
    public KnowledgePointConstraintResult checkConstraints(List<Topic> selectedTopics,
                                                          List<KnowledgePointConfigRequest> knowledgePointConfigs) {

        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return new KnowledgePointConstraintResult(false, "没有选中任何题目");
        }

        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return new KnowledgePointConstraintResult(true, "没有知识点约束");
        }

        // 按知识点分组题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = selectedTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        List<String> violations = new ArrayList<>();

        // 检查每个知识点的约束
        for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
            Integer knowledgeId = config.getKnowledgeId().intValue();
            List<Topic> kpTopics = topicsByKnowledgePoint.getOrDefault(knowledgeId, Collections.emptyList());

            // 检查总题目数量约束
            KnowledgePointConstraintResult totalCountResult = checkTotalQuestionCount(config, kpTopics);
            if (!totalCountResult.isValid()) {
                violations.add(String.format("知识点%d: %s", knowledgeId, totalCountResult.getMessage()));
            }

            // 检查简答题约束（如果有配置）
            if (config.hasShortAnswerConfiguration()) {
                KnowledgePointConstraintResult shortAnswerResult = checkShortAnswerConstraints(config, kpTopics);
                if (!shortAnswerResult.isValid()) {
                    violations.add(String.format("知识点%d: %s", knowledgeId, shortAnswerResult.getMessage()));
                }
            }
        }

        if (violations.isEmpty()) {
            return new KnowledgePointConstraintResult(true, "所有知识点约束都满足");
        } else {
            return new KnowledgePointConstraintResult(false, String.join("; ", violations));
        }
    }

    /**
     * 检查总题目数量约束
     */
    private KnowledgePointConstraintResult checkTotalQuestionCount(KnowledgePointConfigRequest config,
                                                                  List<Topic> kpTopics) {

        Integer expectedCount = config.getQuestionCount();
        if (expectedCount == null || expectedCount <= 0) {
            return new KnowledgePointConstraintResult(true, "没有总题目数量约束");
        }

        int actualCount = kpTopics.size();

        // 允许一定的容差（±1题）
        int tolerance = 1;
        if (Math.abs(actualCount - expectedCount) <= tolerance) {
            return new KnowledgePointConstraintResult(true,
                String.format("总题目数量满足约束（期望%d，实际%d）", expectedCount, actualCount));
        } else {
            return new KnowledgePointConstraintResult(false,
                String.format("总题目数量不满足约束（期望%d，实际%d）", expectedCount, actualCount));
        }
    }

    /**
     * 检查简答题约束
     */
    private KnowledgePointConstraintResult checkShortAnswerConstraints(KnowledgePointConfigRequest config,
                                                                      List<Topic> kpTopics) {

        int expectedShortAnswerCount = config.getShortAnswerCount();
        if (expectedShortAnswerCount <= 0) {
            return new KnowledgePointConstraintResult(true, "没有简答题约束");
        }

        // 统计实际的简答题数量
        long actualShortAnswerCount = kpTopics.stream()
            .filter(topic -> "short".equals(normalizeTopicType(topic.getType())))
            .count();

        if (actualShortAnswerCount == expectedShortAnswerCount) {
            return new KnowledgePointConstraintResult(true,
                String.format("简答题数量满足约束（期望%d，实际%d）", expectedShortAnswerCount, actualShortAnswerCount));
        } else {
            return new KnowledgePointConstraintResult(false,
                String.format("简答题数量不满足约束（期望%d，实际%d）", expectedShortAnswerCount, actualShortAnswerCount));
        }
    }

    /**
     * 生成知识点约束的修复建议
     */
    public List<KnowledgePointAdjustmentSuggestion> generateAdjustmentSuggestions(
            List<Topic> selectedTopics,
            List<KnowledgePointConfigRequest> knowledgePointConfigs,
            List<Topic> availableTopics) {

        List<KnowledgePointAdjustmentSuggestion> suggestions = new ArrayList<>();

        // 按知识点分组可用题目
        Map<Integer, List<Topic>> availableTopicsByKp = availableTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        // 按知识点分组已选题目
        Map<Integer, List<Topic>> selectedTopicsByKp = selectedTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
            Integer knowledgeId = config.getKnowledgeId().intValue();
            List<Topic> kpSelectedTopics = selectedTopicsByKp.getOrDefault(knowledgeId, Collections.emptyList());
            List<Topic> kpAvailableTopics = availableTopicsByKp.getOrDefault(knowledgeId, Collections.emptyList());

            KnowledgePointAdjustmentSuggestion suggestion = generateSingleKpSuggestion(
                config, kpSelectedTopics, kpAvailableTopics);

            if (suggestion != null) {
                suggestions.add(suggestion);
            }
        }

        return suggestions;
    }

    /**
     * 为单个知识点生成调整建议
     */
    private KnowledgePointAdjustmentSuggestion generateSingleKpSuggestion(
            KnowledgePointConfigRequest config,
            List<Topic> selectedTopics,
            List<Topic> availableTopics) {

        Integer knowledgeId = config.getKnowledgeId().intValue();

        // 如果有简答题配置，生成简答题建议
        if (config.hasShortAnswerConfiguration()) {
            return generateShortAnswerSuggestion(knowledgeId, config, selectedTopics, availableTopics);
        }

        // 否则使用总数量配置
        if (config.getQuestionCount() != null && config.getQuestionCount() > 0) {
            return generateCountBasedSuggestion(knowledgeId, config, selectedTopics, availableTopics);
        }

        return null;
    }

    /**
     * 基于简答题配置生成建议
     */
    private KnowledgePointAdjustmentSuggestion generateShortAnswerSuggestion(
            Integer knowledgeId,
            KnowledgePointConfigRequest config,
            List<Topic> selectedTopics,
            List<Topic> availableTopics) {

        int expectedShortAnswer = config.getShortAnswerCount();
        long actualShortAnswer = selectedTopics.stream()
            .filter(topic -> "short".equals(normalizeTopicType(topic.getType())))
            .count();

        if (actualShortAnswer == expectedShortAnswer) {
            return null; // 简答题数量正确，无需调整
        }

        String adjustment;
        if (actualShortAnswer < expectedShortAnswer) {
            int needed = expectedShortAnswer - (int)actualShortAnswer;
            long available = availableTopics.stream()
                .filter(t -> "short".equals(normalizeTopicType(t.getType())))
                .count();

            if (available >= needed) {
                adjustment = String.format("需要增加%d道简答题", needed);
            } else {
                adjustment = String.format("需要增加%d道简答题，但只有%d道可用", needed, available);
            }
        } else {
            int excess = (int)actualShortAnswer - expectedShortAnswer;
            adjustment = String.format("需要减少%d道简答题", excess);
        }

        return new KnowledgePointAdjustmentSuggestion(knowledgeId,
            "简答题数量调整", adjustment);
    }

    /**
     * 基于总数量配置生成建议
     */
    private KnowledgePointAdjustmentSuggestion generateCountBasedSuggestion(
            Integer knowledgeId,
            KnowledgePointConfigRequest config,
            List<Topic> selectedTopics,
            List<Topic> availableTopics) {

        int expected = config.getQuestionCount();
        int actual = selectedTopics.size();

        if (actual == expected) {
            return null; // 数量正确，无需调整
        }

        String adjustment;
        if (actual < expected) {
            int needed = expected - actual;
            adjustment = String.format("需要增加%d道题目", needed);
        } else {
            int excess = actual - expected;
            adjustment = String.format("需要减少%d道题目", excess);
        }

        return new KnowledgePointAdjustmentSuggestion(knowledgeId, "题目数量调整", adjustment);
    }

    /**
     * 标准化题型名称
     */
    private String normalizeTopicType(String type) {
        if (type == null) return "unknown";

        switch (type.toLowerCase()) {
            case "choice":
            case "single_choice":
            case "singlechoice":
                return "choice";
            case "multiple":
            case "multiple_choice":
            case "multiplechoice":
                return "multiple";
            case "judge":
            case "judgment":
                return "judge";
            case "fill":
            case "fill_blank":
            case "fillblank":
                return "fill";
            case "short":
            case "short_answer":
            case "shortanswer":
                return "short";
            default:
                return type.toLowerCase();
        }
    }

    /**
     * 获取题型显示名称
     */
    private String getTypeDisplayName(String type) {
        switch (type) {
            case "choice": return "单选";
            case "multiple": return "多选";
            case "judge": return "判断";
            case "fill": return "填空";
            case "short": return "简答";
            default: return type;
        }
    }

    /**
     * 知识点约束检查结果
     */
    public static class KnowledgePointConstraintResult {
        private final boolean valid;
        private final String message;

        public KnowledgePointConstraintResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("KnowledgePointConstraintResult{valid=%s, message='%s'}", valid, message);
        }
    }

    /**
     * 知识点调整建议
     */
    public static class KnowledgePointAdjustmentSuggestion {
        private final Integer knowledgeId;
        private final String adjustmentType;
        private final String suggestion;

        public KnowledgePointAdjustmentSuggestion(Integer knowledgeId, String adjustmentType, String suggestion) {
            this.knowledgeId = knowledgeId;
            this.adjustmentType = adjustmentType;
            this.suggestion = suggestion;
        }

        public Integer getKnowledgeId() {
            return knowledgeId;
        }

        public String getAdjustmentType() {
            return adjustmentType;
        }

        public String getSuggestion() {
            return suggestion;
        }

        @Override
        public String toString() {
            return String.format("知识点%d - %s: %s", knowledgeId, adjustmentType, suggestion);
        }
    }
}
