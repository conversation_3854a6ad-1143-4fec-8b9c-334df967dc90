<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh">
<head>
    <meta charset="UTF-8">
    <title th:text="${pageTitle ?: '试卷预览'}"></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://polyfill-fastly.io/v3/polyfill.min.js"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script th:inline="none">
        // Configure MathJax to support various LaTeX delimiters
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            },
            startup: {
                typeset: false // Do not typeset immediately, wait for the page to be fully loaded
            }
        };
    </script>
    <style>
        body {
            font-family: 'SimSun', serif; /* 宋体 for better Chinese character display in print */
            line-height: 1.6;
            background-color: #f0f0f0; /* Light gray background for screen */
            margin: 0;
            padding: 20px;
        }

        .preview-container {
            max-width: 800px; /* Limit width on screen */
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .paper {
            /* Styles for A4 printing */
            width: 100%;
            margin: 0 auto;
            page-break-after: always;
        }

        .paper-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .paper-title {
            font-size: 24px;
            font-weight: bold;
        }

        .paper-meta {
            font-size: 14px;
            color: #555;
        }

        .section {
            margin-top: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .question {
            margin-bottom: 15px;
            padding-left: 5px;
        }

        .question-text {
            font-weight: normal; /* Ensure question text is not bold if section title is */
            margin-bottom: 5px;
        }
        
        .question-content p { /* Assuming content might have <p> tags */
            margin-bottom: 5px;
        }

        .options {
            padding-left: 20px; /* Indent options */
        }

        .option {
            margin-bottom: 3px;
        }
        
        .print-button-container {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* A4 Print Styles */
        @media print {
            @page {
                size: A4;
                margin: 1.5cm; /* Adjust margin as needed */
            }

            body {
                background-color: #fff; /* White background for print */
                padding: 0;
                margin: 0;
                font-size: 12pt; /* Standard print font size */
            }

            .preview-container {
                max-width: none;
                margin: 0;
                padding: 0;
                box-shadow: none;
                border: none;
            }
            
            .print-button-container {
                display: none; /* Hide print button when printing */
            }

            .paper {
                width: 100%; /* Use full available width within page margins */
                min-height: initial; /* Not needed for print, content flows */
                padding: 0;
                margin: 0 auto;
                border: none;
                box-shadow: none;
                background: transparent;
            }
        }
    </style>
</head>
<body>

<div class="preview-container">
    <div th:if="${paper}" class="paper">
        <div class="paper-header">
            <h1 class="paper-title" th:text="${paper.title}">试卷标题</h1>
            <p class="paper-meta">
                总分: <span th:text="${paper.totalScore}">100</span> |
                难度: <span th:text="${paper.difficulty != null ? #numbers.formatDecimal(paper.difficulty, 1, 2) : 'N/A'}">中等</span> |
                创建时间: <span th:text="${paper.createTime != null ? #temporals.format(paper.createTime, 'yyyy-MM-dd HH:mm') : 'N/A'}">2023-01-01</span>
            </p>
        </div>

        <div class="print-button-container">
            <button class="btn btn-primary" onclick="window.print();">
                <i class="fas fa-print"></i> 打印试卷
            </button>
             <a th:href="@{'/api/papers/download/' + ${paper.id}}" class="btn btn-success ml-2">
                <i class="fas fa-download"></i> 下载PDF
            </a>
        </div>

        <div th:if="${paper.topicsByType != null and !paper.topicsByType.isEmpty()}">
            <div th:each="entry : ${paper.topicsByType}" class="section">
                <h2 class="section-title" th:text="${entry.key} + ' (共' + ${#lists.size(entry.value)} + '题)'">题型标题</h2>
                <div th:each="topic, iterStat : ${entry.value}" class="question">
                    <div class="question-text">
                        <b th:text="${iterStat.count} + '. '">1. </b>
                        <span th:utext="${topic.title}" class="topic-title-content">题目内容...</span>
                        <span th:if="${topic.score != null}" th:text="' (' + ${topic.score} + '分)'" style="font-style: italic; color: #555;"> (5分)</span>
                    </div>
                    <!-- Display options for choice questions -->
                    <div th:if="${topic.type == 'choice' || topic.type == 'multiple' || topic.type == 'singleChoice' || topic.type == 'multipleChoice'}" class="options">
                        <div th:if="${topic.options != null && topic.options.length() > 2}">
                            <div class="option" th:each="option : ${parsedOptions__${topic.id}__}">
                                <span th:text="${option.key} + '. ' + ${option.name}">A. 选项内容</span>
                            </div>
                        </div>
                        <div th:if="${topic.options == null || topic.options.length() <= 2}" class="text-muted">
                            (无选项数据)
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:unless="${paper.topicsByType != null and !paper.topicsByType.isEmpty()}" class="alert alert-warning">
            此试卷没有详细的题目内容可供预览，或者题目数据未正确加载。
        </div>

    </div>
    <div th:unless="${paper}" class="alert alert-danger">
        加载试卷详情失败，或指定的试卷不存在。
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
</body>
</html> 