2025-05-28 14:41:02.753 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator.
2025-05-28 14:41:19.302 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:41:19.463 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:44:10.886 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:44:10.901 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:47:40.515 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator.
2025-05-28 14:47:56.168 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:47:56.272 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:50:47.224 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:50:47.235 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:51:01.188 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:51:01.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:54:05.521 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:54:05.532 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:54:25.121 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:54:25.130 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:54:46.374 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 14:54:46.683 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 14:56:16.142 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 14:56:16.151 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 14:56:24.673 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成PDF - 试卷ID: 136, 类型: regular, 配置: {"globalTypeScoreMap":{"SINGLE_CHOICE":3,"MULTIPLE_CHOICE":5,"JUDGE":4,"FILL":3,"SHORT":20},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"SINGLE_CHOICE":40,"MULTIPLE_CHOICE":20,"JUDGE":10,"FILL":0,"SHORT":2},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":10,"includeShortAnswer":false,"shortAnswerCount":0,"basicQuestionCount":10,"totalQuestionCount":10},{"knowledgeId":86,"questionCount":11,"includeShortAnswer":true,"shortAnswerCount":1,"basicQuestionCount":11,"totalQuestionCount":12},{"knowledgeId":106,"questionCount":12,"includeShortAnswer":false,"shortAnswerCount":0,"basicQuestionCount":12,"totalQuestionCount":12},{"knowledgeId":195,"questionCount":2,"includeShortAnswer":false,"shortAnswerCount":0,"basicQuestionCount":2,"totalQuestionCount":2},{"knowledgeId":196,"questionCount":6,"includeShortAnswer":false,"shortAnswerCount":0,"basicQuestionCount":6,"totalQuestionCount":6},{"knowledgeId":204,"questionCount":6,"includeShortAnswer":false,"shortAnswerCount":0,"basicQuestionCount":6,"totalQuestionCount":6},{"knowledgeId":206,"questionCount":4,"includeShortAnswer":false,"shortAnswerCount":0,"basicQuestionCount":4,"totalQuestionCount":4},{"knowledgeId":61,"questionCount":19,"includeShortAnswer":true,"shortAnswerCount":1,"basicQuestionCount":19,"totalQuestionCount":20}]}
2025-05-28 14:56:24.830 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: fonts/simhei.ttf
2025-05-28 14:56:24.857 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功读取SimHei字体数据，大小: 10044356 bytes
2025-05-28 14:56:24.899 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: fonts/simsun.ttf
2025-05-28 14:56:24.900 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功读取SimSun字体数据，大小: 14 bytes
2025-05-28 14:56:24.904 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 加载SimSun字体失败: fonts/simsun.ttf is not a valid TTF or OTF file.
2025-05-28 14:56:24.904 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: fonts/simsun.ttf is not a valid TTF or OTF file.
2025-05-28 14:56:24.905 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-28 14:56:24.919 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 解析配置得到的分值映射（数据库格式）: {FILL=3, SINGLE_CHOICE=3, JUDGE=4, MULTIPLE_CHOICE=5, SHORT=20}
2025-05-28 14:56:24.919 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 从配置解析的分值映射: {FILL=3, SINGLE_CHOICE=3, JUDGE=4, MULTIPLE_CHOICE=5, SHORT=20}
2025-05-28 14:56:24.920 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loading 72 topics for paper id: 136
2025-05-28 14:56:24.937 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Found 72 topics in database (from 72 requested IDs) for paper id: 136
2025-05-28 14:56:24.947 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Returning 72 ordered topics for paper id: 136
2025-05-28 14:56:25.002 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91875 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"4"},{"key":"B","name":"5"},{"key":"C","name":"6"},{"key":"D","name":"7"}]
2025-05-28 14:56:25.002 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 91875 的选项，选项数据: '[{"key":"A","name":"4"},{"key":"B","name":"5"},{"key":"C","name":"6"},{"key":"D","name":"7"}]'
2025-05-28 14:56:25.002 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.002 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.002 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=4}
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 4
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=5}
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 5
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=6}
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 6
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=7}
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 7
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 91875 最终解析得到 4 个选项
2025-05-28 14:56:25.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 91875 开始添加 4 个选项到PDF
2025-05-28 14:56:25.032 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91879 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"$\\{x|x是12的倍数\\}$"},{"key":"B","name":"$\\{x|x是8的倍数\\}$"},{"key":"C","name":"$\\{x|x是10的倍数\\}$"},{"key":"D","name":"$\\{x|x是14的倍数\\}$"}]
2025-05-28 14:56:25.032 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 91879 的选项，选项数据: '[{"key":"A","name":"$\\{x|x是12的倍数\\}$"},{"key":"B","name":"$\\{x|x是8的倍数\\}$"},{"key":"C","name":"$\\{x|x是10的倍数\\}$"},{"key":"D","name":"$\\{x|x是14的倍数\\}$"}]'
2025-05-28 14:56:25.032 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.032 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.032 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=$\{x|x是12的倍数\}$}
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> $\{x|x是12的倍数\}$
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=$\{x|x是8的倍数\}$}
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> $\{x|x是8的倍数\}$
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=$\{x|x是10的倍数\}$}
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> $\{x|x是10的倍数\}$
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=$\{x|x是14的倍数\}$}
2025-05-28 14:56:25.033 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> $\{x|x是14的倍数\}$
2025-05-28 14:56:25.034 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 91879 最终解析得到 4 个选项
2025-05-28 14:56:25.034 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 91879 开始添加 4 个选项到PDF
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91137 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"单调递增"},{"key":"B","name":"单调递减"},{"key":"C","name":"在$(-\\infty,0)$上单调递减，在$(0,+\\infty)$上单调递增"},{"key":"D","name":"在$(-\\infty,0)$上单调递增，在$(0,+\\infty)$上单调递减"}]
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 91137 的选项，选项数据: '[{"key":"A","name":"单调递增"},{"key":"B","name":"单调递减"},{"key":"C","name":"在$(-\\infty,0)$上单调递减，在$(0,+\\infty)$上单调递增"},{"key":"D","name":"在$(-\\infty,0)$上单调递增，在$(0,+\\infty)$上单调递减"}]'
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=单调递增}
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 单调递增
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=单调递减}
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 单调递减
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=在$(-\infty,0)$上单调递减，在$(0,+\infty)$上单调递增}
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 在$(-\infty,0)$上单调递减，在$(0,+\infty)$上单调递增
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=在$(-\infty,0)$上单调递增，在$(0,+\infty)$上单调递减}
2025-05-28 14:56:25.039 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 在$(-\infty,0)$上单调递增，在$(0,+\infty)$上单调递减
2025-05-28 14:56:25.040 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 91137 最终解析得到 4 个选项
2025-05-28 14:56:25.040 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 91137 开始添加 4 个选项到PDF
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 27416 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"旋转磁场"},{"key":"B","name":"静电场"},{"key":"C","name":"静止磁场"},{"key":"D","name":"无法确定"}]
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 27416 的选项，选项数据: '[{"key":"A","name":"旋转磁场"},{"key":"B","name":"静电场"},{"key":"C","name":"静止磁场"},{"key":"D","name":"无法确定"}]'
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=旋转磁场}
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 旋转磁场
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=静电场}
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 静电场
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=静止磁场}
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 静止磁场
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=无法确定}
2025-05-28 14:56:25.045 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 无法确定
2025-05-28 14:56:25.046 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 27416 最终解析得到 4 个选项
2025-05-28 14:56:25.046 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 27416 开始添加 4 个选项到PDF
2025-05-28 14:56:25.047 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 27423 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"产生电磁转矩"},{"key":"B","name":"防止飞车"},{"key":"C","name":"限制启地动电流"},{"key":"D","name":"防止产生过大的反电动势"}]
2025-05-28 14:56:25.047 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 27423 的选项，选项数据: '[{"key":"A","name":"产生电磁转矩"},{"key":"B","name":"防止飞车"},{"key":"C","name":"限制启地动电流"},{"key":"D","name":"防止产生过大的反电动势"}]'
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=产生电磁转矩}
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 产生电磁转矩
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=防止飞车}
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 防止飞车
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=限制启地动电流}
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 限制启地动电流
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=防止产生过大的反电动势}
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 防止产生过大的反电动势
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 27423 最终解析得到 4 个选项
2025-05-28 14:56:25.048 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 27423 开始添加 4 个选项到PDF
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 27429 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"停机"},{"key":"B","name":"运行"},{"key":"C","name":"保持"},{"key":"D","name":"无法确定"}]
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 27429 的选项，选项数据: '[{"key":"A","name":"停机"},{"key":"B","name":"运行"},{"key":"C","name":"保持"},{"key":"D","name":"无法确定"}]'
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=停机}
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 停机
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=运行}
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 运行
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=保持}
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 保持
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=无法确定}
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 无法确定
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 27429 最终解析得到 4 个选项
2025-05-28 14:56:25.050 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 27429 开始添加 4 个选项到PDF
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 77682 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"10Ω"},{"key":"B","name":"25Ω"},{"key":"C","name":"5Ω"},{"key":"D","name":"0.1Ω"}]
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 77682 的选项，选项数据: '[{"key":"A","name":"10Ω"},{"key":"B","name":"25Ω"},{"key":"C","name":"5Ω"},{"key":"D","name":"0.1Ω"}]'
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=10Ω}
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 10Ω
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=25Ω}
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 25Ω
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=5Ω}
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 5Ω
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=0.1Ω}
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 0.1Ω
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 77682 最终解析得到 4 个选项
2025-05-28 14:56:25.052 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 77682 开始添加 4 个选项到PDF
2025-05-28 14:56:25.053 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 78411 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"立即进行人工呼吸"},{"key":"B","name":"立即切断电源"},{"key":"C","name":"用手将触电者拉开"},{"key":"D","name":"拨打急救电话"}]
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 78411 的选项，选项数据: '[{"key":"A","name":"立即进行人工呼吸"},{"key":"B","name":"立即切断电源"},{"key":"C","name":"用手将触电者拉开"},{"key":"D","name":"拨打急救电话"}]'
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=立即进行人工呼吸}
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 立即进行人工呼吸
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=立即切断电源}
2025-05-28 14:56:25.054 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 立即切断电源
2025-05-28 14:56:25.055 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=用手将触电者拉开}
2025-05-28 14:56:25.055 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 用手将触电者拉开
2025-05-28 14:56:25.055 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=拨打急救电话}
2025-05-28 14:56:25.055 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 拨打急救电话
2025-05-28 14:56:25.055 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78411 最终解析得到 4 个选项
2025-05-28 14:56:25.055 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78411 开始添加 4 个选项到PDF
2025-05-28 14:56:25.056 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 78439 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"用水灭火"},{"key":"B","name":"用灭火器灭火"},{"key":"C","name":"切断电源"},{"key":"D","name":"拨打火警电话"}]
2025-05-28 14:56:25.056 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 78439 的选项，选项数据: '[{"key":"A","name":"用水灭火"},{"key":"B","name":"用灭火器灭火"},{"key":"C","name":"切断电源"},{"key":"D","name":"拨打火警电话"}]'
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=用水灭火}
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 用水灭火
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=用灭火器灭火}
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 用灭火器灭火
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=切断电源}
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 切断电源
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=拨打火警电话}
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 拨打火警电话
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78439 最终解析得到 4 个选项
2025-05-28 14:56:25.057 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78439 开始添加 4 个选项到PDF
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 78461 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"在大树下避雨"},{"key":"B","name":"靠近高压电线杆"},{"key":"C","name":"远离高压线和避雷针"},{"key":"D","name":"使用手机通话"}]
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 78461 的选项，选项数据: '[{"key":"A","name":"在大树下避雨"},{"key":"B","name":"靠近高压电线杆"},{"key":"C","name":"远离高压线和避雷针"},{"key":"D","name":"使用手机通话"}]'
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=在大树下避雨}
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 在大树下避雨
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=靠近高压电线杆}
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 靠近高压电线杆
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=远离高压线和避雷针}
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 远离高压线和避雷针
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=使用手机通话}
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 使用手机通话
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78461 最终解析得到 4 个选项
2025-05-28 14:56:25.059 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78461 开始添加 4 个选项到PDF
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 78586 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"增加触头数量和容量"},{"key":"B","name":"控制电路通断"},{"key":"C","name":"短路保护"},{"key":"D","name":"过载保护"}]
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 78586 的选项，选项数据: '[{"key":"A","name":"增加触头数量和容量"},{"key":"B","name":"控制电路通断"},{"key":"C","name":"短路保护"},{"key":"D","name":"过载保护"}]'
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=增加触头数量和容量}
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 增加触头数量和容量
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=控制电路通断}
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 控制电路通断
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=短路保护}
2025-05-28 14:56:25.061 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 短路保护
2025-05-28 14:56:25.062 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=过载保护}
2025-05-28 14:56:25.062 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 过载保护
2025-05-28 14:56:25.062 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78586 最终解析得到 4 个选项
2025-05-28 14:56:25.062 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 78586 开始添加 4 个选项到PDF
2025-05-28 14:56:25.063 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 10143 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"抽象思维"},{"key":"B","name":"逻辑思维"},{"key":"C","name":"空间思维"},{"key":"D","name":"想象"}]
2025-05-28 14:56:25.063 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 10143 的选项，选项数据: '[{"key":"A","name":"抽象思维"},{"key":"B","name":"逻辑思维"},{"key":"C","name":"空间思维"},{"key":"D","name":"想象"}]'
2025-05-28 14:56:25.063 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.063 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.063 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=抽象思维}
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 抽象思维
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=逻辑思维}
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 逻辑思维
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=空间思维}
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 空间思维
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=想象}
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 想象
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 10143 最终解析得到 4 个选项
2025-05-28 14:56:25.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 10143 开始添加 4 个选项到PDF
2025-05-28 14:56:25.065 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 58832 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"习惯型购买行为\t"},{"key":"B","name":"经济型购买行为"},{"key":"C","name":"冲动型购买行为"},{"key":"D","name":"理智型购买行为"}]
2025-05-28 14:56:25.065 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 58832 的选项，选项数据: '[{"key":"A","name":"习惯型购买行为\t"},{"key":"B","name":"经济型购买行为"},{"key":"C","name":"冲动型购买行为"},{"key":"D","name":"理智型购买行为"}]'
2025-05-28 14:56:25.065 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.065 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.065 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=习惯型购买行为	}
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 习惯型购买行为	
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=经济型购买行为}
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 经济型购买行为
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=冲动型购买行为}
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 冲动型购买行为
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=理智型购买行为}
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 理智型购买行为
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58832 最终解析得到 4 个选项
2025-05-28 14:56:25.066 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58832 开始添加 4 个选项到PDF
2025-05-28 14:56:25.067 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 58836 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"文化因素"},{"key":"B","name":"心理因素"},{"key":"C","name":"个人因素"},{"key":"D","name":"A、社会因素"}]
2025-05-28 14:56:25.067 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 58836 的选项，选项数据: '[{"key":"A","name":"文化因素"},{"key":"B","name":"心理因素"},{"key":"C","name":"个人因素"},{"key":"D","name":"A、社会因素"}]'
2025-05-28 14:56:25.067 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.067 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.067 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=文化因素}
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 文化因素
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=心理因素}
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 心理因素
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=个人因素}
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 个人因素
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=A、社会因素}
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> A、社会因素
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58836 最终解析得到 4 个选项
2025-05-28 14:56:25.068 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58836 开始添加 4 个选项到PDF
2025-05-28 14:56:25.069 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 58837 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"民族亚文化"},{"key":"B","name":"宗教亚文化"},{"key":"C","name":"种族亚文化\t"},{"key":"D","name":"地理亚文化"}]
2025-05-28 14:56:25.069 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 58837 的选项，选项数据: '[{"key":"A","name":"民族亚文化"},{"key":"B","name":"宗教亚文化"},{"key":"C","name":"种族亚文化\t"},{"key":"D","name":"地理亚文化"}]'
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=民族亚文化}
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 民族亚文化
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=宗教亚文化}
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 宗教亚文化
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=种族亚文化	}
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 种族亚文化	
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=地理亚文化}
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 地理亚文化
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58837 最终解析得到 4 个选项
2025-05-28 14:56:25.070 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58837 开始添加 4 个选项到PDF
2025-05-28 14:56:25.071 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 58906 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"人口环境\t"},{"key":"B","name":".技术环境"},{"key":"C","name":"经济环境\t"},{"key":"D","name":"社会文化环境"}]
2025-05-28 14:56:25.071 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 58906 的选项，选项数据: '[{"key":"A","name":"人口环境\t"},{"key":"B","name":".技术环境"},{"key":"C","name":"经济环境\t"},{"key":"D","name":"社会文化环境"}]'
2025-05-28 14:56:25.071 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.071 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.071 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=人口环境	}
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 人口环境	
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=.技术环境}
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> .技术环境
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=经济环境	}
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 经济环境	
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=社会文化环境}
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 社会文化环境
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58906 最终解析得到 4 个选项
2025-05-28 14:56:25.072 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58906 开始添加 4 个选项到PDF
2025-05-28 14:56:25.074 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 95447 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"book"},{"key":"B","name":"books"},{"key":"C","name":"a book"},{"key":"D","name":"book's"}]
2025-05-28 14:56:25.074 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 95447 的选项，选项数据: '[{"key":"A","name":"book"},{"key":"B","name":"books"},{"key":"C","name":"a book"},{"key":"D","name":"book's"}]'
2025-05-28 14:56:25.075 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.075 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.075 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=book}
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> book
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=books}
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> books
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=a book}
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> a book
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=book's}
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> book's
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95447 最终解析得到 4 个选项
2025-05-28 14:56:25.076 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95447 开始添加 4 个选项到PDF
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 95456 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"animal"},{"key":"B","name":"animals"},{"key":"C","name":"a animal"},{"key":"D","name":"animal's"}]
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 95456 的选项，选项数据: '[{"key":"A","name":"animal"},{"key":"B","name":"animals"},{"key":"C","name":"a animal"},{"key":"D","name":"animal's"}]'
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=animal}
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> animal
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=animals}
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> animals
2025-05-28 14:56:25.077 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=a animal}
2025-05-28 14:56:25.078 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> a animal
2025-05-28 14:56:25.078 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=animal's}
2025-05-28 14:56:25.078 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> animal's
2025-05-28 14:56:25.078 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95456 最终解析得到 4 个选项
2025-05-28 14:56:25.078 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95456 开始添加 4 个选项到PDF
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 95482 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"desk"},{"key":"B","name":"desks"},{"key":"C","name":"a desk"}]
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 95482 的选项，选项数据: '[{"key":"A","name":"desk"},{"key":"B","name":"desks"},{"key":"C","name":"a desk"}]'
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 3
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 3 个选项
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=desk}
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> desk
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=desks}
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> desks
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=a desk}
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> a desk
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95482 最终解析得到 3 个选项
2025-05-28 14:56:25.079 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95482 开始添加 3 个选项到PDF
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 95495 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"plot"},{"key":"B","name":"plots"},{"key":"C","name":"a plot"}]
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 95495 的选项，选项数据: '[{"key":"A","name":"plot"},{"key":"B","name":"plots"},{"key":"C","name":"a plot"}]'
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 3
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 3 个选项
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=plot}
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> plot
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=plots}
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> plots
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=a plot}
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> a plot
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95495 最终解析得到 3 个选项
2025-05-28 14:56:25.095 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95495 开始添加 3 个选项到PDF
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 95514 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"pair"},{"key":"B","name":"pairs"},{"key":"C","name":"many pairs"}]
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 95514 的选项，选项数据: '[{"key":"A","name":"pair"},{"key":"B","name":"pairs"},{"key":"C","name":"many pairs"}]'
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 3
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 3 个选项
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=pair}
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> pair
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=pairs}
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> pairs
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=many pairs}
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> many pairs
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95514 最终解析得到 3 个选项
2025-05-28 14:56:25.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 95514 开始添加 3 个选项到PDF
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 96315 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"book"},{"key":"B","name":"pen"},{"key":"C","name":"computer"},{"key":"D","name":"phone"}]
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 96315 的选项，选项数据: '[{"key":"A","name":"book"},{"key":"B","name":"pen"},{"key":"C","name":"computer"},{"key":"D","name":"phone"}]'
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=book}
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> book
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=pen}
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> pen
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=computer}
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> computer
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=phone}
2025-05-28 14:56:25.098 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> phone
2025-05-28 14:56:25.099 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96315 最终解析得到 4 个选项
2025-05-28 14:56:25.099 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96315 开始添加 4 个选项到PDF
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 96318 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"apples"},{"key":"B","name":"oranges"},{"key":"C","name":"bananas"},{"key":"D","name":"pears"}]
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 96318 的选项，选项数据: '[{"key":"A","name":"apples"},{"key":"B","name":"oranges"},{"key":"C","name":"bananas"},{"key":"D","name":"pears"}]'
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=apples}
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> apples
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=oranges}
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> oranges
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=bananas}
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> bananas
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=pears}
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> pears
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96318 最终解析得到 4 个选项
2025-05-28 14:56:25.100 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96318 开始添加 4 个选项到PDF
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 7320 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"增大"},{"key":"B","name":"减少"},{"key":"C","name":"不变"},{"key":"D","name":"先增大后减少"},{"key":"E","name":"先减少后减少"}]
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 7320 的选项，选项数据: '[{"key":"A","name":"增大"},{"key":"B","name":"减少"},{"key":"C","name":"不变"},{"key":"D","name":"先增大后减少"},{"key":"E","name":"先减少后减少"}]'
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=增大}
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 增大
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=减少}
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 减少
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=不变}
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 不变
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=先增大后减少}
2025-05-28 14:56:25.101 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 先增大后减少
2025-05-28 14:56:25.102 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=先减少后减少}
2025-05-28 14:56:25.102 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 先减少后减少
2025-05-28 14:56:25.102 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 7320 最终解析得到 5 个选项
2025-05-28 14:56:25.102 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 7320 开始添加 5 个选项到PDF
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 7354 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"引起动作电位的临界膜电位"},{"key":"B","name":"引起超极化时的临界膜电位"},{"key":"C","name":"引起局部电位的临界膜电位"},{"key":"D","name":"引起动作电位复极的临界膜电位"},{"key":"E","name":"衡量兴奋性高低的指标"}]
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 7354 的选项，选项数据: '[{"key":"A","name":"引起动作电位的临界膜电位"},{"key":"B","name":"引起超极化时的临界膜电位"},{"key":"C","name":"引起局部电位的临界膜电位"},{"key":"D","name":"引起动作电位复极的临界膜电位"},{"key":"E","name":"衡量兴奋性高低的指标"}]'
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=引起动作电位的临界膜电位}
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 引起动作电位的临界膜电位
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=引起超极化时的临界膜电位}
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 引起超极化时的临界膜电位
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=引起局部电位的临界膜电位}
2025-05-28 14:56:25.104 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 引起局部电位的临界膜电位
2025-05-28 14:56:25.105 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=引起动作电位复极的临界膜电位}
2025-05-28 14:56:25.105 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 引起动作电位复极的临界膜电位
2025-05-28 14:56:25.105 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=衡量兴奋性高低的指标}
2025-05-28 14:56:25.105 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 衡量兴奋性高低的指标
2025-05-28 14:56:25.105 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 7354 最终解析得到 5 个选项
2025-05-28 14:56:25.105 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 7354 开始添加 5 个选项到PDF
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 7545 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"横管系统"},{"key":"B","name":"纵管系统"},{"key":"C","name":"肌浆"},{"key":"D","name":"纵管终末池"},{"key":"E","name":"三联管结构"}]
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 7545 的选项，选项数据: '[{"key":"A","name":"横管系统"},{"key":"B","name":"纵管系统"},{"key":"C","name":"肌浆"},{"key":"D","name":"纵管终末池"},{"key":"E","name":"三联管结构"}]'
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=横管系统}
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 横管系统
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=纵管系统}
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 纵管系统
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=肌浆}
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 肌浆
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=纵管终末池}
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 纵管终末池
2025-05-28 14:56:25.107 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=三联管结构}
2025-05-28 14:56:25.108 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 三联管结构
2025-05-28 14:56:25.108 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 7545 最终解析得到 5 个选项
2025-05-28 14:56:25.108 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 7545 开始添加 5 个选项到PDF
2025-05-28 14:56:25.110 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 8673 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"全身性体液调节"},{"key":"B","name":"局部性体液调节"},{"key":"C","name":"神经体液调节"},{"key":"D","name":"神经调节"},{"key":"E","name":"自身调节"}]
2025-05-28 14:56:25.110 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 8673 的选项，选项数据: '[{"key":"A","name":"全身性体液调节"},{"key":"B","name":"局部性体液调节"},{"key":"C","name":"神经体液调节"},{"key":"D","name":"神经调节"},{"key":"E","name":"自身调节"}]'
2025-05-28 14:56:25.110 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=全身性体液调节}
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 全身性体液调节
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=局部性体液调节}
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 局部性体液调节
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=神经体液调节}
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 神经体液调节
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=神经调节}
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 神经调节
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=自身调节}
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 自身调节
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 8673 最终解析得到 5 个选项
2025-05-28 14:56:25.111 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 8673 开始添加 5 个选项到PDF
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 8683 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"正反馈调节"},{"key":"B","name":"负反馈调节"},{"key":"C","name":"神经调节"},{"key":"D","name":"体液调节"},{"key":"E","name":"自身调节"}]
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 8683 的选项，选项数据: '[{"key":"A","name":"正反馈调节"},{"key":"B","name":"负反馈调节"},{"key":"C","name":"神经调节"},{"key":"D","name":"体液调节"},{"key":"E","name":"自身调节"}]'
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=正反馈调节}
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 正反馈调节
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=负反馈调节}
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 负反馈调节
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=神经调节}
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 神经调节
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=体液调节}
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 体液调节
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=自身调节}
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 自身调节
2025-05-28 14:56:25.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 8683 最终解析得到 5 个选项
2025-05-28 14:56:25.114 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 8683 开始添加 5 个选项到PDF
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 90867 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"Once a week. "},{"key":"B","name":"I like sports. "},{"key":"C","name":"It's good for health. "},{"key":"D","name":"I don't know."}]
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 90867 的选项，选项数据: '[{"key":"A","name":"Once a week. "},{"key":"B","name":"I like sports. "},{"key":"C","name":"It's good for health. "},{"key":"D","name":"I don't know."}]'
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=Once a week. }
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> Once a week. 
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=I like sports. }
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> I like sports. 
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=It's good for health. }
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> It's good for health. 
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=I don't know.}
2025-05-28 14:56:25.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> I don't know.
2025-05-28 14:56:25.116 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 90867 最终解析得到 4 个选项
2025-05-28 14:56:25.116 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 90867 开始添加 4 个选项到PDF
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 90924 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"It's ten yuan.  "},{"key":"B","name":"I don't know"},{"key":"C","name":" It's too expensive."},{"key":"D","name":" I don't like this book."}]
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 90924 的选项，选项数据: '[{"key":"A","name":"It's ten yuan.  "},{"key":"B","name":"I don't know"},{"key":"C","name":" It's too expensive."},{"key":"D","name":" I don't like this book."}]'
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=It's ten yuan.  }
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> It's ten yuan.  
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=I don't know}
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> I don't know
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name= It's too expensive.}
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C ->  It's too expensive.
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name= I don't like this book.}
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D ->  I don't like this book.
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 90924 最终解析得到 4 个选项
2025-05-28 14:56:25.118 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 90924 开始添加 4 个选项到PDF
2025-05-28 14:56:25.120 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 90925 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"I went to Beijing. "},{"key":"B","name":" I like traveling. "},{"key":"C","name":" It was great."},{"key":"D","name":"I don't know."}]
2025-05-28 14:56:25.120 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 90925 的选项，选项数据: '[{"key":"A","name":"I went to Beijing. "},{"key":"B","name":" I like traveling. "},{"key":"C","name":" It was great."},{"key":"D","name":"I don't know."}]'
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=I went to Beijing. }
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> I went to Beijing. 
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name= I like traveling. }
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B ->  I like traveling. 
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name= It was great.}
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C ->  It was great.
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=I don't know.}
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> I don't know.
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 90925 最终解析得到 4 个选项
2025-05-28 14:56:25.121 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 90925 开始添加 4 个选项到PDF
2025-05-28 14:56:25.122 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 101224 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"Yes, I have."},{"key":"B","name":"I'm fine."},{"key":"C","name":"How are you?"},{"key":"D","name":"Nice to meet you."}]
2025-05-28 14:56:25.122 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 101224 的选项，选项数据: '[{"key":"A","name":"Yes, I have."},{"key":"B","name":"I'm fine."},{"key":"C","name":"How are you?"},{"key":"D","name":"Nice to meet you."}]'
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=Yes, I have.}
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> Yes, I have.
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=I'm fine.}
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> I'm fine.
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=How are you?}
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> How are you?
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=Nice to meet you.}
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> Nice to meet you.
2025-05-28 14:56:25.123 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 101224 最终解析得到 4 个选项
2025-05-28 14:56:25.124 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 101224 开始添加 4 个选项到PDF
2025-05-28 14:56:25.125 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 101367 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"I play football"},{"key":"B","name":"I played football"},{"key":"C","name":"I am playing football"},{"key":"D","name":"I will play football"}]
2025-05-28 14:56:25.125 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 101367 的选项，选项数据: '[{"key":"A","name":"I play football"},{"key":"B","name":"I played football"},{"key":"C","name":"I am playing football"},{"key":"D","name":"I will play football"}]'
2025-05-28 14:56:25.125 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=I play football}
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> I play football
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=I played football}
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> I played football
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=I am playing football}
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> I am playing football
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=I will play football}
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> I will play football
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 101367 最终解析得到 4 个选项
2025-05-28 14:56:25.126 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 101367 开始添加 4 个选项到PDF
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 101371 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"Do"},{"key":"B","name":"Are"},{"key":"C","name":"Can"},{"key":"D","name":"Have"}]
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 101371 的选项，选项数据: '[{"key":"A","name":"Do"},{"key":"B","name":"Are"},{"key":"C","name":"Can"},{"key":"D","name":"Have"}]'
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=Do}
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> Do
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=Are}
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> Are
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=Can}
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> Can
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=Have}
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> Have
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 101371 最终解析得到 4 个选项
2025-05-28 14:56:25.127 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 101371 开始添加 4 个选项到PDF
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 92532 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"霜打的茄子——蔫了"},{"key":"B","name":"马尾穿豆腐——提不起来"},{"key":"C","name":"瞎子点灯——白费蜡"},{"key":"D","name":"泥菩萨过河——自身难保"}]
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 92532 的选项，选项数据: '[{"key":"A","name":"霜打的茄子——蔫了"},{"key":"B","name":"马尾穿豆腐——提不起来"},{"key":"C","name":"瞎子点灯——白费蜡"},{"key":"D","name":"泥菩萨过河——自身难保"}]'
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=霜打的茄子——蔫了}
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 霜打的茄子——蔫了
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=马尾穿豆腐——提不起来}
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 马尾穿豆腐——提不起来
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=瞎子点灯——白费蜡}
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 瞎子点灯——白费蜡
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=泥菩萨过河——自身难保}
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 泥菩萨过河——自身难保
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92532 最终解析得到 4 个选项
2025-05-28 14:56:25.129 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92532 开始添加 4 个选项到PDF
2025-05-28 14:56:25.130 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 92567 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"八仙过海——各显神通"},{"key":"B","name":"泥菩萨过江——自身难保"},{"key":"C","name":"蚕豆开花——黑心"},{"key":"D","name":"以上都正确"}]
2025-05-28 14:56:25.130 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 92567 的选项，选项数据: '[{"key":"A","name":"八仙过海——各显神通"},{"key":"B","name":"泥菩萨过江——自身难保"},{"key":"C","name":"蚕豆开花——黑心"},{"key":"D","name":"以上都正确"}]'
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=八仙过海——各显神通}
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 八仙过海——各显神通
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=泥菩萨过江——自身难保}
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 泥菩萨过江——自身难保
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=蚕豆开花——黑心}
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 蚕豆开花——黑心
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=以上都正确}
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 以上都正确
2025-05-28 14:56:25.131 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92567 最终解析得到 4 个选项
2025-05-28 14:56:25.132 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92567 开始添加 4 个选项到PDF
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 92930 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"艾青"},{"key":"B","name":"徐志摩"},{"key":"C","name":"戴望舒"},{"key":"D","name":"舒婷"}]
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 92930 的选项，选项数据: '[{"key":"A","name":"艾青"},{"key":"B","name":"徐志摩"},{"key":"C","name":"戴望舒"},{"key":"D","name":"舒婷"}]'
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=艾青}
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 艾青
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=徐志摩}
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 徐志摩
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=戴望舒}
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 戴望舒
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=舒婷}
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 舒婷
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92930 最终解析得到 4 个选项
2025-05-28 14:56:25.133 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92930 开始添加 4 个选项到PDF
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 92941 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"戴望舒"},{"key":"B","name":"徐志摩"},{"key":"C","name":"卞之琳"},{"key":"D","name":"闻一多"}]
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 92941 的选项，选项数据: '[{"key":"A","name":"戴望舒"},{"key":"B","name":"徐志摩"},{"key":"C","name":"卞之琳"},{"key":"D","name":"闻一多"}]'
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=戴望舒}
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 戴望舒
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=徐志摩}
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 徐志摩
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=卞之琳}
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 卞之琳
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=闻一多}
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 闻一多
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92941 最终解析得到 4 个选项
2025-05-28 14:56:25.134 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 92941 开始添加 4 个选项到PDF
2025-05-28 14:56:25.135 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 93032 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"王维"},{"key":"B","name":"孟浩然"},{"key":"C","name":"李白"},{"key":"D","name":"杜甫"}]
2025-05-28 14:56:25.135 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 93032 的选项，选项数据: '[{"key":"A","name":"王维"},{"key":"B","name":"孟浩然"},{"key":"C","name":"李白"},{"key":"D","name":"杜甫"}]'
2025-05-28 14:56:25.135 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.135 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.135 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=王维}
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 王维
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=孟浩然}
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 孟浩然
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=李白}
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 李白
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=杜甫}
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 杜甫
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 93032 最终解析得到 4 个选项
2025-05-28 14:56:25.136 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 93032 开始添加 4 个选项到PDF
2025-05-28 14:56:25.137 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 93190 - 原始类型: choice, 内部类型: SINGLE_CHOICE, 选项数据: [{"key":"A","name":"杜甫"},{"key":"B","name":"李白"},{"key":"C","name":"王维"},{"key":"D","name":"白居易"}]
2025-05-28 14:56:25.137 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 93190 的选项，选项数据: '[{"key":"A","name":"杜甫"},{"key":"B","name":"李白"},{"key":"C","name":"王维"},{"key":"D","name":"白居易"}]'
2025-05-28 14:56:25.137 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.137 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.137 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=杜甫}
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 杜甫
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=李白}
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 李白
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=王维}
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 王维
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=白居易}
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 白居易
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 93190 最终解析得到 4 个选项
2025-05-28 14:56:25.138 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 93190 开始添加 4 个选项到PDF
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 80148 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"感抗是表示电感对电流阻碍作用的物理量"},{"key":"B","name":"感抗的大小与电感和角频率都有关"},{"key":"C","name":" 角频率一定时，电感越大，感抗越大"},{"key":"D","name":"电感一定时，角频率越大，感抗越大"}]
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 80148 的选项，选项数据: '[{"key":"A","name":"感抗是表示电感对电流阻碍作用的物理量"},{"key":"B","name":"感抗的大小与电感和角频率都有关"},{"key":"C","name":" 角频率一定时，电感越大，感抗越大"},{"key":"D","name":"电感一定时，角频率越大，感抗越大"}]'
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=感抗是表示电感对电流阻碍作用的物理量}
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 感抗是表示电感对电流阻碍作用的物理量
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=感抗的大小与电感和角频率都有关}
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 感抗的大小与电感和角频率都有关
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name= 角频率一定时，电感越大，感抗越大}
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C ->  角频率一定时，电感越大，感抗越大
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=电感一定时，角频率越大，感抗越大}
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 电感一定时，角频率越大，感抗越大
2025-05-28 14:56:25.140 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 80148 最终解析得到 4 个选项
2025-05-28 14:56:25.141 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 80148 开始添加 4 个选项到PDF
2025-05-28 14:56:25.142 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 80468 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"结构简单"},{"key":"B","name":"价格便宜"},{"key":"C","name":"动作可靠"},{"key":"D","name":"分断能力强"}]
2025-05-28 14:56:25.142 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 80468 的选项，选项数据: '[{"key":"A","name":"结构简单"},{"key":"B","name":"价格便宜"},{"key":"C","name":"动作可靠"},{"key":"D","name":"分断能力强"}]'
2025-05-28 14:56:25.142 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.142 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.142 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=结构简单}
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 结构简单
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=价格便宜}
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 价格便宜
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=动作可靠}
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 动作可靠
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=分断能力强}
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 分断能力强
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 80468 最终解析得到 4 个选项
2025-05-28 14:56:25.143 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 80468 开始添加 4 个选项到PDF
2025-05-28 14:56:25.144 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 80749 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"保持清洁"},{"key":"B","name":"轻拿轻放"},{"key":"C","name":"定期校准"},{"key":"D","name":"避免碰撞"}]
2025-05-28 14:56:25.144 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 80749 的选项，选项数据: '[{"key":"A","name":"保持清洁"},{"key":"B","name":"轻拿轻放"},{"key":"C","name":"定期校准"},{"key":"D","name":"避免碰撞"}]'
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=保持清洁}
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 保持清洁
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=轻拿轻放}
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 轻拿轻放
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=定期校准}
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 定期校准
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=避免碰撞}
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 避免碰撞
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 80749 最终解析得到 4 个选项
2025-05-28 14:56:25.145 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 80749 开始添加 4 个选项到PDF
2025-05-28 14:56:25.146 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 81669 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"整个电路的总电阻增大"},{"key":"B","name":" 电路中的电流减小"},{"key":"C","name":"其他电阻上的电压可能会发生变化"},{"key":"D","name":"该电阻上的电压增大"}]
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 81669 的选项，选项数据: '[{"key":"A","name":"整个电路的总电阻增大"},{"key":"B","name":" 电路中的电流减小"},{"key":"C","name":"其他电阻上的电压可能会发生变化"},{"key":"D","name":"该电阻上的电压增大"}]'
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=整个电路的总电阻增大}
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 整个电路的总电阻增大
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name= 电路中的电流减小}
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B ->  电路中的电流减小
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=其他电阻上的电压可能会发生变化}
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 其他电阻上的电压可能会发生变化
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=该电阻上的电压增大}
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 该电阻上的电压增大
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 81669 最终解析得到 4 个选项
2025-05-28 14:56:25.147 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 81669 开始添加 4 个选项到PDF
2025-05-28 14:56:25.148 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 79164 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"安装插座保护盖"},{"key":"B","name":"教育儿童不要玩弄电器"},{"key":"C","name":"把电器设备放在儿童接触不到的地方"},{"key":"D","name":"定期检查家庭电路的安全性"}]
2025-05-28 14:56:25.148 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 79164 的选项，选项数据: '[{"key":"A","name":"安装插座保护盖"},{"key":"B","name":"教育儿童不要玩弄电器"},{"key":"C","name":"把电器设备放在儿童接触不到的地方"},{"key":"D","name":"定期检查家庭电路的安全性"}]'
2025-05-28 14:56:25.148 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.148 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.148 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=安装插座保护盖}
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 安装插座保护盖
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=教育儿童不要玩弄电器}
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 教育儿童不要玩弄电器
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=把电器设备放在儿童接触不到的地方}
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 把电器设备放在儿童接触不到的地方
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=定期检查家庭电路的安全性}
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 定期检查家庭电路的安全性
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 79164 最终解析得到 4 个选项
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 79164 开始添加 4 个选项到PDF
2025-05-28 14:56:25.149 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 58936 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"人口的数量\t"},{"key":"B","name":"人口的构成"},{"key":"C","name":"人口的密度\t"},{"key":"D","name":"收入"},{"key":"E","name":"消费状况"}]
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 58936 的选项，选项数据: '[{"key":"A","name":"人口的数量\t"},{"key":"B","name":"人口的构成"},{"key":"C","name":"人口的密度\t"},{"key":"D","name":"收入"},{"key":"E","name":"消费状况"}]'
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=人口的数量	}
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 人口的数量	
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=人口的构成}
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 人口的构成
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=人口的密度	}
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 人口的密度	
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=收入}
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 收入
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=消费状况}
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 消费状况
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58936 最终解析得到 5 个选项
2025-05-28 14:56:25.150 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 58936 开始添加 5 个选项到PDF
2025-05-28 14:56:25.152 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59063 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"人力不能为"},{"key":"B","name":"耗资大\t"},{"key":"C","name":"占用时间长"},{"key":"D","name":"回收率低"},{"key":"E","name":"不易控制"}]
2025-05-28 14:56:25.152 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59063 的选项，选项数据: '[{"key":"A","name":"人力不能为"},{"key":"B","name":"耗资大\t"},{"key":"C","name":"占用时间长"},{"key":"D","name":"回收率低"},{"key":"E","name":"不易控制"}]'
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=人力不能为}
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 人力不能为
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=耗资大	}
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 耗资大	
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=占用时间长}
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 占用时间长
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=回收率低}
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 回收率低
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=不易控制}
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 不易控制
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59063 最终解析得到 5 个选项
2025-05-28 14:56:25.153 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59063 开始添加 5 个选项到PDF
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59068 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"配额抽样"},{"key":"B","name":"抽签法\t"},{"key":"C","name":"任意抽样"},{"key":"D","name":"机械抽样"},{"key":"E","name":"乱数表法"}]
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59068 的选项，选项数据: '[{"key":"A","name":"配额抽样"},{"key":"B","name":"抽签法\t"},{"key":"C","name":"任意抽样"},{"key":"D","name":"机械抽样"},{"key":"E","name":"乱数表法"}]'
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=配额抽样}
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 配额抽样
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=抽签法	}
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 抽签法	
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=任意抽样}
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 任意抽样
2025-05-28 14:56:25.154 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=机械抽样}
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 机械抽样
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=乱数表法}
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 乱数表法
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59068 最终解析得到 5 个选项
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59068 开始添加 5 个选项到PDF
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59114 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"生活方式"},{"key":"B","name":"教育"},{"key":"C","name":"年龄\t"},{"key":"D","name":"性别"},{"key":"E","name":"收入"}]
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59114 的选项，选项数据: '[{"key":"A","name":"生活方式"},{"key":"B","name":"教育"},{"key":"C","name":"年龄\t"},{"key":"D","name":"性别"},{"key":"E","name":"收入"}]'
2025-05-28 14:56:25.155 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=生活方式}
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 生活方式
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=教育}
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 教育
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=年龄	}
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 年龄	
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=性别}
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 性别
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=收入}
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 收入
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59114 最终解析得到 5 个选项
2025-05-28 14:56:25.156 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59114 开始添加 5 个选项到PDF
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59158 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"声誉与资金"},{"key":"B","name":"管理能力与经验\t"},{"key":"C","name":"控制分销渠道的要求"},{"key":"D","name":"为中间商提供服务"}]
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59158 的选项，选项数据: '[{"key":"A","name":"声誉与资金"},{"key":"B","name":"管理能力与经验\t"},{"key":"C","name":"控制分销渠道的要求"},{"key":"D","name":"为中间商提供服务"}]'
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=声誉与资金}
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 声誉与资金
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=管理能力与经验	}
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 管理能力与经验	
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=控制分销渠道的要求}
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 控制分销渠道的要求
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=为中间商提供服务}
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 为中间商提供服务
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59158 最终解析得到 4 个选项
2025-05-28 14:56:25.157 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59158 开始添加 4 个选项到PDF
2025-05-28 14:56:25.158 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59162 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"直复市场营销"},{"key":"B","name":"直接销售"},{"key":"C","name":"自动售货\t\t"},{"key":"D","name":"购货服务公司\t"},{"key":"E","name":"传销"}]
2025-05-28 14:56:25.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59162 的选项，选项数据: '[{"key":"A","name":"直复市场营销"},{"key":"B","name":"直接销售"},{"key":"C","name":"自动售货\t\t"},{"key":"D","name":"购货服务公司\t"},{"key":"E","name":"传销"}]'
2025-05-28 14:56:25.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 5
2025-05-28 14:56:25.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 5 个选项
2025-05-28 14:56:25.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=直复市场营销}
2025-05-28 14:56:25.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 直复市场营销
2025-05-28 14:56:25.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=直接销售}
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 直接销售
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=自动售货		}
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 自动售货		
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=购货服务公司	}
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 购货服务公司	
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 4: {key=E, name=传销}
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: E -> 传销
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59162 最终解析得到 5 个选项
2025-05-28 14:56:25.160 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59162 开始添加 5 个选项到PDF
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59204 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"消费者接待日"},{"key":"B","name":"开放参观日\t"},{"key":"C","name":"典礼仪式"},{"key":"D","name":"专题竞赛活动"}]
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59204 的选项，选项数据: '[{"key":"A","name":"消费者接待日"},{"key":"B","name":"开放参观日\t"},{"key":"C","name":"典礼仪式"},{"key":"D","name":"专题竞赛活动"}]'
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=消费者接待日}
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 消费者接待日
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=开放参观日	}
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 开放参观日	
2025-05-28 14:56:25.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=典礼仪式}
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 典礼仪式
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=专题竞赛活动}
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 专题竞赛活动
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59204 最终解析得到 4 个选项
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59204 开始添加 4 个选项到PDF
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59206 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"赞助\t"},{"key":"B","name":"募捐\t"},{"key":"C","name":"回扣\t\t"},{"key":"D","name":"赠品"}]
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 59206 的选项，选项数据: '[{"key":"A","name":"赞助\t"},{"key":"B","name":"募捐\t"},{"key":"C","name":"回扣\t\t"},{"key":"D","name":"赠品"}]'
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.162 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=赞助	}
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 赞助	
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=募捐	}
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 募捐	
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=回扣		}
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 回扣		
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=赠品}
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 赠品
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59206 最终解析得到 4 个选项
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 59206 开始添加 4 个选项到PDF
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 96264 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"car"},{"key":"B","name":"book"},{"key":"C","name":"computer"},{"key":"D","name":"phone"}]
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 96264 的选项，选项数据: '[{"key":"A","name":"car"},{"key":"B","name":"book"},{"key":"C","name":"computer"},{"key":"D","name":"phone"}]'
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=car}
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> car
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=book}
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> book
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=computer}
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> computer
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=phone}
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> phone
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96264 最终解析得到 4 个选项
2025-05-28 14:56:25.164 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96264 开始添加 4 个选项到PDF
2025-05-28 14:56:25.165 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 96272 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"teacher"},{"key":"B","name":"doctor"},{"key":"C","name":"nurse"},{"key":"D","name":"engineer"}]
2025-05-28 14:56:25.165 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 96272 的选项，选项数据: '[{"key":"A","name":"teacher"},{"key":"B","name":"doctor"},{"key":"C","name":"nurse"},{"key":"D","name":"engineer"}]'
2025-05-28 14:56:25.165 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=teacher}
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> teacher
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=doctor}
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> doctor
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=nurse}
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> nurse
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=engineer}
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> engineer
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96272 最终解析得到 4 个选项
2025-05-28 14:56:25.166 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 96272 开始添加 4 个选项到PDF
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 71628 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"为细胞提供适宜的生存环境"},{"key":"B","name":"保证细胞新陈代谢的正常进行"},{"key":"C","name":"是机体维持正常生命活动的必要条件"},{"key":"D","name":"有助于机体适应外界环境的变化"}]
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 71628 的选项，选项数据: '[{"key":"A","name":"为细胞提供适宜的生存环境"},{"key":"B","name":"保证细胞新陈代谢的正常进行"},{"key":"C","name":"是机体维持正常生命活动的必要条件"},{"key":"D","name":"有助于机体适应外界环境的变化"}]'
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=为细胞提供适宜的生存环境}
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 为细胞提供适宜的生存环境
2025-05-28 14:56:25.167 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=保证细胞新陈代谢的正常进行}
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 保证细胞新陈代谢的正常进行
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=是机体维持正常生命活动的必要条件}
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 是机体维持正常生命活动的必要条件
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=有助于机体适应外界环境的变化}
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 有助于机体适应外界环境的变化
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71628 最终解析得到 4 个选项
2025-05-28 14:56:25.168 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71628 开始添加 4 个选项到PDF
2025-05-28 14:56:25.171 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 71762 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"神经调节"},{"key":"B","name":"体液调节"},{"key":"C","name":"自身调节"},{"key":"D","name":"免疫调节"}]
2025-05-28 14:56:25.172 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 71762 的选项，选项数据: '[{"key":"A","name":"神经调节"},{"key":"B","name":"体液调节"},{"key":"C","name":"自身调节"},{"key":"D","name":"免疫调节"}]'
2025-05-28 14:56:25.172 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.172 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=神经调节}
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 神经调节
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=体液调节}
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 体液调节
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=自身调节}
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 自身调节
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=免疫调节}
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 免疫调节
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71762 最终解析得到 4 个选项
2025-05-28 14:56:25.173 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71762 开始添加 4 个选项到PDF
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 71904 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"比重"},{"key":"B","name":"粘滞性"},{"key":"C","name":"渗透压"},{"key":"D","name":"pH 值"}]
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 71904 的选项，选项数据: '[{"key":"A","name":"比重"},{"key":"B","name":"粘滞性"},{"key":"C","name":"渗透压"},{"key":"D","name":"pH 值"}]'
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=比重}
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 比重
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=粘滞性}
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 粘滞性
2025-05-28 14:56:25.174 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=渗透压}
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 渗透压
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=pH 值}
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> pH 值
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71904 最终解析得到 4 个选项
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71904 开始添加 4 个选项到PDF
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 71906 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"红细胞数量"},{"key":"B","name":"血浆蛋白含量"},{"key":"C","name":"血流速度"},{"key":"D","name":"血管口径"}]
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 71906 的选项，选项数据: '[{"key":"A","name":"红细胞数量"},{"key":"B","name":"血浆蛋白含量"},{"key":"C","name":"血流速度"},{"key":"D","name":"血管口径"}]'
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=红细胞数量}
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 红细胞数量
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=血浆蛋白含量}
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 血浆蛋白含量
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=血流速度}
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 血流速度
2025-05-28 14:56:25.175 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=血管口径}
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 血管口径
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71906 最终解析得到 4 个选项
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71906 开始添加 4 个选项到PDF
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 71953 - 原始类型: multiple, 内部类型: MULTIPLE_CHOICE, 选项数据: [{"key":"A","name":"人际关系的处理"},{"key":"B","name":"学业成绩的波动"},{"key":"C","name":"职业规划的形成"},{"key":"D","name":"心理健康状况"}]
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始解析题目 71953 的选项，选项数据: '[{"key":"A","name":"人际关系的处理"},{"key":"B","name":"学业成绩的波动"},{"key":"C","name":"职业规划的形成"},{"key":"D","name":"心理健康状况"}]'
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 成功解析为List格式，选项数量: 4
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理List格式选项，共 4 个选项
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 0: {key=A, name=人际关系的处理}
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: A -> 人际关系的处理
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 1: {key=B, name=学业成绩的波动}
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: B -> 学业成绩的波动
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 2: {key=C, name=职业规划的形成}
2025-05-28 14:56:25.176 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: C -> 职业规划的形成
2025-05-28 14:56:25.177 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 选项 3: {key=D, name=心理健康状况}
2025-05-28 14:56:25.177 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 添加选项: D -> 心理健康状况
2025-05-28 14:56:25.177 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71953 最终解析得到 4 个选项
2025-05-28 14:56:25.177 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 题目 71953 开始添加 4 个选项到PDF
2025-05-28 14:56:25.178 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91708 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.179 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 90385 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.179 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91103 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.180 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91104 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.181 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 91126 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.182 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 59086 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.182 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 72035 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.183 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 72040 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.183 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 72076 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.183 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 92209 - 原始类型: judge, 内部类型: JUDGE, 选项数据: 
2025-05-28 14:56:25.184 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 27688 - 原始类型: short, 内部类型: SHORT, 选项数据: 
2025-05-28 14:56:25.184 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 处理题目 71387 - 原始类型: short, 内部类型: SHORT, 选项数据: 
2025-05-28 14:56:25.238 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 136, type: regular
