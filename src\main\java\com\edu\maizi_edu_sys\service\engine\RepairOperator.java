package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 修复算子组件
 * 负责在遗传算法的交叉和变异操作后，对染色体进行局部贪婪修复，
 * 确保题型约束和分数目标的满足。
 */
@Component
@Slf4j
public class RepairOperator {

    @Value("${algorithm.genetic.repair.enabled:true}")
    private boolean repairEnabled;

    @Value("${algorithm.genetic.repair.max-steps:3}")
    private int maxRepairSteps;

    @Value("${algorithm.genetic.repair.greedy-threshold:0.1}")
    private double greedyThreshold;

    /**
     * 修复染色体，确保满足题型约束和分数目标
     *
     * @param chromosome 待修复的染色体
     * @param availableQuestions 可用题目列表
     * @param typeTargetCounts 目标题型数量
     * @param targetScore 目标分数
     * @param typeScores 题型分数映射
     * @param enhancementDataMap 题目增强数据
     * @return 修复步数
     */
    public int repairChromosome(GeneticSolver.Chromosome chromosome,
                               List<Topic> availableQuestions,
                               Map<String, Integer> typeTargetCounts,
                               int targetScore,
                               Map<String, Integer> typeScores,
                               Map<Integer, TopicEnhancementData> enhancementDataMap) {

        if (!repairEnabled || typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            return 0;
        }

        int repairSteps = 0;

        for (int step = 0; step < maxRepairSteps; step++) {
            // 分析当前状态
            RepairAnalysis analysis = analyzeChromosome(chromosome, availableQuestions,
                                                       typeTargetCounts, targetScore, typeScores);

            if (analysis.isOptimal()) {
                break; // 已经满足所有约束
            }

            // 执行一步修复
            boolean improved = performRepairStep(chromosome, availableQuestions, analysis,
                                                enhancementDataMap, typeScores);

            if (improved) {
                repairSteps++;
            } else {
                break; // 无法进一步改进
            }
        }

        return repairSteps;
    }

    /**
     * 分析染色体当前状态
     */
    private RepairAnalysis analyzeChromosome(GeneticSolver.Chromosome chromosome,
                                           List<Topic> availableQuestions,
                                           Map<String, Integer> typeTargetCounts,
                                           int targetScore,
                                           Map<String, Integer> typeScores) {

        // 统计当前选中的题目
        List<Topic> selectedTopics = new ArrayList<>();
        int currentScore = 0;

        for (int i = 0; i < chromosome.getGene().length() && i < availableQuestions.size(); i++) {
            if (chromosome.getGene().get(i)) {
                Topic topic = availableQuestions.get(i);
                selectedTopics.add(topic);
                currentScore += getTopicScore(topic, typeScores);
            }
        }

        // 统计题型分布
        Map<String, Integer> currentTypeCounts = selectedTopics.stream()
            .collect(Collectors.groupingBy(
                topic -> normalizeTopicType(topic.getType()),
                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
            ));

        // 计算缺口
        Map<String, Integer> typeGaps = new HashMap<>();
        int totalTypeGap = 0;

        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int target = entry.getValue();
            int current = currentTypeCounts.getOrDefault(type, 0);
            int gap = target - current;

            if (gap != 0) {
                typeGaps.put(type, gap);
                totalTypeGap += Math.abs(gap);
            }
        }

        int scoreGap = targetScore - currentScore;

        return new RepairAnalysis(currentScore, scoreGap, currentTypeCounts, typeGaps, totalTypeGap);
    }

    /**
     * 执行一步修复操作
     */
    private boolean performRepairStep(GeneticSolver.Chromosome chromosome,
                                    List<Topic> availableQuestions,
                                    RepairAnalysis analysis,
                                    Map<Integer, TopicEnhancementData> enhancementDataMap,
                                    Map<String, Integer> typeScores) {

        boolean improved = false;

        // 优先修复题型缺口
        if (analysis.getTotalTypeGap() > 0) {
            improved = repairTypeGaps(chromosome, availableQuestions, analysis, enhancementDataMap, typeScores);
        }

        // 如果题型已平衡，尝试修复分数缺口
        if (!improved && Math.abs(analysis.getScoreGap()) > analysis.getCurrentScore() * greedyThreshold) {
            improved = repairScoreGap(chromosome, availableQuestions, analysis, enhancementDataMap, typeScores);
        }

        return improved;
    }

    /**
     * 修复题型缺口
     */
    private boolean repairTypeGaps(GeneticSolver.Chromosome chromosome,
                                 List<Topic> availableQuestions,
                                 RepairAnalysis analysis,
                                 Map<Integer, TopicEnhancementData> enhancementDataMap,
                                 Map<String, Integer> typeScores) {

        // 找到最大的正缺口（需要增加的题型）
        String typeToAdd = null;
        int maxPositiveGap = 0;

        for (Map.Entry<String, Integer> entry : analysis.getTypeGaps().entrySet()) {
            if (entry.getValue() > maxPositiveGap) {
                maxPositiveGap = entry.getValue();
                typeToAdd = entry.getKey();
            }
        }

        if (typeToAdd == null) {
            return false;
        }

        // 找到该题型的最佳候选题目
        List<Integer> candidateIndices = new ArrayList<>();
        for (int i = 0; i < availableQuestions.size(); i++) {
            if (!chromosome.getGene().get(i)) { // 未选中的题目
                Topic topic = availableQuestions.get(i);
                if (normalizeTopicType(topic.getType()).equals(typeToAdd)) {
                    candidateIndices.add(i);
                }
            }
        }

        if (candidateIndices.isEmpty()) {
            return false;
        }

        // 按质量排序，选择最佳题目
        candidateIndices.sort((i1, i2) -> {
            Topic t1 = availableQuestions.get(i1);
            Topic t2 = availableQuestions.get(i2);
            double quality1 = calculateTopicQuality(t1, enhancementDataMap);
            double quality2 = calculateTopicQuality(t2, enhancementDataMap);
            return Double.compare(quality2, quality1); // 降序
        });

        // 选择最佳题目
        int bestIndex = candidateIndices.get(0);
        chromosome.getGene().set(bestIndex);

        return true;
    }

    /**
     * 修复分数缺口
     */
    private boolean repairScoreGap(GeneticSolver.Chromosome chromosome,
                                 List<Topic> availableQuestions,
                                 RepairAnalysis analysis,
                                 Map<Integer, TopicEnhancementData> enhancementDataMap,
                                 Map<String, Integer> typeScores) {

        int scoreGap = analysis.getScoreGap();

        if (scoreGap > 0) {
            // 需要增加分数：添加高分题目
            return addHighScoreTopic(chromosome, availableQuestions, scoreGap, enhancementDataMap, typeScores);
        } else {
            // 需要减少分数：移除低分题目或替换为更低分题目
            return reduceScore(chromosome, availableQuestions, -scoreGap, enhancementDataMap, typeScores);
        }
    }

    /**
     * 添加高分题目
     */
    private boolean addHighScoreTopic(GeneticSolver.Chromosome chromosome,
                                    List<Topic> availableQuestions,
                                    int neededScore,
                                    Map<Integer, TopicEnhancementData> enhancementDataMap,
                                    Map<String, Integer> typeScores) {

        int bestIndex = -1;
        int bestScore = 0;
        double bestQuality = -1;

        for (int i = 0; i < availableQuestions.size(); i++) {
            if (!chromosome.getGene().get(i)) { // 未选中的题目
                Topic topic = availableQuestions.get(i);
                int score = getTopicScore(topic, typeScores);
                double quality = calculateTopicQuality(topic, enhancementDataMap);

                // 选择分数接近需求且质量高的题目
                if (score > 0 && score <= neededScore * 1.5) { // 允许一定超出
                    if (bestIndex == -1 ||
                        (Math.abs(score - neededScore) < Math.abs(bestScore - neededScore)) ||
                        (Math.abs(score - neededScore) == Math.abs(bestScore - neededScore) && quality > bestQuality)) {
                        bestIndex = i;
                        bestScore = score;
                        bestQuality = quality;
                    }
                }
            }
        }

        if (bestIndex != -1) {
            chromosome.getGene().set(bestIndex);
            return true;
        }

        return false;
    }

    /**
     * 减少分数
     */
    private boolean reduceScore(GeneticSolver.Chromosome chromosome,
                              List<Topic> availableQuestions,
                              int excessScore,
                              Map<Integer, TopicEnhancementData> enhancementDataMap,
                              Map<String, Integer> typeScores) {

        // 找到分数接近多余分数且质量较低的题目移除
        int bestIndex = -1;
        int bestScore = Integer.MAX_VALUE;
        double worstQuality = Double.MAX_VALUE;

        for (int i = 0; i < availableQuestions.size(); i++) {
            if (chromosome.getGene().get(i)) { // 已选中的题目
                Topic topic = availableQuestions.get(i);
                int score = getTopicScore(topic, typeScores);
                double quality = calculateTopicQuality(topic, enhancementDataMap);

                // 选择分数接近多余分数且质量较低的题目
                if (score > 0 && score <= excessScore * 1.5) {
                    if (bestIndex == -1 ||
                        (Math.abs(score - excessScore) < Math.abs(bestScore - excessScore)) ||
                        (Math.abs(score - excessScore) == Math.abs(bestScore - excessScore) && quality < worstQuality)) {
                        bestIndex = i;
                        bestScore = score;
                        worstQuality = quality;
                    }
                }
            }
        }

        if (bestIndex != -1) {
            chromosome.getGene().clear(bestIndex);
            return true;
        }

        return false;
    }

    /**
     * 计算题目质量
     */
    private double calculateTopicQuality(Topic topic, Map<Integer, TopicEnhancementData> enhancementDataMap) {
        if (enhancementDataMap == null) {
            return 0.5; // 默认中等质量
        }

        TopicEnhancementData data = enhancementDataMap.get(topic.getId());
        if (data == null) {
            return 0.8; // 新题目质量较高
        }

        // 基于使用次数计算质量（使用次数越少质量越高）
        Integer usageCount = data.getUsageCount();
        if (usageCount == null || usageCount == 0) {
            return 1.0;
        }

        return 1.0 / (1.0 + usageCount * 0.1);
    }

    /**
     * 获取题目分数
     */
    private int getTopicScore(Topic topic, Map<String, Integer> typeScores) {
        if (typeScores != null) {
            String normalizedType = normalizeTopicType(topic.getType());
            Integer typeScore = typeScores.get(normalizedType);
            if (typeScore != null) {
                return typeScore;
            }
        }
        return topic.getScore() != null ? topic.getScore() : 0;
    }

    /**
     * 标准化题型名称
     */
    private String normalizeTopicType(String type) {
        if (type == null) return "unknown";

        switch (type.toLowerCase()) {
            case "choice":
            case "single_choice":
            case "singlechoice":
                return "choice";
            case "multiple":
            case "multiple_choice":
            case "multiplechoice":
                return "multiple";
            case "judge":
            case "judgment":
                return "judge";
            case "fill":
            case "fill_blank":
            case "fillblank":
                return "fill";
            case "short":
            case "short_answer":
            case "shortanswer":
                return "short";
            default:
                return type.toLowerCase();
        }
    }

    /**
     * 修复分析结果
     */
    public static class RepairAnalysis {
        private final int currentScore;
        private final int scoreGap;
        private final Map<String, Integer> currentTypeCounts;
        private final Map<String, Integer> typeGaps;
        private final int totalTypeGap;

        public RepairAnalysis(int currentScore, int scoreGap,
                            Map<String, Integer> currentTypeCounts,
                            Map<String, Integer> typeGaps,
                            int totalTypeGap) {
            this.currentScore = currentScore;
            this.scoreGap = scoreGap;
            this.currentTypeCounts = currentTypeCounts;
            this.typeGaps = typeGaps;
            this.totalTypeGap = totalTypeGap;
        }

        public boolean isOptimal() {
            return totalTypeGap == 0 && Math.abs(scoreGap) <= currentScore * 0.05; // 5%容差
        }

        // Getters
        public int getCurrentScore() { return currentScore; }
        public int getScoreGap() { return scoreGap; }
        public Map<String, Integer> getCurrentTypeCounts() { return currentTypeCounts; }
        public Map<String, Integer> getTypeGaps() { return typeGaps; }
        public int getTotalTypeGap() { return totalTypeGap; }
    }
}
