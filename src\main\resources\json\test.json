[
  {
    "know_id": 226,
    "type": "choice",
    "title": "信息系统中负责数据处理的核心设备是（    ）",
    "options": [
      {
        "key": "A",
        "name": "计算机"
      },
      {
        "key": "B",
        "name": "路由器"
      },
      {
        "key": "C",
        "name": "交换机"
      },
      {
        "key": "D",
        "name": "传感器"
      }
    ],
    "answer": "A",
    "parse": "计算机是信息系统的数据处理核心，其他设备负责传输或感知。",
    "source": "3.1.1　计算机、移动终端与信息系统--《信息技术必修二-信息系统与社会》",
    "difficulty": 0.2
  },
  {
    "know_id": 226,
    "type": "multiple",
    "title": "移动终端的特点包括（    ）",
    "options": [
      {
        "key": "A",
        "name": "便携性"
      },
      {
        "key": "B",
        "name": "续航有限"
      },
      {
        "key": "C",
        "性能与台式机相同"
      },
      {
        "key": "D",
        "依赖无线通信"
      ],
        "answer": "ABD",
        "parse": "移动终端性能通常低于台式机，其他选项均为其典型特点。",
        "source": "3.1.2　计算机与移动终端--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.5
      },
      {
        "know_id": 226,
        "type": "judge",
        "title": "局域网的覆盖范围通常大于广域网（    ）",
        "answer": "否",
        "parse": "局域网覆盖范围一般在千米级，广域网可跨城市或国家。",
        "source": "3.2.2　局域网与广域网--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.2
      },
      {
        "know_id": 226,
        "type": "choice",
        "title": "下列属于分组交换技术的是（    ）",
        "options": [
          {
            "key": "A",
            "name": "TCP/IP"
          },
          {
            "key": "B",
            "name": "电路交换"
          },
          {
            "key": "C",
            "name": "报文交换"
          },
          {
            "key": "D",
            "name": "光纤传输"
          }
        ],
        "answer": "A",
        "parse": "TCP/IP基于分组交换，电路和报文交换是其他类型，光纤是物理介质。",
        "source": "3.2.3　数据交换技术--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.6
      },
      {
        "know_id": 226,
        "type": "multiple",
        "title": "星型拓扑的优点包括（    ）",
        "options": [
          {
            "key": "A",
            "name": "易于维护"
          },
          {
            "key": "B",
            "单点故障影响小"
          },
          {
            "key": "C",
            "传输效率高"
          },
          {
            "key": "D",
            "组网成本低"
          }
        ],
        "answer": "AC",
        "parse": "星型拓扑中心节点故障会导致全网瘫痪（B错误），需较多交换机（D错误）。",
        "source": "3.2.4　网络拓扑--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.5
      },
      {
        "know_id": 226,
        "type": "choice",
        "title": "IPv4地址的位数是（    ）",
        "options": [
          {
            "key": "A",
            "name": "16位"
          },
          {
            "key": "B",
            "name": "32位"
          },
          {
            "key": "C",
            "name": "64位"
          },
          {
            "key": "D",
            "name": "128位"
          }
        ],
        "answer": "B",
        "parse": "IPv4地址为32位二进制数，IPv6为128位。",
        "source": "3.2.5　IP地址--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.3
      },
      {
        "know_id": 226,
        "type": "judge",
        "title": "域名必须包含.com后缀（    ）",
        "answer": "否",
        "parse": "域名后缀可以是.org、.net等，.com是常见但非必需。",
        "source": "3.2.6　域名--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.4
      },
      {
        "know_id": 226,
        "type": "choice",
        "title": "组建无线局域网必需的软件是（    ）",
        "options": [
          {
            "key": "A",
            "name": "无线网卡驱动"
          },
          {
            "key": "B",
            "name": "办公软件"
          },
          {
            "key": "C",
            "name": "杀毒软件"
          },
          {
            "key": "D",
            "name": "数据库软件"
          }
        ],
        "answer": "A",
        "parse": "无线网卡驱动是硬件正常工作的必需软件，其他为非必需。",
        "source": "3.2.7　组建无线局域网--《信息技术必修二-信息系统与社会》",
        "difficulty": 0.5
      },
      {
        "know_id": 226,
        "type": "multiple",
        "title": "影响网络带宽的因素包括（    ）",
        "options": [
          {
            "key": "A",
            "name": "接入方式"
          },
          {
            "key": "B",
            "传输介质"
          },
          {
            "key": "C",
            "路由器性能"
          },
          {
            "key": "D",
            "网页内容"
          },
          "answer"
          :
          "ABC",
          "parse"
          :
          "网页内容不影响带宽，带宽由网络基础设施决定。",
          "source"
          :
          "3.2.9　带宽和接入方式对信息系统的影响--《信息技术必修二-信息系统与社会》",
          "difficulty"
          :
          0.6
          },
          {
            "know_id": 226,
            "type": "choice",
            "title": "下列属于应用软件的是（    ）",
            "options": [
              {
                "key": "A",
                "name": "Linux系统"
              },
              {
                "key": "B",
                "name": "Photoshop"
              },
              {
                "key": "C",
                "name": "BIOS固件"
              },
              {
                "key": "D",
                "name": "Python编译器"
              }
            ],
            "answer": "B",
            "parse": "Photoshop是图像处理软件，其他均为系统软件或固件。",
            "source": "3.3.1　软件与信息系统--《信息技术必修二-信息系统与社会》",
            "difficulty": 0.3
          },
          {
            "know_id": 226,
            "type": "multiple",
            "title": "电子邮件系统的组成包括（    ）",
            "options": [
              {
                "key": "A",
                "name": "发件服务器"
              },
              {
                "key": "B",
                "name": "收件服务器"
              },
              {
                "key": "C",
                "邮件客户端"
              },
              {
                "key": "D",
                "路由器"
              }
            ],
            "answer": "ABC",
            "parse": "路由器是网络设备，非电子邮件系统核心组件。",
            "source": "3.3.2　开发简易的电子邮件客户端--《信息技术必修二-信息系统与社会》",
            "difficulty": 0.5
          },
          {
            "know_id": 226,
            "type": "judge",
            "title": "网络聊天系统只能通过文字通信（    ）",
            "answer": "否",
            "parse": "现代网络聊天系统支持语音、视频、文件等多种通信方式。",
            "source": "3.3.3　开发网络聊天系统--《信息技术必修二-信息系统与社会》",
            "difficulty": 0.2
          },
          {
            "know_id": 226,
            "type": "choice",
            "title": "物联网的主要技术基础是（    ）",
            "options": [
              {
                "key": "A",
                "name": "5G技术"
              },
              {
                "key": "B",
                "name": "传感器技术与互联网"
              },
              {
                "key": "C",
                "name": "云计算"
              },
              {
                "key": "D",
                "name": "大数据"
              },
              "answer"
              :
              "B",
              "parse"
              :
              "物联网依赖传感器采集数据，并通过互联网实现设备互联。",
              "source"
              :
              "3.4.1　物联网与信息系统--《信息技术必修二-信息系统与社会》",
              "difficulty"
              :
              0.4
              }
            ]