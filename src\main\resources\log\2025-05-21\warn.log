2025-05-21 09:46:19.325 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 74 没有题目数量统计信息
2025-05-21 09:46:19.330 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 78 没有题目数量统计信息
2025-05-21 09:46:19.336 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 81 没有题目数量统计信息
2025-05-21 09:46:19.396 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 20 要求包含简答题但没有可用的简答题
2025-05-21 09:46:19.415 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 75 要求包含简答题但没有可用的简答题
2025-05-21 09:46:19.417 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 74 没有可用题目
2025-05-21 09:46:19.417 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 75 要求包含简答题但没有可用的简答题
2025-05-21 09:46:19.444 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 78 没有可用题目
2025-05-21 09:46:19.487 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 80 要求包含简答题但没有可用的简答题
2025-05-21 09:46:19.488 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点ID 81 没有可用题目
2025-05-21 09:46:19.507 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 无法按照目标难度分布选择足够题目，返回所有可用题目
2025-05-21 09:46:19.507 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [80, 81, 20, 74, 75, 78]
2025-05-21 11:24:53.257 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:24:53.257 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:24:53.257 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:24:53.257 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:24:54.344 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-21 11:24:54.752 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-21 11:24:55.059 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-05-21 11:25:20.260 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:25:20.261 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:25:20.261 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:25:20.261 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:25:21.314 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-21 11:25:21.720 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-21 11:27:20.688 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点要求包含简答题但没有可用的简答题: [36, 75, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]
2025-05-21 11:27:20.695 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 74 没有题目数量统计信息
2025-05-21 11:27:20.698 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 78 没有题目数量统计信息
2025-05-21 11:27:20.701 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 81 没有题目数量统计信息
2025-05-21 11:27:20.768 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 74 没有可用的题目
2025-05-21 11:27:20.792 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 78 没有可用的题目
2025-05-21 11:27:20.834 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 81 没有可用的题目
2025-05-21 11:27:20.852 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点没有任何可用题目: [74, 78, 81]
2025-05-21 11:27:20.853 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [80, 81, 82, 83, 36, 73, 74, 75, 76, 77, 78, 79]
2025-05-21 11:28:01.150 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 11:28:32.065 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点要求包含简答题但没有可用的简答题: [36, 75, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]
2025-05-21 11:28:32.072 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 74 没有题目数量统计信息
2025-05-21 11:28:32.076 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 78 没有题目数量统计信息
2025-05-21 11:28:32.082 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 81 没有题目数量统计信息
2025-05-21 11:28:32.144 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 74 没有可用的题目
2025-05-21 11:28:32.171 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 78 没有可用的题目
2025-05-21 11:28:32.214 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 知识点 81 没有可用的题目
2025-05-21 11:28:32.233 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点没有任何可用题目: [74, 78, 81]
2025-05-21 11:28:32.233 [http-nio-8081-exec-5] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [80, 81, 82, 83, 36, 73, 74, 75, 76, 77, 78, 79]
2025-05-21 11:38:31.266 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 11:45:49.805 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:45:49.806 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:45:49.806 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:45:49.806 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:45:50.857 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-21 11:45:51.268 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-21 11:45:57.329 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 11:49:23.674 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:49:23.674 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:49:23.674 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:49:23.674 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-21 11:49:24.776 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-21 11:49:25.200 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-21 11:49:33.729 [http-nio-8081-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 11:51:57.568 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 11:52:23.981 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点要求包含简答题但没有可用的简答题: [206, 61, 59, 61, 60, 64, 62, 58, 63, 65, 66, 67, 68]
2025-05-21 11:52:24.249 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [64, 65, 66, 67, 68, 58, 59, 60, 61, 206, 62, 63]
2025-05-21 12:06:21.371 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 12:08:11.788 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点要求包含简答题但没有可用的简答题: [190, 206, 205, 204, 206, 207, 208]
2025-05-21 12:08:11.957 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [208, 204, 205, 190, 206, 207]
2025-05-21 12:17:23.487 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-21 12:18:19.918 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 以下知识点要求包含简答题但没有可用的简答题: [190, 106]
2025-05-21 12:18:20.009 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [106, 190]
