<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maizi EDU - 智慧教育平台</title>
    <script src="/static/js/avatar-fix.js"></script>
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/index.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <a href="/">Maizi EDU</a>
        </div>
        <div class="nav-menu">
            <a href="/main/chat" class="nav-item">出题</a>
            <a href="/topics/upload-topics" class="nav-item">上传</a>
            <a href="/paper/generate" class="nav-item">组卷</a>
            <a href="/paper/check" class="nav-item">查重</a>
            <a href="/topics/bank" class="nav-item">题库</a>
        </div>
        <div class="nav-user">
            <div class="user-info">
                <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                <span class="username">加载中...</span>
            </div>
            <div class="dropdown-menu">
                <a href="/user/profile">个人信息</a>
                <a href="#" id="logout">退出登录</a>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <div class="welcome-section">
            <h1>欢迎使用 Maizi EDU</h1>
            <p>智能教育辅助系统，让教学更轻松</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>智能出题</h3>
                <p>基于AI技术，快速生成高质量试题</p>
            </div>
            <div class="feature-card">
                <h3>试卷上传</h3>
                <p>支持多种格式试卷上传和智能识别</p>
            </div>
            <div class="feature-card">
                <h3>智能组卷</h3>
                <p>根据教学目标自动生成试卷</p>
            </div>
            <div class="feature-card">
                <h3>查重系统</h3>
                <p>快速检测试题重复度</p>
            </div>
        </div>
    </main>

    <!-- 消息提示框 -->
    <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/toast.js"></script>
    <script src="/static/js/index.js"></script>
    <script>
        // 加载用户信息
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (!token) {
                document.querySelector('.username').textContent = '未登录';
                return;
            }
            
            fetch('/api/user/info', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    document.querySelector('.username').textContent = data.data.username;
                    if (data.data.avatar) {
                        document.querySelector('.avatar').src = data.data.avatar;
                    }
                } else {
                    document.querySelector('.username').textContent = '未登录';
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                document.querySelector('.username').textContent = '未登录';
            });
            
            // 注销功能
            document.getElementById('logout').addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('token');
                showToast('已成功退出登录', 'info');
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 1500);
            });
        });
    </script>
</body>
</html> 