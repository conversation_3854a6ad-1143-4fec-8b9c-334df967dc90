-- 创建测试试卷（如果不存在）
-- 执行时间: 2024-05-24

-- 检查是否已存在ID为1的试卷
SET @paper_exists = (
    SELECT COUNT(*)
    FROM papers
    WHERE id = 1 AND is_deleted = 0
);

-- 如果不存在则创建测试试卷
SET @sql = IF(@paper_exists = 0,
    'INSERT INTO papers (
        id,
        title, 
        user_id, 
        knowledge_id, 
        knowledge_name, 
        total_score, 
        actual_total_score, 
        difficulty, 
        content, 
        config, 
        paper_type, 
        file_format, 
        download_count, 
        create_time, 
        update_time, 
        is_deleted
    ) VALUES (
        1,
        "测试试卷", 
        1, 
        1, 
        "测试知识点", 
        100, 
        100, 
        0.5, 
        "1,2,3", 
        "{\"totalScore\":100,\"questionTypes\":{\"choice\":5,\"multiple\":3,\"judge\":2}}", 
        "regular", 
        "pdf", 
        0, 
        NOW(), 
        NOW(), 
        0
    )',
    'SELECT "Test paper with ID 1 already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证试卷是否创建成功
SELECT 
    id,
    title,
    user_id,
    knowledge_name,
    total_score,
    paper_type,
    create_time
FROM papers
WHERE id = 1 AND is_deleted = 0;

-- 显示试卷统计信息
SELECT 
    COUNT(*) as total_papers,
    COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as active_papers,
    COUNT(CASE WHEN is_deleted = 1 THEN 1 END) as deleted_papers
FROM papers;
