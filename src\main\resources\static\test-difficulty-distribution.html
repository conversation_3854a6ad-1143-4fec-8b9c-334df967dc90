<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>难度分布功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-bar mr-2"></i>难度分布功能测试
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="paperId">试卷ID:</label>
                            <input type="number" class="form-control" id="paperId" value="122" placeholder="请输入试卷ID">
                        </div>
                        <button class="btn btn-primary" onclick="testDifficultyDistribution()">
                            <i class="fas fa-test-tube mr-1"></i>测试难度分布API
                        </button>
                        <button class="btn btn-info ml-2" onclick="testPaperList()">
                            <i class="fas fa-list mr-1"></i>获取试卷列表
                        </button>
                        <hr>
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取认证token
        function getAuthToken() {
            return localStorage.getItem('token') || '';
        }

        // 测试难度分布API
        function testDifficultyDistribution() {
            const paperId = document.getElementById('paperId').value;
            if (!paperId) {
                Swal.fire('错误', '请输入试卷ID', 'error');
                return;
            }

            const token = getAuthToken();
            console.log('使用token:', token);

            $.ajax({
                url: `/api/papers/${paperId}/difficulty-distribution`,
                method: 'GET',
                headers: {
                    'Authorization': token ? `Bearer ${token}` : '',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    console.log('API响应:', response);
                    displayResult('success', '成功', response);
                    
                    if (response && response.success && response.data) {
                        showDifficultyModal(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('API错误:', xhr.responseText);
                    displayResult('error', '失败', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                }
            });
        }

        // 测试获取试卷列表
        function testPaperList() {
            const token = getAuthToken();
            
            $.ajax({
                url: '/api/papers?page=0&size=5',
                method: 'GET',
                headers: {
                    'Authorization': token ? `Bearer ${token}` : '',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    console.log('试卷列表响应:', response);
                    displayResult('success', '试卷列表获取成功', response);
                },
                error: function(xhr, status, error) {
                    console.error('试卷列表错误:', xhr.responseText);
                    displayResult('error', '试卷列表获取失败', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    });
                }
            });
        }

        // 显示结果
        function displayResult(type, title, data) {
            const resultDiv = document.getElementById('result');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
            
            resultDiv.innerHTML = `
                <div class="alert ${alertClass}">
                    <h5><i class="fas ${icon} mr-2"></i>${title}</h5>
                    <pre class="mt-2 mb-0" style="background: rgba(0,0,0,0.1); padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }

        // 显示难度分布模态框
        function showDifficultyModal(data) {
            const { easy = [], medium = [], hard = [], paperTitle = '试卷' } = data;
            
            let easyHtml = '';
            let mediumHtml = '';
            let hardHtml = '';

            // 生成简单题目列表
            if (easy.length > 0) {
                easy.forEach((question, index) => {
                    easyHtml += `
                        <div class="question-item mb-2 p-2 border rounded">
                            <span class="badge badge-success mr-2">第${index + 1}题</span>
                            <span class="badge badge-secondary mr-2">${getQuestionTypeText(question.type)}</span>
                            <span class="question-title">${question.title || '题目内容'}</span>
                        </div>
                    `;
                });
            } else {
                easyHtml = '<div class="text-muted text-center py-3">无简单题目</div>';
            }

            // 生成中等题目列表
            if (medium.length > 0) {
                medium.forEach((question, index) => {
                    mediumHtml += `
                        <div class="question-item mb-2 p-2 border rounded">
                            <span class="badge badge-warning mr-2">第${easy.length + index + 1}题</span>
                            <span class="badge badge-secondary mr-2">${getQuestionTypeText(question.type)}</span>
                            <span class="question-title">${question.title || '题目内容'}</span>
                        </div>
                    `;
                });
            } else {
                mediumHtml = '<div class="text-muted text-center py-3">无中等题目</div>';
            }

            // 生成困难题目列表
            if (hard.length > 0) {
                hard.forEach((question, index) => {
                    hardHtml += `
                        <div class="question-item mb-2 p-2 border rounded">
                            <span class="badge badge-danger mr-2">第${easy.length + medium.length + index + 1}题</span>
                            <span class="badge badge-secondary mr-2">${getQuestionTypeText(question.type)}</span>
                            <span class="question-title">${question.title || '题目内容'}</span>
                        </div>
                    `;
                });
            } else {
                hardHtml = '<div class="text-muted text-center py-3">无困难题目</div>';
            }

            const modalHtml = `
                <div class="modal fade" id="testDifficultyModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-info text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-chart-bar mr-2"></i>难度分布 - ${paperTitle}
                                </h5>
                                <button type="button" class="close text-white" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-md-4 text-center">
                                        <div class="difficulty-stat">
                                            <div class="stat-number text-success">${easy.length}</div>
                                            <div class="stat-label">简单题目</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="difficulty-stat">
                                            <div class="stat-number text-warning">${medium.length}</div>
                                            <div class="stat-label">中等题目</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="difficulty-stat">
                                            <div class="stat-number text-danger">${hard.length}</div>
                                            <div class="stat-label">困难题目</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <ul class="nav nav-tabs" id="difficultyTabs" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="easy-tab" data-toggle="tab" href="#easy" role="tab">
                                            <i class="fas fa-smile text-success"></i> 简单 (${easy.length})
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="medium-tab" data-toggle="tab" href="#medium" role="tab">
                                            <i class="fas fa-meh text-warning"></i> 中等 (${medium.length})
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="hard-tab" data-toggle="tab" href="#hard" role="tab">
                                            <i class="fas fa-frown text-danger"></i> 困难 (${hard.length})
                                        </a>
                                    </li>
                                </ul>
                                
                                <div class="tab-content mt-3" id="difficultyTabContent">
                                    <div class="tab-pane fade show active" id="easy" role="tabpanel">
                                        <div class="difficulty-questions" style="max-height: 400px; overflow-y: auto;">
                                            ${easyHtml}
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="medium" role="tabpanel">
                                        <div class="difficulty-questions" style="max-height: 400px; overflow-y: auto;">
                                            ${mediumHtml}
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="hard" role="tabpanel">
                                        <div class="difficulty-questions" style="max-height: 400px; overflow-y: auto;">
                                            ${hardHtml}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                    <i class="fas fa-times mr-1"></i>关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#testDifficultyModal').remove();
            
            // 添加新的模态框到页面
            $('body').append(modalHtml);
            
            // 显示模态框
            $('#testDifficultyModal').modal('show');
        }

        // 获取题目类型文本
        function getQuestionTypeText(type) {
            const typeMap = {
                'choice': '单选题',
                'multiple': '多选题', 
                'judge': '判断题',
                'fill': '填空题',
                'short': '简答题',
                'subjective': '主观题',
                'group': '题组题'
            };
            return typeMap[type] || '未知类型';
        }

        // 页面加载完成后的初始化
        $(document).ready(function() {
            console.log('页面加载完成');
            console.log('当前token:', getAuthToken());
        });
    </script>

    <style>
        .difficulty-stat {
            padding: 15px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.02);
            margin-bottom: 10px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .question-item {
            transition: all 0.2s ease;
            background: #f8f9fa;
        }

        .question-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .question-title {
            font-size: 0.9rem;
            color: #495057;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            vertical-align: middle;
        }
    </style>
</body>
</html>
