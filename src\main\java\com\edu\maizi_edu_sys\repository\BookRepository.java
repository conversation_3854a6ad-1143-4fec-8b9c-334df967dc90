package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.Book;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BookRepository extends JpaRepository<Book, Long> {

    /**
     * Find books by title containing the search keyword
     */
    List<Book> findByTitleContainingIgnoreCase(String title);

    /**
     * Find books by title or type containing the search keyword
     */
    @Query("SELECT b FROM Book b WHERE " +
           "LOWER(b.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(b.type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(b.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Book> searchBooks(@Param("keyword") String keyword);

    /**
     * Alternative search method using native SQL for better performance
     */
    @Query(value = "SELECT * FROM books b WHERE " +
                  "LOWER(b.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
                  "LOWER(b.type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
                  "LOWER(b.description) LIKE LOWER(CONCAT('%', :keyword, '%'))",
           nativeQuery = true)
    List<Book> searchBooksNative(@Param("keyword") String keyword);

    /**
     * Find books by user id
     */
    List<Book> findByUserId(Long userId);

    /**
     * Find books by type
     */
    List<Book> findByType(String type);

    // 添加自定义查询方法
    List<Book> findByTitleContainingOrDescriptionContaining(String titleQuery, String descQuery);
}