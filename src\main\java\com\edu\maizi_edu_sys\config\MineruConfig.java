// com.edu.maizi_edu_sys.config.MineruConfig.java
package com.edu.maizi_edu_sys.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "mineru")
public class MineruConfig {
    private String baseUrl = "https://mineru.net/api/v4";
    private String token;
    private int connectTimeout = 5000;
    private int readTimeout = 10000;
    private int maxRetries = 3;
    private int retryDelay = 1000;
}