package com.edu.maizi_edu_sys.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TopicTypeMapper 统一映射工具测试
 */
public class TopicTypeMapperTest {

    @Test
    @DisplayName("测试前端格式到数据库格式的映射")
    public void testFrontendToDbMapping() {
        // 前端格式映射测试
        assertEquals("choice", TopicTypeMapper.toDbFormat("SINGLE_CHOICE"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("MULTIPLE_CHOICE"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("JUDGE"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("FILL"));
        assertEquals("short", TopicTypeMapper.toDbFormat("SHORT"));
        assertEquals("subjective", TopicTypeMapper.toDbFormat("SUBJECTIVE"));
        assertEquals("group", TopicTypeMapper.toDbFormat("GROUP"));
    }

    @Test
    @DisplayName("测试数据库格式保持不变")
    public void testDbFormatPreservation() {
        // 数据库格式应该保持不变
        assertEquals("choice", TopicTypeMapper.toDbFormat("choice"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("multiple"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("judge"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("fill"));
        assertEquals("short", TopicTypeMapper.toDbFormat("short"));
        assertEquals("subjective", TopicTypeMapper.toDbFormat("subjective"));
        assertEquals("group", TopicTypeMapper.toDbFormat("group"));
    }

    @Test
    @DisplayName("测试数据库格式到前端格式的映射")
    public void testDbToFrontendMapping() {
        assertEquals("SINGLE_CHOICE", TopicTypeMapper.toFrontendFormat("choice"));
        assertEquals("MULTIPLE_CHOICE", TopicTypeMapper.toFrontendFormat("multiple"));
        assertEquals("JUDGE", TopicTypeMapper.toFrontendFormat("judge"));
        assertEquals("FILL", TopicTypeMapper.toFrontendFormat("fill"));
        assertEquals("SHORT", TopicTypeMapper.toFrontendFormat("short"));
        assertEquals("SUBJECTIVE", TopicTypeMapper.toFrontendFormat("subjective"));
        assertEquals("GROUP", TopicTypeMapper.toFrontendFormat("group"));
    }

    @Test
    @DisplayName("测试中文名称映射")
    public void testChineseNameMapping() {
        assertEquals("单选题", TopicTypeMapper.getChineseName("choice"));
        assertEquals("多选题", TopicTypeMapper.getChineseName("multiple"));
        assertEquals("判断题", TopicTypeMapper.getChineseName("judge"));
        assertEquals("填空题", TopicTypeMapper.getChineseName("fill"));
        assertEquals("简答题", TopicTypeMapper.getChineseName("short"));
        assertEquals("主观题", TopicTypeMapper.getChineseName("subjective"));
        assertEquals("组合题", TopicTypeMapper.getChineseName("group"));
    }

    @Test
    @DisplayName("测试其他常见格式映射")
    public void testCommonFormatMapping() {
        // 测试其他常见的题型格式
        assertEquals("choice", TopicTypeMapper.toDbFormat("单选题"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("多选题"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("判断题"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("填空题"));
        assertEquals("short", TopicTypeMapper.toDbFormat("简答题"));
        
        // 测试英文别名
        assertEquals("choice", TopicTypeMapper.toDbFormat("single"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("multi"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("judgment"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("fillblank"));
        assertEquals("short", TopicTypeMapper.toDbFormat("shortanswer"));
    }

    @Test
    @DisplayName("测试题型验证")
    public void testTypeValidation() {
        // 有效题型
        assertTrue(TopicTypeMapper.isValidType("SINGLE_CHOICE"));
        assertTrue(TopicTypeMapper.isValidType("choice"));
        assertTrue(TopicTypeMapper.isValidType("单选题"));
        
        // 无效题型
        assertFalse(TopicTypeMapper.isValidType("invalid_type"));
        assertFalse(TopicTypeMapper.isValidType(""));
        assertFalse(TopicTypeMapper.isValidType(null));
    }

    @Test
    @DisplayName("测试边界情况")
    public void testEdgeCases() {
        // null 值处理
        assertEquals("choice", TopicTypeMapper.toDbFormat(null));
        assertEquals("SINGLE_CHOICE", TopicTypeMapper.toFrontendFormat(null));
        assertEquals("未知题型", TopicTypeMapper.getChineseName(null));
        
        // 空字符串处理
        assertEquals("choice", TopicTypeMapper.toDbFormat(""));
        assertEquals("choice", TopicTypeMapper.toDbFormat("   "));
        
        // 大小写不敏感
        assertEquals("choice", TopicTypeMapper.toDbFormat("CHOICE"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("Choice"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("choice"));
    }

    @Test
    @DisplayName("测试完整的映射循环")
    public void testFullMappingCycle() {
        // 测试前端 -> 数据库 -> 前端的完整循环
        String[] frontendTypes = TopicTypeMapper.getAllFrontendTypes();
        
        for (String frontendType : frontendTypes) {
            String dbType = TopicTypeMapper.toDbFormat(frontendType);
            String backToFrontend = TopicTypeMapper.toFrontendFormat(dbType);
            assertEquals(frontendType, backToFrontend, 
                "映射循环失败: " + frontendType + " -> " + dbType + " -> " + backToFrontend);
        }
    }

    @Test
    @DisplayName("测试所有支持的题型数量")
    public void testAllSupportedTypes() {
        String[] dbTypes = TopicTypeMapper.getAllDbTypes();
        String[] frontendTypes = TopicTypeMapper.getAllFrontendTypes();
        
        assertEquals(7, dbTypes.length, "数据库格式题型数量应该是7");
        assertEquals(7, frontendTypes.length, "前端格式题型数量应该是7");
        
        // 验证每个数据库题型都有对应的前端格式
        for (String dbType : dbTypes) {
            String frontendType = TopicTypeMapper.toFrontendFormat(dbType);
            assertNotNull(frontendType, "数据库题型 " + dbType + " 应该有对应的前端格式");
            assertNotEquals("SINGLE_CHOICE", frontendType, "不应该都映射为默认值");
        }
    }

    @Test
    @DisplayName("验证与现有系统的兼容性")
    public void testSystemCompatibility() {
        // 验证与前端请求格式的兼容性
        assertEquals("judge", TopicTypeMapper.toDbFormat("JUDGE"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("SINGLE_CHOICE"));
        
        // 验证与数据库存储格式的兼容性
        assertEquals("judge", TopicTypeMapper.toDbFormat("judge"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("choice"));
        
        // 验证与遗传算法的兼容性（现在应该都使用数据库格式）
        assertEquals("judge", TopicTypeMapper.toDbFormat("judgment"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("singleChoice"));
    }
}
