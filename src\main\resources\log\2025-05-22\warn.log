2025-05-22 11:22:17.048 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-22 11:22:17.048 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-22 11:22:17.048 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-22 11:22:17.048 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 11:22:18.171 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-22 11:22:18.616 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-22 11:22:46.125 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-22 11:24:12.939 [http-nio-8081-exec-2] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [61, 206]
2025-05-22 11:24:12.939 [http-nio-8081-exec-2] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 计算的总分为0，可能是因为题型分值配置有问题，返回默认值100
2025-05-22 11:24:12.939 [http-nio-8081-exec-2] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 警告: 实际题目总分(100) 与目标总分(50) 不一致
2025-05-22 12:02:58.301 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:02:58.301 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:02:58.301 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:02:58.301 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:02:59.364 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-22 12:02:59.770 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-22 12:03:42.291 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:03:42.292 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:03:42.292 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:03:42.292 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-22 12:03:43.392 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-22 12:03:43.809 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-22 12:03:59.969 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-05-22 12:04:03.717 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-22 12:05:10.072 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [61, 206]
2025-05-22 12:05:10.072 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 计算的总分为0，可能是因为题型分值配置有问题，返回默认值100
2025-05-22 12:05:10.072 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 警告: 实际题目总分(100) 与目标总分(50) 不一致
2025-05-22 12:07:19.140 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-05-22 12:07:43.535 [http-nio-8081-exec-3] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 未能满足简答题要求的知识点: [61, 206]
2025-05-22 12:07:43.535 [http-nio-8081-exec-3] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 计算的总分为0，可能是因为题型分值配置有问题，返回默认值100
2025-05-22 12:07:43.535 [http-nio-8081-exec-3] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 警告: 实际题目总分(100) 与目标总分(50) 不一致
