package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.ChatSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, Long> {
    List<ChatSession> findByUserIdAndDeletedOrderByUpdatedAtDesc(Long userId, Boolean deleted);
} 