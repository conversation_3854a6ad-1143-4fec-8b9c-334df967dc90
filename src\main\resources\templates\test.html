<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>A4知识手册打印模板</title>
    <style>
        /* 基础打印设置 */
        @page {
            size: A4 portrait;
            margin: 25mm 20mm 20mm 25mm;
            @top-left { content: element(pageHeader); }
            @bottom-center { content: counter(page) " / " counter(pages); }
        }

        /* 全局样式 */
        body {
            font-family: "SimSun", "Times New Roman", serif;
            font-size: 10.5pt;
            line-height: 1.25;
            color: #333;
            counter-reset: chapter section figure;
        }

        /* 标题系统 */
        h1 {
            font: bold 16pt "SimHei", sans-serif;
            margin: 24pt 0 12pt;
            padding: 8pt 0;
            border-bottom: 2px solid #666;
            counter-increment: chapter;
            counter-reset: section;
        }
        h1::before {
            content: "第" counter(chapter) "章 ";
            color: #2c5f96;
        }

        h2 {
            font: 14pt "SimHei", sans-serif;
            margin: 18pt 0 9pt;
            padding-left: 2em;
            position: relative;
            counter-increment: section;
        }
        h2::before {
            content: counter(chapter) "." counter(section) " ";
            color: #2c5f96;
            position: absolute;
            left: 0;
        }

        /* 知识点区块 */
        .knowledge-point {
            margin: 12pt 0;
            padding: 10pt;
            background: #f8f9fa;
            border-left: 4px solid #2c5f96;
            page-break-inside: avoid;
        }
        .warning {
            border-color: #dc3545;
            background: #fff3f3;
        }
        .highlight {
            background-color: #e3f4ed;
            padding: 2pt 4pt;
        }

        /* 例题系统 */
        .example {
            margin: 15pt 0;
            page-break-inside: avoid;
        }
        .example-title {
            font: bold 11pt "KaiTi", cursive;
            color: #2c5f96;
            margin-bottom: 8pt;
        }
        .example-title::before {
            content: "📐 例" counter(chapter) "-" counter(section) "-" counter(figure) " ";
            counter-increment: figure;
        }
        .analysis {
            font-family: "KaiTi", cursive;
            padding: 8pt;
            background: #f8f9fa;
            margin: 8pt 0;
        }
        .answer-area {
            border: 1px dashed #999;
            min-height: 50pt;
            margin: 10pt 0;
            position: relative;
        }
        .answer-area::after {
            content: "答题区";
            position: absolute;
            right: 4pt;
            bottom: 2pt;
            font-size: 9pt;
            color: #666;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 12pt 0;
        }
        th {
            background: #e9ecef;
            font-weight: bold;
            padding: 6pt;
            border: 1px solid #dee2e6;
        }
        td {
            padding: 6pt;
            border: 1px solid #dee2e6;
        }

        /* 打印优化 */
        @media print {
            .page-break {
                page-break-before: always;
            }
            .no-print {
                display: none;
            }
        }

        /* 页眉 */
        #pageHeader {
            position: running(pageHeader);
            font-size: 9pt;
            color: #666;
            padding-bottom: 4pt;
            border-bottom: 1px solid #ddd;
        }
    </style>
</head>
<body>
<div id="pageHeader">初中数学知识点精讲 | 第三章：平面几何</div>

<h1>三角形与多边形</h1>

<h2>基本性质</h2>

<div class="knowledge-point">
    <span class="highlight">核心定理</span>：三角形内角和等于180°
    <div class="warning">易错点：钝角三角形的高可能在三角形外部</div>
</div>

<div class="example">
    <div class="example-title">内角计算</div>
    <p>已知△ABC中，∠A=60°，∠B=2∠C，求各角度数</p>
    <div class="analysis">
        解析：设∠C=x°，则∠B=2x°<br>
        根据内角和定理：60 + 2x + x = 180 → x=40<br>
        ∴ ∠A=60°, ∠B=80°, ∠C=40°
    </div>
    <div class="answer-area"></div>
</div>

<h2>面积计算</h2>

<table>
    <tr>
        <th>公式名称</th>
        <th>表达式</th>
        <th>适用条件</th>
    </tr>
    <tr>
        <td>海伦公式</td>
        <td>S=√[p(p-a)(p-b)(p-c)]</td>
        <td>已知三边长度</td>
    </tr>
</table>

<!-- 分页示例 -->
<div class="page-break"></div>

<h1>圆的相关计算</h1>
<!-- 更多内容... -->

</body>
</html>