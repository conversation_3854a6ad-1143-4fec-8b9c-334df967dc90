-- 创建测试题目数据
-- 执行时间: 2024-05-24

-- 首先检查是否存在测试题目
SET @topic_count = (SELECT COUNT(*) FROM topic_bak WHERE id BETWEEN 1 AND 20);

-- 如果题目数量少于10个，则插入测试题目
SET @sql = IF(@topic_count < 10,
    'INSERT INTO topic_bak (id, know_id, type, title, options, answer, parse, score, source, sort, difficulty, created_at) VALUES
    (1, 1, "choice", "Java中哪个关键字用于定义类？", "[{\"key\":\"A\",\"name\":\"class\"},{\"key\":\"B\",\"name\":\"Class\"},{\"key\":\"C\",\"name\":\"define\"},{\"key\":\"D\",\"name\":\"object\"}]", "A", "class是Java中定义类的关键字", 5, "测试题库", 1, 0.3, NOW()),
    (2, 1, "choice", "Java中的继承使用哪个关键字？", "[{\"key\":\"A\",\"name\":\"inherit\"},{\"key\":\"B\",\"name\":\"extends\"},{\"key\":\"C\",\"name\":\"implements\"},{\"key\":\"D\",\"name\":\"super\"}]", "B", "extends关键字用于类的继承", 5, "测试题库", 2, 0.4, NOW()),
    (3, 1, "multiple", "以下哪些是Java的基本数据类型？", "[{\"key\":\"A\",\"name\":\"int\"},{\"key\":\"B\",\"name\":\"String\"},{\"key\":\"C\",\"name\":\"boolean\"},{\"key\":\"D\",\"name\":\"double\"}]", "ACD", "int、boolean、double是基本数据类型，String是引用类型", 8, "测试题库", 3, 0.5, NOW()),
    (4, 1, "multiple", "Java中的访问修饰符包括？", "[{\"key\":\"A\",\"name\":\"public\"},{\"key\":\"B\",\"name\":\"private\"},{\"key\":\"C\",\"name\":\"protected\"},{\"key\":\"D\",\"name\":\"default\"}]", "ABC", "Java有public、private、protected三个显式访问修饰符", 8, "测试题库", 4, 0.4, NOW()),
    (5, 1, "judge", "Java是面向对象的编程语言。", "", "正确", "Java确实是面向对象的编程语言", 3, "测试题库", 5, 0.2, NOW()),
    (6, 1, "judge", "Java中的数组长度是可变的。", "", "错误", "Java中数组长度在创建后是固定的", 3, "测试题库", 6, 0.3, NOW()),
    (7, 1, "fill", "Java中创建对象使用______关键字。", "", "new", "new关键字用于创建对象实例", 4, "测试题库", 7, 0.3, NOW()),
    (8, 1, "fill", "Java中的主方法签名是public static void ______。", "", "main", "main是Java程序的入口方法", 4, "测试题库", 8, 0.2, NOW()),
    (9, 1, "short", "请简述Java中封装的概念和作用。", "", "封装是面向对象编程的基本特征之一，通过将数据和操作数据的方法组合在一起，并对外隐藏内部实现细节，只暴露必要的接口。封装的作用包括：1.保护数据安全；2.提高代码的可维护性；3.降低系统的耦合度。", "封装是OOP的重要概念，需要理解其定义和作用", 10, "测试题库", 9, 0.6, NOW()),
    (10, 1, "short", "解释Java中方法重载和方法重写的区别。", "", "方法重载(Overload)：在同一个类中，方法名相同但参数列表不同的多个方法。方法重写(Override)：子类重新定义父类中已有的方法，方法名、参数列表、返回类型都必须相同。", "重载和重写是Java中重要的概念，需要明确区分", 10, "测试题库", 10, 0.7, NOW()),
    (11, 1, "choice", "计算二次方程 $ax^2 + bx + c = 0$ 的判别式是？", "[{\"key\":\"A\",\"name\":\"$b^2 - 4ac$\"},{\"key\":\"B\",\"name\":\"$b^2 + 4ac$\"},{\"key\":\"C\",\"name\":\"$4ac - b^2$\"},{\"key\":\"D\",\"name\":\"$a^2 + b^2 + c^2$\"}]", "A", "二次方程的判别式为 $\\Delta = b^2 - 4ac$", 5, "数学题库", 11, 0.4, NOW()),
    (12, 1, "fill", "圆的面积公式是 $S = ______$，其中r是半径。", "", "$\\pi r^2$", "圆的面积公式：$S = \\pi r^2$", 4, "数学题库", 12, 0.3, NOW())
    ON DUPLICATE KEY UPDATE
    title = VALUES(title),
    options = VALUES(options),
    answer = VALUES(answer),
    parse = VALUES(parse)',
    'SELECT "Test topics already exist" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新试卷内容，使用存在的题目ID（包含数学公式题目）
UPDATE papers
SET content = "1,2,3,4,5,6,7,8,9,10,11,12",
    config = '{"totalScore":72,"typeScoreMap":{"choice":5,"multiple":8,"judge":3,"fill":4,"short":10},"questionTypes":{"choice":3,"multiple":2,"judge":2,"fill":3,"short":2}}'
WHERE id IN (1, 79, 80);

-- 验证题目是否创建成功
SELECT
    id,
    type,
    LEFT(title, 50) as title_preview,
    answer,
    score,
    difficulty
FROM topic_bak
WHERE id BETWEEN 1 AND 12
ORDER BY id;

-- 显示题目统计信息
SELECT
    type,
    COUNT(*) as count,
    AVG(difficulty) as avg_difficulty
FROM topic_bak
WHERE id BETWEEN 1 AND 12
GROUP BY type
ORDER BY type;
