package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("wm_topic")
public class WmTopic {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("know_id")
    private Integer knowId;

    @TableField("type")
    private String type;

    @TableField("title")
    private String title;

    @TableField("options")
    private String options;

    @TableField("subs")
    private String subs;

    @TableField("answer")
    private String answer;

    @TableField("parse")
    private String parse;

    @TableField("star")
    private double star;  // range in 0.1-0.5

    @TableField("sort")
    private Integer sort = 1;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("score")
    private Integer score;  // 题目分值，根据题型设置
} 