package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class ViewController {

    // 主页/默认重定向到出题页面
    @GetMapping("/")
    public String index() {
        return "redirect:/main/chat";
    }

    // 出题页面
    @GetMapping("/main/chat")
    public String chatPage() {
        return "main/chat";
    }

    // 上传题目页面
    @GetMapping("/topics/upload-topics")
    public String uploadTopics() {
        return "topics/upload-topics";
    }

    // 题库页面路由
    @GetMapping("/topics/bank")
    public String bankPage() {
        return "topics/bank";
    }

    // 组卷页面路由
    @GetMapping("/paper/generate")
    public String generatePage() {
        return "paper/generate";
    }

    //  试卷配置管理页面路由
    @GetMapping("/paper-configs")
    public String paperConfigsPage() {
        return "paper/config-management";
    }

    // 查重页面路由
    @GetMapping("/paper/check")
    public String checkPage() {
        return "paper/check";
    }

    // 用户信息页面路由
    @GetMapping("/user/profile")
    public String profilePage() {
        return "user/profile";
    }

    // 登录页面路由
    @GetMapping("/auth/login")
    public String loginPage() {
        return "auth/login";
    }

    // 旧上传路径重定向
    @GetMapping("/paper/upload")
    public String oldUploadPage() {
        return "redirect:/topics/upload-topics";
    }

    // 处理旧的 HTML 文件请求
    @GetMapping("/login.html")
    public String loginHtml() {
        return "redirect:/auth/login";
    }

    @GetMapping("/chat.html")
    public String chatHtml() {
        return "redirect:/main/chat";
    }

    @GetMapping("/profile.html")
    public String profileHtml() {
        return "redirect:/user/profile";
    }

    @GetMapping("/index.html")
    public String indexHtml() {
        return "index";
    }

    // 认证功能测试页面
    @GetMapping("/test/auth")
    public String authTestPage() {
        return "test/auth-test";
    }

    // 数学公式渲染测试页面
    @GetMapping("/test/math")
    public String mathFormulaTestPage() {
        return "test/math-formula-test";
    }

    // PDF生成测试页面
    @GetMapping("/test/pdf")
    public String pdfTestPage() {
        return "test/pdf-test";
    }

    // 知识点删除功能测试页面
    @GetMapping("/test/knowledge-point-delete")
    public String knowledgePointDeleteTestPage() {
        return "test/knowledge-point-delete-test";
    }
}