package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户认证服务实现类 - 基于JWT Token
 * 从HTTP请求中获取JWT token并解析用户信息
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final JwtUtil jwtUtil;
    private final UserService userService;

    @Override
    public Long getCurrentUserId() {
        try {
            String token = getTokenFromRequest();
            log.debug("获取当前用户ID - token: {}", token != null ? "存在" : "不存在");

            User currentUser = getCurrentUser();
            if (currentUser != null) {
                log.debug("成功获取当前用户ID: {}, 用户名: {}", currentUser.getId(), currentUser.getUsername());
                return currentUser.getId();
            } else {
                log.warn("未能获取当前用户信息 - token验证失败或用户不存在");
                return null;
            }
        } catch (Exception e) {
            log.error("获取当前用户ID失败", e);
            return null;
        }
    }

    @Override
    public boolean isAuthenticated() {
        try {
            String token = getTokenFromRequest();
            return token != null && jwtUtil.validateToken(token);
        } catch (Exception e) {
            log.debug("认证检查失败", e);
            return false;
        }
    }

    @Override
    public String getCurrentUsername() {
        try {
            User currentUser = getCurrentUser();
            return currentUser != null ? currentUser.getUsername() : null;
        } catch (Exception e) {
            log.error("获取当前用户名失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户信息
     * @return 用户实体，如果未认证或用户不存在返回null
     */
    private User getCurrentUser() {
        try {
            String token = getTokenFromRequest();
            if (token == null || !jwtUtil.validateToken(token)) {
                return null;
            }

            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null) {
                return null;
            }

            return userService.getByUsername(username);
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return null;
        }
    }

    /**
     * 从HTTP请求中获取JWT token
     * @return JWT token字符串，如果不存在返回null
     */
    private String getTokenFromRequest() {
        try {
            ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            if (attr == null) {
                return null;
            }

            HttpServletRequest request = attr.getRequest();

            // 1. 从Authorization header获取
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                return authHeader.substring(7);
            }

            // 2. 直接从Authorization header获取（不带Bearer前缀）
            if (authHeader != null && !authHeader.isEmpty()) {
                return authHeader;
            }

            // 3. 从Cookie获取
            if (request.getCookies() != null) {
                for (javax.servlet.http.Cookie cookie : request.getCookies()) {
                    if ("JWT_TOKEN".equals(cookie.getName()) || "Authorization".equals(cookie.getName())) {
                        String value = cookie.getValue();
                        if (value.startsWith("Bearer ")) {
                            return value.substring(7);
                        }
                        return value;
                    }
                }
            }

            // 4. 从查询参数获取（用于下载链接等）
            String tokenParam = request.getParameter("token");
            if (tokenParam != null && !tokenParam.isEmpty()) {
                return tokenParam;
            }

            return null;
        } catch (Exception e) {
            log.debug("从请求中获取token失败", e);
            return null;
        }
    }
}