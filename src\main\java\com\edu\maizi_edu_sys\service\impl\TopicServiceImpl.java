package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.service.TopicService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Validated
public class TopicServiceImpl implements TopicService {

    @Autowired
    private TopicMapper topicMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public void validateAndSaveTopics(List<TopicDTO> topics) {
        List<Topic> topicEntities = new ArrayList<>();
        
        for (TopicDTO dto : topics) {
            validateTopic(dto);
            Topic topic = convertToEntity(dto);
            topicEntities.add(topic);
        }
        
        // 使用MyBatis-Plus的批量插入
        for (Topic topic : topicEntities) {
            topicMapper.insert(topic);
        }
    }

    private void validateTopic(@Valid TopicDTO topic) {
        // 验证选择题必须有选项
        if (("choice".equals(topic.getType()) || "multiple".equals(topic.getType())) 
            && (topic.getOptions() == null || topic.getOptions().isEmpty())) {
            throw new IllegalArgumentException("选择题必须包含选项");
        }

        // 验证判断题答案
        if ("judge".equals(topic.getType()) && topic.getAnswer() != null 
            && !("是".equals(topic.getAnswer()) || "否".equals(topic.getAnswer()))) {
            throw new IllegalArgumentException("判断题答案必须是'是'或'否'");
        }

        // 验证选择题答案格式
        if (("choice".equals(topic.getType()) || "multiple".equals(topic.getType())) 
            && topic.getAnswer() != null) {
            if (!topic.getAnswer().matches("^[A-Z]+$")) {
                throw new IllegalArgumentException("选择题答案必须是大写字母");
            }
            
            // 验证答案顺序
            String sortedAnswer = topic.getAnswer().chars()
                .mapToObj(ch -> String.valueOf((char) ch))
                .sorted()
                .reduce("", String::concat);
            
            if (!topic.getAnswer().equals(sortedAnswer)) {
                throw new IllegalArgumentException("选择题答案必须按字母顺序排列");
            }
        }
    }

    private Topic convertToEntity(TopicDTO dto) {
        Topic topic = new Topic();
        topic.setKnowId(dto.getKnowId());
        topic.setType(dto.getType());
        topic.setTitle(dto.getTitle());
        
        // 设置创建时间
        topic.setCreatedAt(LocalDateTime.now());
        
        // 将选项转换为JSON字符串
        if (dto.getOptions() != null && !dto.getOptions().isEmpty()) {
            try {
                topic.setOptions(objectMapper.writeValueAsString(dto.getOptions()));
            } catch (Exception e) {
                throw new IllegalArgumentException("选项格式转换失败");
            }
        } else {
            // 为没有选项的题型（如判断题、填空题等）设置默认空数组
            topic.setOptions("[]");
        }

        if (topic.getType().equals("group"))
            topic.setSubs(dto.getSubs());
        else
            topic.setSubs("[]");
        topic.setAnswer(dto.getAnswer());
        topic.setParse(dto.getParse());
        topic.setScore(dto.getScore());
        topic.setSource(dto.getSource());
        topic.setSort(dto.getSort());
        topic.setDifficulty(dto.getDifficulty());
        
        return topic;
    }
} 