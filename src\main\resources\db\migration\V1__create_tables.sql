-- 知识点表
CREATE TABLE IF NOT EXISTS `wm_knowledge` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `knowledge_id` int unsigned NOT NULL COMMENT '知识点id',
  `knowledge_name` varchar(255) NOT NULL COMMENT '知识点名',
  `group_name` varchar(255) NOT NULL COMMENT '分类名（如语文、数学）',
  `is_free` tinyint NOT NULL COMMENT '是否免费：0不免费，1免费',
  `sort` tinyint unsigned NOT NULL COMMENT '排序字段',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0=未删除，1=已删除',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识点表';

-- 题目增强数据表
CREATE TABLE IF NOT EXISTS `topic_enhancement_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `topic_id` int NOT NULL COMMENT '题目ID',
  `cognitive_level` varchar(50) DEFAULT NULL COMMENT '认知层次',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_topic_id` (`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题目增强数据表';

-- 试卷表
CREATE TABLE IF NOT EXISTS `papers` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '试卷标题',
  `knowledge_id` int DEFAULT NULL COMMENT '知识点ID',
  `knowledge_name` varchar(255) DEFAULT NULL COMMENT '知识点名称',
  `total_score` int NOT NULL COMMENT '总分（目标分数）',
  `actual_total_score` int DEFAULT NULL COMMENT '实际总分',
  `difficulty` double DEFAULT NULL COMMENT '试卷难度',
  `content` text COMMENT '试卷内容JSON',
  `config` text COMMENT '生成配置JSON',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0=未删除，1=已删除',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷表';