/**
 * Tooltip Event Fix
 * 
 * This script fixes the "Cannot read properties of undefined (reading 'clientX')" error
 * by patching the tooltip event handling in book-search.js
 */

(function() {
    console.log('Tooltip event fix script loaded');
    
    // Store the original showBookTooltip function if it exists
    if (typeof window.showBookTooltip === 'function') {
        window.originalShowBookTooltip = window.showBookTooltip;
    }
    
    // Override the tooltip mouseleave event handler
    function patchTooltipEvents() {
        // First, patch any existing tooltip elements
        document.querySelectorAll('.tooltip-box, .book-tooltip, .tooltip-container').forEach(tooltip => {
            const originalLeaveHandler = tooltip.onmouseleave;
            tooltip.onmouseleave = function(e) {
                tooltip.style.display = 'none';
                tooltip.remove();
            };
        });
        
        // Replace the showBookTooltip function with a fixed version
        window.showBookTooltip = function(element, event) {
            try {
                // Skip if the element doesn't exist
                if (!element) return;
                
                // Store the mouse position from the event
                if (event && typeof event.clientX === 'number') {
                    window.mousePosition = {
                        x: event.clientX,
                        y: event.clientY
                    };
                }
                
                // Extract book info from the element
                let book;
                try {
                    const row = $(element).closest('tr');
                    const bookId = row.attr('id');
                    if (bookId && bookId.startsWith('book-')) {
                        const index = parseInt(bookId.replace('book-', ''), 10);
                        if (!isNaN(index) && window.currentSearchBooks && window.currentSearchBooks[index]) {
                            book = window.currentSearchBooks[index];
                        }
                    }
                } catch (e) {
                    console.warn('Error getting book info:', e);
                }
                
                // If book not found, create minimal book object
                if (!book) {
                    const textContent = element.textContent || '';
                    book = {
                        title: textContent,
                        type: '',
                        description: '书籍信息无法获取'
                    };
                }
                
                // Remove any existing tooltips
                document.querySelectorAll('.tooltip-box, .book-tooltip, .tooltip-container').forEach(tooltip => {
                    tooltip.remove();
                });
                
                // Create tooltip element
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip-box';
                
                // Extract chapter info if available
                let chapterInfo = '';
                if (book.title) {
                    const cnMatch = book.title.match(/第(\d+)章/);
                    if (cnMatch) {
                        chapterInfo = cnMatch[0];
                    } else {
                        const enMatch = book.title.match(/Chapter\s+(\d+)/i);
                        if (enMatch) {
                            chapterInfo = enMatch[0];
                        } else {
                            const sectionMatch = book.title.match(/Section\s+(\d+)/i);
                            if (sectionMatch) {
                                chapterInfo = sectionMatch[0];
                            } else {
                                const partMatch = book.title.match(/Part\s+(\d+)/i);
                                if (partMatch) {
                                    chapterInfo = partMatch[0];
                                }
                            }
                        }
                    }
                }
                
                // Set tooltip content
                tooltip.innerHTML = `
                    <div class="tooltip-title">${book.title || 'Unknown Book'}</div>
                    ${book.type ? `<div class="tooltip-type">类型: ${book.type}</div>` : ''}
                    ${chapterInfo ? `<div class="tooltip-chapter">章节: ${chapterInfo}</div>` : ''}
                    ${book.url ? `<div class="tooltip-url">链接: ${book.url}</div>` : ''}
                    ${book.description
                        ? `<div class="tooltip-description">${book.description}</div>`
                        : '<div class="tooltip-no-desc">暂无简介</div>'}
                `;
                
                // Add to document
                document.body.appendChild(tooltip);
                
                // Position tooltip
                positionTooltipSafely(tooltip, element);
                
                // Show tooltip
                tooltip.style.display = 'block';
                
                // FIXED VERSION: Set up mouse leave event with safe event handling
                element.addEventListener('mouseleave', function onMouseLeave() {
                    setTimeout(() => {
                        // Get current mouse position from global tracking
                        const rect = tooltip.getBoundingClientRect();
                        const currentMousePos = window.getMousePosition ? 
                            window.getMousePosition() : 
                            window.mousePosition || { x: 0, y: 0 };
                        
                        const mouseX = currentMousePos.x;
                        const mouseY = currentMousePos.y;
                        
                        // Check if mouse is over tooltip
                        if (!(mouseX >= rect.left && mouseX <= rect.right &&
                              mouseY >= rect.top && mouseY <= rect.bottom)) {
                            tooltip.style.display = 'none';
                            tooltip.remove();
                        }
                    }, 100);
                    
                    // Remove this event listener
                    element.removeEventListener('mouseleave', onMouseLeave);
                });
                
                // Safe tooltip mouse leave handler
                tooltip.addEventListener('mouseleave', function() {
                    tooltip.style.display = 'none';
                    tooltip.remove();
                });
                
                // Add safety timeout to remove tooltip after 5 seconds
                setTimeout(() => {
                    if (document.body.contains(tooltip)) {
                        tooltip.remove();
                    }
                }, 5000);
                
            } catch (error) {
                console.error('Error in fixed showBookTooltip:', error);
            }
        };
        
        console.log('Patched tooltip event handlers to fix clientX error');
    }
    
    // Safe tooltip positioning
    function positionTooltipSafely(tooltip, element) {
        try {
            const rect = element.getBoundingClientRect();
            const tooltipWidth = 350; // Fixed width from CSS
            
            // Calculate position
            let left = rect.right + 10;
            let top = rect.top;
            
            // Ensure tooltip doesn't go off-screen to the right
            if (left + tooltipWidth > window.innerWidth) {
                left = rect.left - tooltipWidth - 10;
                
                // If tooltip would go off-screen to the left, position below element
                if (left < 0) {
                    left = rect.left;
                    top = rect.bottom + 10;
                }
            }
            
            // Set position using fixed positioning
            tooltip.style.position = 'fixed';
            tooltip.style.top = top + 'px';
            tooltip.style.left = left + 'px';
            tooltip.style.zIndex = '10000'; // Ensure tooltip appears above modals
        } catch (error) {
            console.error('Error positioning tooltip:', error);
            
            // Fallback positioning
            tooltip.style.position = 'fixed';
            tooltip.style.top = '50px';
            tooltip.style.left = '50px';
            tooltip.style.zIndex = '10000';
        }
    }
    
    // Ensure global mouse position tracking is available
    if (typeof window.mousePosition === 'undefined') {
        window.mousePosition = { x: 0, y: 0 };
        
        // Track mouse position globally
        document.addEventListener('mousemove', function(e) {
            window.mousePosition.x = e.clientX;
            window.mousePosition.y = e.clientY;
        }, { passive: true });
    }
    
    // Add global mouse position getter
    if (typeof window.getMousePosition !== 'function') {
        window.getMousePosition = function() {
            return { x: window.mousePosition.x, y: window.mousePosition.y };
        };
    }
    
    // Apply the patch when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', patchTooltipEvents);
    } else {
        patchTooltipEvents();
    }
    
    // Also apply when jQuery is ready
    if (typeof $ !== 'undefined' && typeof $.ready === 'function') {
        $(document).ready(patchTooltipEvents);
    }
    
    console.log('Tooltip event fix script initialized');
})(); 