/**
 * 智能组卷流程介绍 - 交互和动画控制
 */

// 演示动画控制
function startDemoAnimation() {
    console.log('🎬 开始演示动画...');
    
    // 添加演示活动类
    $('.algorithm-flow-container').addClass('demo-active');
    
    // 重置所有步骤
    $('.flow-step').removeClass('step-highlight');
    
    // 按步骤高亮显示
    highlightStep(1, () => {
        setTimeout(() => {
            highlightStep(2, () => {
                setTimeout(() => {
                    animateProgressBars();
                    setTimeout(() => {
                        highlightStep(3, () => {
                            setTimeout(() => {
                                animateOptimization();
                                setTimeout(() => {
                                    highlightStep(4, () => {
                                        setTimeout(() => {
                                            animateQualityCheck();
                                            setTimeout(() => {
                                                highlightStep(5, () => {
                                                    setTimeout(() => {
                                                        showCompletionMessage();
                                                    }, 2000);
                                                });
                                            }, 2000);
                                        });
                                    }, 1000);
                                });
                            }, 3000);
                        });
                    }, 1000);
                });
            }, 2000);
        });
    }, 1000);
}

// 高亮显示指定步骤
function highlightStep(stepNumber, callback) {
    console.log(`✨ 高亮步骤 ${stepNumber}`);
    
    // 移除之前的高亮
    $('.flow-step').removeClass('step-highlight');
    
    // 高亮当前步骤
    $(`.flow-step[data-step="${stepNumber}"]`).addClass('step-highlight');
    
    // 滚动到当前步骤
    const stepElement = $(`.flow-step[data-step="${stepNumber}"]`)[0];
    if (stepElement) {
        stepElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
        });
    }
    
    // 执行回调
    if (callback) {
        callback();
    }
}

// 动画进度条
function animateProgressBars() {
    console.log('📊 动画进度条...');
    
    const progressBars = $('.database-search .progress-bar');
    progressBars.each(function(index) {
        const bar = $(this);
        const targetWidth = bar.css('width');
        
        // 设置CSS变量
        bar[0].style.setProperty('--target-width', targetWidth);
        
        // 重置宽度然后动画
        bar.css('width', '0%');
        setTimeout(() => {
            bar.css('width', targetWidth);
        }, index * 500);
    });
}

// 动画优化过程
function animateOptimization() {
    console.log('🧠 动画优化过程...');
    
    const rounds = $('.ai-optimization .round-item');
    rounds.removeClass('active');
    
    rounds.each(function(index) {
        setTimeout(() => {
            $(this).addClass('active');
            
            // 移除之前的active类
            if (index > 0) {
                rounds.eq(index - 1).removeClass('active');
            }
            
            // 最后一轮保持active
            if (index === rounds.length - 1) {
                setTimeout(() => {
                    $('.optimization-status').addClass('animate__animated animate__bounceIn');
                }, 500);
            }
        }, index * 1000);
    });
}

// 动画质量检验
function animateQualityCheck() {
    console.log('🔍 动画质量检验...');
    
    const checkItems = $('.quality-check .check-item');
    checkItems.css('opacity', '0');
    
    checkItems.each(function(index) {
        setTimeout(() => {
            $(this).css({
                'opacity': '1',
                'transform': 'translateX(0)'
            });
            
            // 添加检查音效（如果需要）
            if (index === checkItems.length - 1) {
                setTimeout(() => {
                    showCheckCompleteMessage();
                }, 500);
            }
        }, index * 300);
    });
}

// 显示检验完成消息
function showCheckCompleteMessage() {
    console.log('✅ 质量检验完成');
    
    // 可以添加一个临时的成功提示
    const message = $('<div class="alert alert-success text-center mt-3">' +
                     '<i class="fas fa-check-circle mr-2"></i>' +
                     '所有质量检验项目通过！' +
                     '</div>');
    
    $('.quality-check .card-body').append(message);
    
    setTimeout(() => {
        message.fadeOut(() => {
            message.remove();
        });
    }, 2000);
}

// 显示完成消息
function showCompletionMessage() {
    console.log('🎉 演示完成');
    
    // 移除演示活动类
    $('.algorithm-flow-container').removeClass('demo-active');
    $('.flow-step').removeClass('step-highlight');
    
    // 显示完成提示
    Swal.fire({
        icon: 'success',
        title: '演示完成！',
        text: '现在您了解了我们智能组卷系统的工作原理',
        confirmButtonText: '开始使用',
        confirmButtonColor: '#667eea',
        timer: 5000,
        timerProgressBar: true
    }).then((result) => {
        if (result.isConfirmed || result.isDismissed) {
            $('#algorithmFlowModal').modal('hide');
        }
    });
}

// 模态框事件监听
$(document).ready(function() {
    // 监听模态框显示事件
    $('#algorithmFlowModal').on('shown.bs.modal', function() {
        console.log('🚀 智能组卷流程介绍模态框已显示');
        
        // 自动开始轻微的动画效果
        setTimeout(() => {
            $('.flow-step').addClass('animate__animated animate__fadeInUp');
        }, 500);
    });
    
    // 监听模态框隐藏事件
    $('#algorithmFlowModal').on('hidden.bs.modal', function() {
        console.log('📴 智能组卷流程介绍模态框已隐藏');
        
        // 重置所有动画状态
        $('.algorithm-flow-container').removeClass('demo-active');
        $('.flow-step').removeClass('step-highlight animate__animated animate__fadeInUp');
        $('.optimization-status').removeClass('animate__animated animate__bounceIn');
    });
});

// 步骤点击事件
$(document).on('click', '.flow-step', function() {
    const stepNumber = $(this).data('step');
    console.log(`👆 用户点击步骤 ${stepNumber}`);
    
    // 高亮点击的步骤
    highlightStep(stepNumber);
    
    // 显示步骤详情（可选）
    showStepDetails(stepNumber);
});

// 显示步骤详情
function showStepDetails(stepNumber) {
    const stepTitles = {
        1: '智能分析您的需求',
        2: '从海量题库中筛选候选题目',
        3: 'AI智能优化组合',
        4: '多维度质量检验',
        5: '完美试卷新鲜出炉'
    };
    
    const stepDescriptions = {
        1: '系统会仔细分析您的所有设置，包括知识点选择、题型要求、难度分布等，确保完全理解您的需求。',
        2: '从庞大的题库中快速找出所有符合条件的题目，为后续的智能选择做好准备。',
        3: '使用先进的AI算法，像进化一样不断优化题目组合，直到找到最完美的搭配。',
        4: '从多个维度检查试卷质量，确保难度合理、知识点覆盖完整、分数分布科学。',
        5: '生成您专属的高质量试卷，支持多种格式下载和实时预览。'
    };
    
    // 可以在这里添加更详细的说明弹窗
    console.log(`📖 步骤 ${stepNumber}: ${stepTitles[stepNumber]}`);
    console.log(`📝 详情: ${stepDescriptions[stepNumber]}`);
}

// 优势卡片悬停效果
$(document).on('mouseenter', '.advantage-card', function() {
    $(this).addClass('animate__animated animate__pulse');
});

$(document).on('mouseleave', '.advantage-card', function() {
    $(this).removeClass('animate__animated animate__pulse');
});

// 可视化卡片点击效果
$(document).on('click', '.visual-card', function() {
    $(this).addClass('animate__animated animate__tada');
    
    setTimeout(() => {
        $(this).removeClass('animate__animated animate__tada');
    }, 1000);
});

// 导出全局函数
window.startDemoAnimation = startDemoAnimation;
window.highlightStep = highlightStep;

// 初始化
$(document).ready(function() {
    // 智能组卷流程介绍模块已加载
    
    // 预加载动画库（如果使用animate.css）
    if (typeof window.animate !== 'undefined') {
        console.log('✨ Animate.css 已加载');
    }
    
    // 检查是否需要自动显示介绍
    const shouldShowIntro = localStorage.getItem('showAlgorithmIntro');
    if (shouldShowIntro === 'true') {
        setTimeout(() => {
            $('#algorithmFlowModal').modal('show');
            localStorage.removeItem('showAlgorithmIntro');
        }, 1000);
    }
});

// 设置自动显示介绍
window.setShowAlgorithmIntro = function() {
    localStorage.setItem('showAlgorithmIntro', 'true');
    console.log('📌 已设置自动显示算法介绍');
};
