package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.PaperConfigDTO;
import com.edu.maizi_edu_sys.dto.PaginationResponse;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 试卷配置服务接口
 */
public interface PaperConfigService {

    /**
     * 保存配置
     */
    PaperConfigDTO saveConfig(PaperConfigDTO configDTO);

    /**
     * 更新配置
     */
    PaperConfigDTO updateConfig(Long id, PaperConfigDTO configDTO);

    /**
     * 删除配置
     */
    void deleteConfig(Long id, Long userId);

    /**
     * 根据ID获取配置
     */
    PaperConfigDTO getConfigById(Long id, Long userId);

    /**
     * 获取用户的所有配置
     */
    List<PaperConfigDTO> getUserConfigs(Long userId);

    /**
     * 分页获取用户配置
     */
    PaginationResponse<PaperConfigDTO> getUserConfigsPaginated(Long userId, int page, int size);

    /**
     * 获取用户配置和公共配置
     */
    List<PaperConfigDTO> getUserAndPublicConfigs(Long userId);

    /**
     * 分页获取用户配置和公共配置
     */
    PaginationResponse<PaperConfigDTO> getUserAndPublicConfigsPaginated(Long userId, int page, int size);

    /**
     * 搜索配置
     */
    List<PaperConfigDTO> searchConfigs(Long userId, String keyword, boolean includePublic);

    /**
     * 获取用户默认配置
     */
    PaperConfigDTO getDefaultConfig(Long userId);

    /**
     * 设置默认配置
     */
    void setDefaultConfig(Long id, Long userId);

    /**
     * 获取最近使用的配置
     */
    List<PaperConfigDTO> getRecentlyUsedConfigs(Long userId, int limit);

    /**
     * 获取最常用的配置
     */
    List<PaperConfigDTO> getMostUsedConfigs(Long userId, int limit);

    /**
     * 使用配置（更新使用次数和最后使用时间）
     */
    void useConfig(Long id, Long userId);

    /**
     * 复制配置
     */
    PaperConfigDTO copyConfig(Long id, Long userId, String newConfigName);

    /**
     * 导出配置到JSON
     */
    Resource exportConfig(Long id, Long userId);

    /**
     * 导出多个配置到JSON
     */
    Resource exportConfigs(List<Long> ids, Long userId);

    /**
     * 从JSON文件导入配置
     */
    List<PaperConfigDTO> importConfigs(MultipartFile file, Long userId);

    /**
     * 验证配置数据
     */
    void validateConfig(PaperConfigDTO configDTO);

    /**
     * 检查配置名称是否存在
     */
    boolean isConfigNameExists(String configName, Long userId, Long excludeId);

    /**
     * 获取配置统计信息
     */
    ConfigStatistics getConfigStatistics(Long userId);

    /**
     * 配置统计信息
     */
    class ConfigStatistics {
        private long totalConfigs;
        private long publicConfigs;
        private long recentlyUsed;
        private PaperConfigDTO mostUsedConfig;
        private PaperConfigDTO latestConfig;

        // 构造函数、getter和setter
        public ConfigStatistics(long totalConfigs, long publicConfigs, long recentlyUsed, 
                              PaperConfigDTO mostUsedConfig, PaperConfigDTO latestConfig) {
            this.totalConfigs = totalConfigs;
            this.publicConfigs = publicConfigs;
            this.recentlyUsed = recentlyUsed;
            this.mostUsedConfig = mostUsedConfig;
            this.latestConfig = latestConfig;
        }

        // Getters and Setters
        public long getTotalConfigs() { return totalConfigs; }
        public void setTotalConfigs(long totalConfigs) { this.totalConfigs = totalConfigs; }

        public long getPublicConfigs() { return publicConfigs; }
        public void setPublicConfigs(long publicConfigs) { this.publicConfigs = publicConfigs; }

        public long getRecentlyUsed() { return recentlyUsed; }
        public void setRecentlyUsed(long recentlyUsed) { this.recentlyUsed = recentlyUsed; }

        public PaperConfigDTO getMostUsedConfig() { return mostUsedConfig; }
        public void setMostUsedConfig(PaperConfigDTO mostUsedConfig) { this.mostUsedConfig = mostUsedConfig; }

        public PaperConfigDTO getLatestConfig() { return latestConfig; }
        public void setLatestConfig(PaperConfigDTO latestConfig) { this.latestConfig = latestConfig; }
    }
}
