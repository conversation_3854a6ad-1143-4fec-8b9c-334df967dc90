package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import javax.persistence.PrePersist;
import java.time.LocalDateTime;

@Data
@TableName("topic_bak")
public class Topic {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("know_id")
    private Integer knowId;

    @TableField("type")
    private String type;

    @TableField("title")
    private String title;

    @TableField("options")
    private String options;

    @TableField("subs")
    private String subs;

    @TableField("answer")
    private String answer;

    @TableField("parse")
    private String parse;

    @TableField("score")
    private Integer score = 3;

    @TableField("source")
    private String source;

    @TableField("sort")
    private Integer sort = 1;

    @TableField("difficulty")
    private Double difficulty;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 获取题目解析（与parse字段相同）
     * @return 题目解析内容
     */
    public String getAnalysis() {
        return this.parse;
    }
    
    /**
     * 设置题目解析（同时设置parse字段）
     * @param analysis 题目解析内容
     */
    public void setAnalysis(String analysis) {
        this.parse = analysis;
    }

    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
    }

    public Topic() {
        this.createdAt = LocalDateTime.now();
    }
} 