2025-05-28 14:40:50.789 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-05-28 14:40:50.894 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 19548 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-28 14:40:50.899 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-28 14:40:52.209 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-28 14:40:52.212 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-28 14:40:52.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 392 ms. Found 5 JPA repository interfaces.
2025-05-28 14:40:52.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-28 14:40:52.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-28 14:40:52.669 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-28 14:40:52.670 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-28 14:40:52.671 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-28 14:40:52.671 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-28 14:40:52.672 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-28 14:40:52.672 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-05-28 14:40:52.861 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:40:52.862 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:40:52.862 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:40:52.863 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:40:52.863 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-28 14:40:55.003 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-28 14:40:55.015 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-28 14:40:55.016 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-28 14:40:55.016 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-28 14:40:55.347 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-28 14:40:55.347 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4392 ms
2025-05-28 14:40:55.760 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-28 14:40:55.875 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-05-28 14:40:56.186 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-28 14:40:56.322 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-28 14:40:56.806 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-28 14:40:56.832 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-28 14:40:57.903 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-28 14:40:57.914 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-28 14:40:57.930 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-28 14:40:57.930 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-28 14:40:57.931 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-28 14:41:01.106 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-28 14:41:01.118 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-28 14:41:01.296 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-28 14:41:02.384 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-28 14:41:02.882 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-28 14:41:03.356 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-28 14:41:03.381 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-28 14:41:04.020 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-28 14:41:04.102 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-28 14:41:04.142 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-28 14:41:04.144 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-28 14:41:04.144 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ae0eb98]]
2025-05-28 14:41:04.145 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-28 14:41:04.160 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 14.004 seconds (JVM running for 15.21)
2025-05-28 14:41:04.183 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-28 14:41:04.186 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-28 14:41:04.186 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-28 14:41:04.186 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-28 14:41:04.186 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - user_id: bigint
2025-05-28 14:41:04.187 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-28 14:41:04.187 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-28 14:41:04.187 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-28 14:41:04.187 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-28 14:41:04.187 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-28 14:41:04.188 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty_distribution: json
2025-05-28 14:41:04.188 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-28 14:41:04.188 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-28 14:41:04.188 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - paper_type: varchar
2025-05-28 14:41:04.188 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - file_format: varchar
2025-05-28 14:41:04.189 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - download_count: int
2025-05-28 14:41:04.189 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - last_download_time: datetime
2025-05-28 14:41:04.189 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-28 14:41:04.189 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-28 14:41:04.189 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: tinyint
2025-05-28 14:41:04.195 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.5, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=1,2,3,4,5,6,7,8,9,10,11,12, config={"totalScore":72,"typeScoreMap":{"choice":5,"multiple":8,"judge":3,"fill":4,"short":10},"questionTypes":{"choice":3,"multiple":2,"judge":2,"fill":3,"short":2}}, paper_type=regular, file_format=pdf, download_count=28, last_download_time=2025-05-24T19:14:30, create_time=2025-05-16T21:40:57, update_time=2025-05-24T19:14:30, is_deleted=false}, {id=2, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.6999999999999966, difficulty_distribution=null, content=103797,103802,103813,103825,103826,103831,103834,103839,103856,103868,103893,103902,103905,103915,103921,103929,103936,103958,103965,103972,103984,103989,103997,104003,104016,104021,104029,104045,104050,104106,104115,104138,104156,104159,104188,104203,104207,104212,104215,104218,104224,104233,104234,104252,104262,104286,104890,104893,104897,104918,104925,104931,104943,104957,104962,104990,104998,105003,105021,105048,105051,105061,105071,105088,105102,105112,105116,105155,105161,105167,105171,105181,105190,105195,105207,105210,105216,105229,105234,105244,105248,105255,105257,105279,105285,105288,105296,105299,105306,105315,105317,106862,106864,106874,107004,107015,107050,107163,107165,107168,107173,107186,107222,107234,107264,107268,107401,107540,107542,107583,107587,107592,107611,107615,107618,107621,107622,107636,107643,107652,107657,107682,107684,107686,107690,107691,107693,107696,107700,107701,107709,107717,107725,107739,107744,107749,107753,107758,107761,107764,107777,107779,107781,107782,107786,107788,107790,107791,107792,107793,107794,107795,107801,107802,107804,107805,107808,107809,107810,107812,107814,107815,107816,107817,107824,107825,107829,107832,107834,107836,107850,107851,107852,107870,107872,107882,107888,107929,107942,107993,108012,108082,108131,108200,108364,108382,108384,108385,108389,108411,108417,108421,108440,108442,108445,108475,108509,108534,108569,108585,108590,108819,108837,108845,108879,108940,109022,109030,109034,109036,109050,109074,109082,109152,109220,109226,109227,109234,109314,109343,109417,109419,109543,109545,109579,109592,109649,109650,109697,109703,109704,109705,109708,109714,109718,109721,109731,109767,109769,109771,110009,110016,110168,110172,110175,110268,110292,110471,110489,110494,110502,110509,110525,110562,110576,110724,110731,110734,110738,110751,110756,110762,110777,110784,110791,110977,111018,111025,111032,111053,111061,111230,111249,111335,111350,111357,111449,111463,111465,111475,111485,111488,111489,111504,111514,111561,111563,111564,111566,111567,111571,111572,111603,111604,111619,111627,111628,111632,111633,111635,111638,111642,111648,111649,111655,111657,111663,111692,111694,111695,111697,111720,111731,111751,111757,111798,111822,111858,112003,112009,112029,112035,112044,112047,112192,112212,112303,112310,112321,112325,112329,112336,112349,112360,112420,112517,112524,112529,112530,112532,112554,112574,112595,112606,112613,112616,112621,112625,112627,112628,112635,112830,112831,113331,113347,113353,113368,113374,113381,113401,113419,113422,113430,113433,113439,113483,113523,113524,113544,113552,113554,113560,113563,113568,113572,113655,113747,113796,113802,113883,113925,113929,113935,113943,113951,113959,113975,113977,113981,113985,114010,114024,114030,114037,114119,114124,114135,114173,115006,115033,115056,115062,115064,115066,115177,115191,115202,115209,115219,115240,115248,115257,115303,115309,115336,115352,115366,115447,115567,115570,115620,115650,115668,115679,115688,115693,115707,115723,115730,115740,115744,115745,115753,115772,115806,115814,115820,115923,115927,115938,115947,115955,115968,116073,116076,116127,116131,116335,116340,116345,116442,116721,116722,116991,116996,116999,117004,117007,117014,117019,117027,117033,117199,117230,117239,117484,117491,117503,117507,117510,117672,117685,117693,117694,117777,117842,117845,117856,117977,117981,118120,118129,118185,118193,118195,118196,118197,118200,118201,118202,118242,118251,118254,119853,119856,119858,119862,119864,119865,123783,123802,123875,123986,131454,131455,131456,131492,131498,131501,131505,131507,131510,131513,131527,131531,131532,131533,131534,131535,131540,131541,131543,131547,131552,131555,131560,131562,131569,131573,131578,131584,131590,131592,131598,131602,131604,131606,131616,131617,131624,131628,131630,131637,131643,131645,131646,131647,131648,131652,131727,131728,131734,131737,131739,131749,131750,131754,131760,131762,131763,131768,131769,131785,131791,131795,131798,131806,131809,131814,131817,131818,131823,131824,131825,131827,131828,131830,131831,133520,160715, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:44:07, update_time=2025-05-16T21:44:07, is_deleted=false}, {id=3, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.5, difficulty_distribution=null, content=126658,126668,126680,126686,126714,126718,126873,126899,126949,126953,126957,126960,126971,126975,126976,126981,126985,126989,126996,126998,127009,127094,127124,127132,127139,127178,127193,127197,127310,127316,127467,127479,127534, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:53:09, update_time=2025-05-16T21:53:09, is_deleted=false}, {id=4, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=105, actual_total_score=null, difficulty=0.7, difficulty_distribution=null, content=126658,126663,126723,126818,126853,126882,126890,126950,126952,126953,126963,126964,126968,126975,126980,126981,126986,127001,127007,127011,127049,127069,127083,127138,127142,127151,127153,127155,127161,127170,127182,127313,127457,127473,127505, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:57:20, update_time=2025-05-16T21:57:20, is_deleted=true}, {id=5, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.6928571428571428, difficulty_distribution=null, content=126641,126680,126718,126728,126762,126840,126845,126873,126890,126907,126914,126934,126959,126968,126975,126976,126984,126985,126991,127001,127011,127083,127111,127139,127148,127153,127157,127166,127174,127178,127501,127513,127532, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:59:54, update_time=2025-05-16T21:59:54, is_deleted=false}]
2025-05-28 14:41:04.329 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 136
2025-05-28 14:41:17.597 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODM0NzQ1NywiZXhwIjoxNzQ4NDMzODU3fQ.Tyyp1nzT-Ad4D8rlJ8q8xc3gmoLzbFAOWSYoFohDDtn_ILyDzZeRD-x5Lqhr5aCJ9U4LCZrMSVCyFKmNpN6oSA] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-28 14:41:17.608 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28 14:41:17.608 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-28 14:41:17.609 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-28 14:41:18.664 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 14:41:18.800 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 14:41:18.804 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 14:41:18.892 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 14:41:19.111 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 14:41:19.279 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 14:41:19.279 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 14:41:19.284 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 14:41:19.293 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 14:44:08.565 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 14:44:08.570 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 14:44:08.575 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 14:44:08.582 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 14:44:08.593 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 14:44:08.596 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 14:44:08.601 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 14:44:08.605 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 14:44:08.713 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 14:44:08.720 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/history, Token: exists
2025-05-28 14:44:08.720 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /main/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 14:44:08.721 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Template path access: /main/avatars/20250508090939_f9f90b54.jpg
2025-05-28 14:44:08.746 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 14:44:08.996 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/32, Token: exists
2025-05-28 14:44:09.150 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/messages/32, Token: exists
2025-05-28 14:44:10.355 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 14:44:10.360 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 14:44:10.364 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 14:44:10.373 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 14:44:10.531 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 14:44:10.883 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 14:44:10.883 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 14:44:10.885 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 14:44:10.886 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 14:44:12.376 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-05-28 14:47:22.723 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-28 14:47:22.723 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ae0eb98]]
2025-05-28 14:47:22.724 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-28 14:47:23.923 [SpringApplicationShutdownHook] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-28 14:47:23.925 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-28 14:47:23.936 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
