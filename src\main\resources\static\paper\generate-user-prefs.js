/**
 * 试卷生成用户偏好设置
 * 保存和恢复用户的试卷生成配置
 */

// 全局函数定义
window.updateDifficultyChart = function() {
    if (window.difficultyChart) {
        const easy = parseInt($('#easyPercentage').val()) || 0;
        const medium = parseInt($('#mediumPercentage').val()) || 0;
        const hard = parseInt($('#hardPercentage').val()) || 0;

        window.difficultyChart.data.datasets[0].data = [easy, medium, hard];
        window.difficultyChart.update();

        // 验证总和是否为100%
        const total = easy + medium + hard;
        if (total !== 100) {
            $('#difficultyWarning').show();
        } else {
            $('#difficultyWarning').hide();
        }
    }
};

window.updateQuestionDistributionChart = function() {
    if (window.questionDistributionChart) {
        const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
        const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
        const judgment = parseInt($('#judgmentCount').val()) || 0;
        const fillBlank = parseInt($('#fillCount').val()) || 0;
        const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

        window.questionDistributionChart.data.datasets[0].data = [
            singleChoice, multipleChoice, judgment, fillBlank, shortAnswer
        ];
        window.questionDistributionChart.update();
    }
};

window.calculateTotalScore = function() {
    const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
    const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
    const judgment = parseInt($('#judgmentCount').val()) || 0;
    const fillBlank = parseInt($('#fillCount').val()) || 0;
    const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

    const singleChoiceScore = parseFloat($('#singleChoiceScore').val()) || 0;
    const multipleChoiceScore = parseFloat($('#multipleChoiceScore').val()) || 0;
    const judgmentScore = parseFloat($('#judgmentScore').val()) || 0;
    const fillBlankScore = parseFloat($('#fillScore').val()) || 0;
    const shortAnswerScore = parseFloat($('#shortAnswerScore').val()) || 0;

    const totalScore =
        singleChoice * singleChoiceScore +
        multipleChoice * multipleChoiceScore +
        judgment * judgmentScore +
        fillBlank * fillBlankScore +
        shortAnswer * shortAnswerScore;

    const totalQuestions = singleChoice + multipleChoice + judgment + fillBlank + shortAnswer;

    $('#totalScore').text(totalScore.toFixed(1));
    $('#totalQuestions').text(totalQuestions);
    $('#previewTotalScore').text(totalScore.toFixed(1));
    $('#previewTotalQuestions').text(totalQuestions);
};

window.updatePaperPreview = function() {
    const paperTitle = $('#paperTitle').val() || '自定义试卷';
    $('#previewTitle').text(paperTitle);

    const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
    const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
    const judgment = parseInt($('#judgmentCount').val()) || 0;
    const fillBlank = parseInt($('#fillCount').val()) || 0;
    const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

    let previewHtml = '';

    // 单选题
    if (singleChoice > 0) {
        previewHtml += `
            <div class="question-section">
                <h4>一、单选题（每题${$('#singleChoiceScore').val()}分，共${singleChoice}题）</h4>
                ${generateQuestionItems('single', singleChoice)}
            </div>
        `;
    }

    // 多选题
    if (multipleChoice > 0) {
        previewHtml += `
            <div class="question-section">
                <h4>二、多选题（每题${$('#multipleChoiceScore').val()}分，共${multipleChoice}题）</h4>
                ${generateQuestionItems('multiple', multipleChoice)}
            </div>
        `;
    }

    // 判断题
    if (judgment > 0) {
        previewHtml += `
            <div class="question-section">
                <h4>三、判断题（每题${$('#judgmentScore').val()}分，共${judgment}题）</h4>
                ${generateQuestionItems('judgment', judgment)}
            </div>
        `;
    }

    // 填空题
    if (fillBlank > 0) {
        previewHtml += `
            <div class="question-section">
                <h4>四、填空题（每题${$('#fillScore').val()}分，共${fillBlank}题）</h4>
                ${generateQuestionItems('fill', fillBlank)}
            </div>
        `;
    }

    // 简答题
    if (shortAnswer > 0) {
        previewHtml += `
            <div class="question-section">
                <h4>五、简答题（每题${$('#shortAnswerScore').val()}分，共${shortAnswer}题）</h4>
                ${generateQuestionItems('short', shortAnswer)}
            </div>
        `;
    }

    $('#paperPreviewContent').html(previewHtml);

    // 渲染数学公式
    if (window.renderKaTeX) {
        renderKaTeX(document.getElementById('paperPreviewContent'));
    }
};

// 生成题目项
function generateQuestionItems(type, count) {
    let html = '';

    for (let i = 1; i <= Math.min(count, 3); i++) {
        html += `<div class="question-item">`;

        if (type === 'single' || type === 'multiple') {
            html += `
                <div class="question-content">
                    ${i}. 这是一个${type === 'single' ? '单' : '多'}选题示例，可能包含数学公式 $f(x) = \\int_{-\\infty}^{\\infty} \\hat{f}(\\xi)\\,e^{2 \\pi i \\xi x} \\,d\\xi$
                </div>
                <div class="options">
                    <div class="option">A. 选项A</div>
                    <div class="option">B. 选项B</div>
                    <div class="option">C. 选项C</div>
                    <div class="option">D. 选项D</div>
                </div>
            `;
        } else if (type === 'judgment') {
            html += `
                <div class="question-content">
                    ${i}. 这是一个判断题示例，可能包含数学公式 $E = mc^2$
                </div>
            `;
        } else if (type === 'fill') {
            html += `
                <div class="question-content">
                    ${i}. 这是一个填空题示例，可能包含数学公式 $\\frac{d}{dx}\\left( \\int_{0}^{x} f(t)\\, dt\\right)=f(x)$
                </div>
            `;
        } else if (type === 'short') {
            html += `
                <div class="question-content">
                    ${i}. 这是一个简答题示例，可能包含数学公式 $$\\sum_{i=0}^{n} i = \\frac{n(n+1)}{2}$$
                </div>
            `;
        }

        html += `</div>`;
    }

    if (count > 3) {
        html += `<div class="text-muted">（预览仅显示前3题，实际生成将包含全部${count}题）</div>`;
    }

    return html;
}

$(document).ready(function() {
    // 初始化用户偏好
    initUserPreferences();

    // 监听表单变化，保存用户偏好
    $('#generatePaperForm input, #generatePaperForm select').on('change', function() {
        saveUserPreferences();
    });

    // 初始化用户偏好
    function initUserPreferences() {
        try {
            // 从localStorage加载用户偏好
            const savedPrefs = localStorage.getItem('paperGenerationPrefs');
            if (savedPrefs) {
                const prefs = JSON.parse(savedPrefs);

                // 恢复题型数量和分值
                if (prefs.questionCounts) {
                    $('#singleChoiceCount').val(prefs.questionCounts.singleChoice || 5);
                    $('#multipleChoiceCount').val(prefs.questionCounts.multipleChoice || 3);
                    $('#judgmentCount').val(prefs.questionCounts.judgment || 5);
                    $('#fillCount').val(prefs.questionCounts.fillBlank || 3);
                    $('#shortAnswerCount').val(prefs.questionCounts.shortAnswer || 2);
                }

                if (prefs.questionScores) {
                    $('#singleChoiceScore').val(prefs.questionScores.singleChoice || 3);
                    $('#multipleChoiceScore').val(prefs.questionScores.multipleChoice || 3);
                    $('#judgmentScore').val(prefs.questionScores.judgment || 2);
                    $('#fillScore').val(prefs.questionScores.fillBlank || 3);
                    $('#shortAnswerScore').val(prefs.questionScores.shortAnswer || 5);
                }

                // 恢复难度分布
                if (prefs.difficultyDistribution) {
                    $('#easyPercentage').val(prefs.difficultyDistribution.easy || 30);
                    $('#mediumPercentage').val(prefs.difficultyDistribution.medium || 50);
                    $('#hardPercentage').val(prefs.difficultyDistribution.hard || 20);
                }

                // 触发更新事件
                $('#singleChoiceCount').trigger('change');
                $('#easyPercentage').trigger('change');
            }
        } catch (e) {
            console.error('加载用户偏好设置失败:', e);
        }
    }

    // 保存用户偏好
    function saveUserPreferences() {
        try {
            // 收集当前设置
            const prefs = {
                questionCounts: {
                    singleChoice: parseInt($('#singleChoiceCount').val()) || 5,
                    multipleChoice: parseInt($('#multipleChoiceCount').val()) || 3,
                    judgment: parseInt($('#judgmentCount').val()) || 5,
                    fillBlank: parseInt($('#fillCount').val()) || 3,
                    shortAnswer: parseInt($('#shortAnswerCount').val()) || 2
                },
                questionScores: {
                    singleChoice: parseFloat($('#singleChoiceScore').val()) || 3,
                    multipleChoice: parseFloat($('#multipleChoiceScore').val()) || 3,
                    judgment: parseFloat($('#judgmentScore').val()) || 2,
                    fillBlank: parseFloat($('#fillScore').val()) || 3,
                    shortAnswer: parseFloat($('#shortAnswerScore').val()) || 5
                },
                difficultyDistribution: {
                    easy: parseInt($('#easyPercentage').val()) || 30,
                    medium: parseInt($('#mediumPercentage').val()) || 50,
                    hard: parseInt($('#hardPercentage').val()) || 20
                }
            };

            // 保存到localStorage
            localStorage.setItem('paperGenerationPrefs', JSON.stringify(prefs));
        } catch (e) {
            console.error('保存用户偏好设置失败:', e);
        }
    }

    // 绑定事件
    $('#paperTitle').on('input', window.updatePaperPreview);
    $('.question-count, .question-score').on('change', function() {
        window.calculateTotalScore();
        window.updateQuestionDistributionChart();
        window.updatePaperPreview();
    });

    $('#easyPercentage, #mediumPercentage, #hardPercentage').on('change', window.updateDifficultyChart);

    // 切换到预览选项卡时更新预览
    $('#preview-tab').on('shown.bs.tab', window.updatePaperPreview);
});
