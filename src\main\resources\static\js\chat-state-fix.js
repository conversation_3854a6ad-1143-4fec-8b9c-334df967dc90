/**
 * 修复ChatState状态管理问题
 */
(function(window) {
    // 等待DOM和其他JS加载完成
    $(document).ready(function() {
        
        // 确保UnifiedChatState存在
        if (!window.UnifiedChatState) {
            console.error('[ChatStateFix] UnifiedChatState对象不存在!');
            return;
        }
        
        // 添加或覆盖必要的方法
        const chatStateExtensions = {
            // 保存当前消息输入状态
            saveCurrentMessageInputState: function() {
                // 保存当前输入框的内容
                const messageInputValue = $('#messageInput').val() || '';
                const currentChatId = this.getCurrentChatId();
                
                if (currentChatId) {
                    // 将当前输入存储到会话状态
                    this.state.inputState = this.state.inputState || {};
                    this.state.inputState[currentChatId] = messageInputValue;
                    this.saveState();
                    console.log('[ChatState] 已保存聊天输入状态:', currentChatId);
                }
            },
            
            // 恢复消息输入状态
            restoreMessageInputState: function() {
                const currentChatId = this.getCurrentChatId();
                if (!currentChatId) return;
                
                // 从状态中获取存储的输入
                const savedInput = this.state.inputState && this.state.inputState[currentChatId];
                
                // 将内容设置到输入框
                $('#messageInput').val(savedInput || '');
                console.log('[ChatState] 已恢复聊天输入状态:', currentChatId);
            },
            
            // 设置当前聊天ID (如果需要)
            setCurrentChatId: function(chatId) {
                if (!this.state.currentChat) this.state.currentChat = {};
                this.state.currentChat.id = chatId;
                this.saveState();
                console.log('[ChatState] 设置当前聊天ID:', chatId);
            }
        };
        
        // 扩展UnifiedChatState对象
        $.extend(window.UnifiedChatState, chatStateExtensions);
    });
})(window); 