/*  模态框滚动问题修复样式 */

/* 确保body在模态框显示时仍然可以滚动 */
body.modal-open {
    overflow: auto !important;
    padding-right: 0px !important;
    margin-right: 0px !important;
}

/* 确保容器在模态框显示时仍然可以滚动 */
.container-fluid {
    overflow: visible !important;
    height: auto !important;
    min-height: 100vh;
}

/* 模态框本身的滚动设置 */
.modal {
    overflow-x: hidden;
    overflow-y: auto;
}

/* 模态框对话框的滚动设置 */
.modal-dialog {
    margin: 1.75rem auto;
    max-height: calc(100vh - 3.5rem);
    overflow-y: auto;
}

/* 模态框内容的滚动设置 */
.modal-content {
    max-height: calc(100vh - 3.5rem);
    overflow-y: auto;
}

/* 模态框主体的滚动设置 */
.modal-body {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
}

/* 确保模态框背景不影响页面滚动 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}

/* 版本说明模态框特殊处理 */
#versionHelpModal .modal-body,
#defaultVersionModal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
}

/* 试卷生成模态框特殊处理 */
#paperGenerationModal .modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

/* 试卷预览区域的滚动 */
#realTimePreviewContainer {
    max-height: 700px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 知识点配置区域的滚动 */
#knowledgePointsConfigContainer {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 选项卡内容的滚动 */
.tab-content {
    overflow: visible;
}

.tab-pane {
    overflow: visible;
}

/* 确保表格在模态框中正常显示 */
.modal .table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

/* 移动设备优化 */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
    }
    
    .modal-content {
        max-height: calc(100vh - 1rem);
    }
    
    .modal-body {
        max-height: calc(100vh - 150px);
        padding: 1rem 0.75rem;
    }
    
    #versionHelpModal .modal-body,
    #defaultVersionModal .modal-body {
        max-height: 70vh;
    }
    
    #paperGenerationModal .modal-body {
        max-height: 85vh;
    }
}

/* 强制滚动条样式 */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 确保页面内容在模态框显示时不被遮挡 */
.page-content {
    position: relative;
    z-index: 1;
}

/* 修复可能的定位问题 */
.modal.show {
    display: block !important;
}

.modal.fade.show {
    opacity: 1;
}

/* 确保模态框关闭后页面状态正常 */
.modal.fade:not(.show) {
    display: none !important;
}

/* 防止模态框影响页面布局 */
.modal-open .container-fluid {
    filter: none !important;
    transform: none !important;
}

/* 确保背景可以正常滚动 */
.modal-open .navbar,
.modal-open .page-header,
.modal-open .card {
    position: relative;
    z-index: auto;
}

/* 调试样式 - 开发时可用 */
.debug-scroll {
    border: 2px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

.debug-overflow {
    overflow: visible !important;
    border: 2px solid blue !important;
    background-color: rgba(0, 0, 255, 0.1) !important;
}

/* 紧急修复类 - JavaScript可以添加这些类来强制修复 */
.force-scroll {
    overflow: auto !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

.force-no-modal-open {
    padding-right: 0px !important;
    margin-right: 0px !important;
    overflow: auto !important;
}

.force-visible {
    overflow: visible !important;
    height: auto !important;
    min-height: 100vh !important;
}
