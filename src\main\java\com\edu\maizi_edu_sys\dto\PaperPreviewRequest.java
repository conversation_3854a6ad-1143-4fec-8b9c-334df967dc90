package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 试卷预览请求DTO
 * 用于实时预览试卷内容
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperPreviewRequest {
    
    /**
     * 试卷标题
     */
    private String title;

    /**
     * 题型数量映射
     * 例如：{"SINGLE_CHOICE": 10, "MULTIPLE_CHOICE": 5, "JUDGMENT": 5}
     */
    @NotNull(message = "题型数量映射不能为空")
    private Map<String, Integer> typeCountMap;

    /**
     * 题型分值映射
     * 例如：{"SINGLE_CHOICE": 3, "MULTIPLE_CHOICE": 4, "JUDGMENT": 2}
     */
    @NotNull(message = "题型分值映射不能为空")
    private Map<String, Integer> typeScoreMap;

    /**
     * 知识点配置列表
     * 用于从指定知识点中选择题目
     */
    @NotEmpty(message = "知识点配置不能为空")
    @Valid
    private List<KnowledgePointConfigRequest> knowledgePointConfigs;

    /**
     * 难度分布（可选）
     * 例如：{"easy": 0.3, "medium": 0.5, "hard": 0.2}
     */
    private Map<String, Double> difficultyDistribution;

    /**
     * 预览题目数量限制（每种题型最多预览几道题）
     * 默认每种题型预览3道题
     */
    private Integer previewLimit = 3;
}
