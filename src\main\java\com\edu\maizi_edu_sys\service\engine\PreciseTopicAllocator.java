package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 精确题型分配器
 * 负责根据用户精确配置分配题目，确保100%满足用户需求
 */
@Component
@Slf4j
public class PreciseTopicAllocator {

    /**
     * 根据知识点配置精确分配题目
     *
     * @param availableTopics 可用题目列表
     * @param knowledgePointConfigs 知识点配置列表
     * @return 分配结果
     */
    public AllocationResult allocateTopics(List<Topic> availableTopics,
                                         List<KnowledgePointConfigRequest> knowledgePointConfigs) {

        if (availableTopics == null || availableTopics.isEmpty()) {
            return new AllocationResult(false, "没有可用题目", Collections.emptyList(), Collections.emptyList());
        }

        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return new AllocationResult(false, "没有知识点配置", Collections.emptyList(), Collections.emptyList());
        }

        // 按知识点分组题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = availableTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        List<Topic> allocatedTopics = new ArrayList<>();
        List<String> allocationMessages = new ArrayList<>();
        boolean allocationSuccess = true;

        // 为每个知识点分配题目
        for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
            Integer knowledgeId = config.getKnowledgeId().intValue();
            List<Topic> kpTopics = topicsByKnowledgePoint.getOrDefault(knowledgeId, Collections.emptyList());

            AllocationResult kpResult = allocateTopicsForKnowledgePoint(config, kpTopics);

            if (kpResult.isSuccess()) {
                allocatedTopics.addAll(kpResult.getAllocatedTopics());
                allocationMessages.addAll(kpResult.getMessages());
            } else {
                allocationSuccess = false;
                allocationMessages.add(String.format("知识点%d分配失败: %s", knowledgeId,
                                                   String.join("; ", kpResult.getMessages())));
            }
        }

        return new AllocationResult(allocationSuccess,
                                  allocationSuccess ? "所有知识点分配成功" : "部分知识点分配失败",
                                  allocatedTopics, allocationMessages);
    }

    /**
     * 为单个知识点分配题目
     */
    private AllocationResult allocateTopicsForKnowledgePoint(KnowledgePointConfigRequest config,
                                                           List<Topic> availableTopics) {

        Integer knowledgeId = config.getKnowledgeId().intValue();
        List<Topic> allocatedTopics = new ArrayList<>();
        List<String> messages = new ArrayList<>();

        // 按题型分组可用题目
        Map<String, List<Topic>> topicsByType = availableTopics.stream()
            .collect(Collectors.groupingBy(topic -> normalizeTopicType(topic.getType())));

        // 检查是否有简答题配置
        if (!config.hasShortAnswerConfiguration()) {
            messages.add(String.format("知识点%d没有简答题配置", knowledgeId));
            return new AllocationResult(true, "无需分配简答题", allocatedTopics, messages);
        }

        // 只分配简答题（独立计算）
        if (config.getShortAnswerCount() > 0) {
            AllocationResult shortResult = allocateShortAnswerQuestions(knowledgeId,
                                                                       config.getShortAnswerCount(),
                                                                       topicsByType.getOrDefault("short", Collections.emptyList()),
                                                                       allocatedTopics);

            if (!shortResult.isSuccess()) {
                messages.add(String.format("知识点%d简答题分配失败: %s", knowledgeId,
                                         String.join("; ", shortResult.getMessages())));
                return new AllocationResult(false, "简答题分配失败", allocatedTopics, messages);
            }

            allocatedTopics.addAll(shortResult.getAllocatedTopics());
            messages.addAll(shortResult.getMessages());
        }

        return new AllocationResult(true, String.format("知识点%d分配成功", knowledgeId),
                                  allocatedTopics, messages);
    }



    /**
     * 分配简答题（独立计算，不与其他题目冲突）
     */
    private AllocationResult allocateShortAnswerQuestions(Integer knowledgeId,
                                                        int requiredCount,
                                                        List<Topic> availableShortAnswers,
                                                        List<Topic> alreadyAllocated) {

        List<Topic> allocatedTopics = new ArrayList<>();
        List<String> messages = new ArrayList<>();

        if (requiredCount <= 0) {
            return new AllocationResult(true, "无需分配简答题", allocatedTopics, messages);
        }

        // 过滤掉已经分配的题目
        Set<Integer> allocatedIds = alreadyAllocated.stream()
            .map(Topic::getId)
            .collect(Collectors.toSet());

        List<Topic> availableTopics = availableShortAnswers.stream()
            .filter(topic -> !allocatedIds.contains(topic.getId()))
            .collect(Collectors.toList());

        if (availableTopics.size() < requiredCount) {
            messages.add(String.format("知识点%d的简答题不足：需要%d道，可用%d道",
                                     knowledgeId, requiredCount, availableTopics.size()));
            return new AllocationResult(false, "简答题数量不足", allocatedTopics, messages);
        }

        // 精确选择所需数量的简答题
        List<Topic> selectedTopics = selectTopicsWithDiversity(availableTopics, requiredCount);
        allocatedTopics.addAll(selectedTopics);

        messages.add(String.format("知识点%d成功分配简答题%d道（额外）", knowledgeId, selectedTopics.size()));

        return new AllocationResult(true, "简答题分配成功", allocatedTopics, messages);
    }

    /**
     * 带多样性的题目选择
     */
    private List<Topic> selectTopicsWithDiversity(List<Topic> availableTopics, int count) {
        if (availableTopics.size() <= count) {
            return new ArrayList<>(availableTopics);
        }

        // 按难度分组，确保多样性
        Map<String, List<Topic>> topicsByDifficulty = availableTopics.stream()
            .collect(Collectors.groupingBy(topic -> getDifficultyName(topic.getDifficulty())));

        List<Topic> selectedTopics = new ArrayList<>();
        List<String> difficulties = Arrays.asList("easy", "medium", "hard");

        // 轮流从不同难度中选择题目
        int selectedCount = 0;
        int difficultyIndex = 0;

        while (selectedCount < count) {
            String difficulty = difficulties.get(difficultyIndex % difficulties.size());
            List<Topic> difficultyTopics = topicsByDifficulty.getOrDefault(difficulty, Collections.emptyList());

            // 从该难度中选择一个未选择的题目
            Optional<Topic> unselectedTopic = difficultyTopics.stream()
                .filter(topic -> !selectedTopics.contains(topic))
                .findFirst();

            if (unselectedTopic.isPresent()) {
                selectedTopics.add(unselectedTopic.get());
                selectedCount++;
            }

            difficultyIndex++;

            // 如果所有难度都遍历过但还没选够，随机选择剩余的
            if (difficultyIndex >= difficulties.size() * 3 && selectedCount < count) {
                List<Topic> remainingTopics = availableTopics.stream()
                    .filter(topic -> !selectedTopics.contains(topic))
                    .collect(Collectors.toList());

                Collections.shuffle(remainingTopics);
                int needed = count - selectedCount;
                selectedTopics.addAll(remainingTopics.subList(0, Math.min(needed, remainingTopics.size())));
                break;
            }
        }

        return selectedTopics;
    }

    /**
     * 标准化题型名称
     */
    private String normalizeTopicType(String type) {
        if (type == null) return "unknown";

        switch (type.toLowerCase()) {
            case "choice":
            case "single_choice":
            case "singlechoice":
                return "choice";
            case "multiple":
            case "multiple_choice":
            case "multiplechoice":
                return "multiple";
            case "judge":
            case "judgment":
                return "judge";
            case "fill":
            case "fill_blank":
            case "fillblank":
                return "fill";
            case "short":
            case "short_answer":
            case "shortanswer":
                return "short";
            default:
                return type.toLowerCase();
        }
    }

    /**
     * 获取题型显示名称
     */
    private String getTypeDisplayName(String type) {
        switch (type) {
            case "choice": return "单选";
            case "multiple": return "多选";
            case "judge": return "判断";
            case "fill": return "填空";
            case "short": return "简答";
            default: return type;
        }
    }

    /**
     * 获取难度名称
     */
    private String getDifficultyName(Double difficultyValue) {
        if (difficultyValue == null) {
            return "medium";
        }

        if (difficultyValue <= 0.2) return "easy";
        if (difficultyValue <= 0.4) return "medium";
        return "hard";
    }

    /**
     * 分配结果类
     */
    public static class AllocationResult {
        private final boolean success;
        private final String summary;
        private final List<Topic> allocatedTopics;
        private final List<String> messages;

        public AllocationResult(boolean success, String summary, List<Topic> allocatedTopics, List<String> messages) {
            this.success = success;
            this.summary = summary;
            this.allocatedTopics = allocatedTopics != null ? allocatedTopics : Collections.emptyList();
            this.messages = messages != null ? messages : Collections.emptyList();
        }

        public boolean isSuccess() {
            return success;
        }

        public String getSummary() {
            return summary;
        }

        public List<Topic> getAllocatedTopics() {
            return allocatedTopics;
        }

        public List<String> getMessages() {
            return messages;
        }

        @Override
        public String toString() {
            return String.format("AllocationResult{success=%s, summary='%s', topics=%d, messages=%s}",
                               success, summary, allocatedTopics.size(), messages);
        }
    }
}
