package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 增强版遗传算法测试
 * 测试新增的修复算子、可行解检测、启发式种子生成等功能
 */
@ExtendWith(MockitoExtension.class)
public class EnhancedGeneticSolverTest {

    @InjectMocks
    private GeneticSolver geneticSolver;

    @Mock
    private RepairOperator repairOperator;

    @Mock
    private FeasibilityChecker feasibilityChecker;

    @Mock
    private GreedySeedGenerator greedySeedGenerator;

    private List<Topic> testTopics;
    private Map<String, Integer> typeTargetCounts;
    private Map<String, Double> difficultyDistribution;
    private Map<Integer, TopicEnhancementData> enhancementDataMap;

    @BeforeEach
    void setUp() {
        // 设置遗传算法参数
        ReflectionTestUtils.setField(geneticSolver, "POPULATION_SIZE", 50);
        ReflectionTestUtils.setField(geneticSolver, "MAX_GENERATIONS", 30);
        ReflectionTestUtils.setField(geneticSolver, "MIN_GENERATIONS", 10);
        ReflectionTestUtils.setField(geneticSolver, "CROSSOVER_RATE", 0.8);
        ReflectionTestUtils.setField(geneticSolver, "MUTATION_RATE", 0.1);
        ReflectionTestUtils.setField(geneticSolver, "TOURNAMENT_SIZE", 5);
        ReflectionTestUtils.setField(geneticSolver, "EARLY_TERMINATE_THRESHOLD", 0.97);
        ReflectionTestUtils.setField(geneticSolver, "GLOBAL_TIMEOUT_SECONDS", 2);

        // 设置权重
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_SCORE", 0.4);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_QUALITY", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_DIFFICULTY_DIST", 0.15);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_COGNITIVE_DIST", 0.15);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_KP_COVERAGE", 0.05);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_TOPIC_TYPE_DIVERSITY", 0.05);

        // 设置自适应变异参数
        ReflectionTestUtils.setField(geneticSolver, "ADAPTIVE_MUTATION_ENABLED", true);
        ReflectionTestUtils.setField(geneticSolver, "ADAPTIVE_MUTATION_MAX_RATE", 0.3);
        ReflectionTestUtils.setField(geneticSolver, "ADAPTIVE_MUTATION_MIN_RATE", 0.05);
        ReflectionTestUtils.setField(geneticSolver, "ADAPTIVE_MUTATION_STAGNATION_THRESHOLD", 5);

        // 创建测试数据
        createTestData();
    }

    private void createTestData() {
        testTopics = new ArrayList<>();
        
        // 创建简答题
        for (int i = 1; i <= 10; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(206);
            topic.setType("short");
            topic.setTitle("简答题 " + i);
            topic.setScore(25);
            topic.setDifficulty(0.1 + (i % 3) * 0.15); // 0.1, 0.25, 0.4
            testTopics.add(topic);
        }
        
        // 创建单选题
        for (int i = 11; i <= 30; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(206);
            topic.setType("choice");
            topic.setTitle("单选题 " + i);
            topic.setScore(5);
            topic.setDifficulty(0.1 + (i % 3) * 0.15);
            testTopics.add(topic);
        }

        // 设置题型目标数量
        typeTargetCounts = new HashMap<>();
        typeTargetCounts.put("short", 2);
        typeTargetCounts.put("choice", 0);

        // 设置难度分布
        difficultyDistribution = new HashMap<>();
        difficultyDistribution.put("easy", 0.3);
        difficultyDistribution.put("medium", 0.5);
        difficultyDistribution.put("hard", 0.2);

        // 创建增强数据
        enhancementDataMap = new HashMap<>();
        for (Topic topic : testTopics) {
            TopicEnhancementData data = new TopicEnhancementData();
            data.setTopicId(topic.getId());
            data.setUsageCount(topic.getId() % 3); // 模拟不同的使用次数
            enhancementDataMap.put(topic.getId(), data);
        }
    }

    @Test
    @DisplayName("测试启发式种子生成")
    void testGreedySeedGeneration() {
        // 模拟启发式种子生成
        List<GeneticSolver.Chromosome> mockSeeds = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            BitSet gene = new BitSet(testTopics.size());
            gene.set(i); // 选择前几个题目
            gene.set(i + 10); // 选择一些单选题
            mockSeeds.add(new GeneticSolver.Chromosome(gene));
        }

        when(greedySeedGenerator.generateSeeds(
            eq(testTopics), eq(typeTargetCounts), eq(100), 
            eq(difficultyDistribution), isNull(), eq(enhancementDataMap), eq(5)
        )).thenReturn(mockSeeds);

        // 执行测试
        List<Topic> result = geneticSolver.solve(
            testTopics, 100, difficultyDistribution, null, 
            enhancementDataMap, Arrays.asList(206), typeTargetCounts
        );

        // 验证
        verify(greedySeedGenerator, times(1)).generateSeeds(any(), any(), anyInt(), any(), any(), any(), anyInt());
        assertNotNull(result);
    }

    @Test
    @DisplayName("测试修复算子调用")
    void testRepairOperatorInvocation() {
        // 模拟修复算子返回修复步数
        when(repairOperator.repairChromosome(
            any(GeneticSolver.Chromosome.class), eq(testTopics), eq(typeTargetCounts),
            eq(100), isNull(), eq(enhancementDataMap)
        )).thenReturn(2);

        // 模拟启发式种子生成返回空列表，强制使用传统初始化
        when(greedySeedGenerator.generateSeeds(any(), any(), anyInt(), any(), any(), any(), anyInt()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        List<Topic> result = geneticSolver.solve(
            testTopics, 100, difficultyDistribution, null, 
            enhancementDataMap, Arrays.asList(206), typeTargetCounts
        );

        // 验证修复算子被调用
        verify(repairOperator, atLeastOnce()).repairChromosome(any(), any(), any(), anyInt(), any(), any());
        assertNotNull(result);
    }

    @Test
    @DisplayName("测试可行解检测")
    void testFeasibilityChecking() {
        // 模拟可行解检测
        FeasibilityChecker.FeasibilityResult feasibleResult = 
            new FeasibilityChecker.FeasibilityResult(true, "All constraints satisfied");
        
        when(feasibilityChecker.checkFeasibility(
            any(GeneticSolver.Chromosome.class), eq(testTopics), eq(typeTargetCounts),
            eq(100), isNull(), any()
        )).thenReturn(feasibleResult);

        // 模拟启发式种子生成
        when(greedySeedGenerator.generateSeeds(any(), any(), anyInt(), any(), any(), any(), anyInt()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        List<Topic> result = geneticSolver.solve(
            testTopics, 100, difficultyDistribution, null, 
            enhancementDataMap, Arrays.asList(206), typeTargetCounts
        );

        // 验证可行解检测被调用
        verify(feasibilityChecker, atLeastOnce()).checkFeasibility(any(), any(), any(), anyInt(), any(), any());
        assertNotNull(result);
    }

    @Test
    @DisplayName("测试权重总和为1")
    void testWeightSum() {
        double weightSum = 0.4 + 0.2 + 0.15 + 0.15 + 0.05 + 0.05; // 所有权重之和
        assertEquals(1.0, weightSum, 0.001, "权重总和应该等于1.0");
    }

    @Test
    @DisplayName("测试自适应变异率计算")
    void testAdaptiveMutationRate() {
        // 通过反射调用私有方法测试自适应变异率
        try {
            java.lang.reflect.Method method = GeneticSolver.class.getDeclaredMethod(
                "calculateAdaptiveMutationRate", int.class, int.class);
            method.setAccessible(true);

            // 测试早期代数
            double earlyRate = (Double) method.invoke(geneticSolver, 5, 0);
            assertTrue(earlyRate > 0.05, "早期变异率应该较高");

            // 测试后期代数
            double lateRate = (Double) method.invoke(geneticSolver, 25, 0);
            assertTrue(lateRate < earlyRate, "后期变异率应该较低");

            // 测试停滞情况
            double stagnantRate = (Double) method.invoke(geneticSolver, 15, 8);
            assertTrue(stagnantRate > lateRate, "停滞时变异率应该提高");

        } catch (Exception e) {
            fail("自适应变异率计算测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试简答题约束满足")
    void testShortAnswerConstraints() {
        // 确保只有简答题被选中
        List<Topic> result = geneticSolver.solve(
            testTopics, 100, difficultyDistribution, null, 
            enhancementDataMap, Arrays.asList(206), typeTargetCounts
        );

        assertNotNull(result);
        
        // 统计简答题数量
        long shortAnswerCount = result.stream()
            .filter(topic -> "short".equals(topic.getType()))
            .count();

        assertEquals(2, shortAnswerCount, "应该选择2道简答题");
    }

    @Test
    @DisplayName("测试全局超时机制")
    void testGlobalTimeout() {
        // 设置很短的超时时间
        ReflectionTestUtils.setField(geneticSolver, "GLOBAL_TIMEOUT_SECONDS", 0);

        long startTime = System.currentTimeMillis();
        
        List<Topic> result = geneticSolver.solve(
            testTopics, 100, difficultyDistribution, null, 
            enhancementDataMap, Arrays.asList(206), typeTargetCounts
        );

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证在合理时间内返回（考虑到初始化时间）
        assertTrue(duration < 2000, "应该在2秒内返回结果（包含超时机制）");
        assertNotNull(result);
    }
}
