package com.edu.maizi_edu_sys.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.StompSubProtocolErrorHandler;

import java.nio.charset.StandardCharsets;

/**
 * Custom error handler for WebSocket STOMP messages
 * Provides better error messages and logging for WebSocket errors
 */
@Component
@Slf4j
public class WebSocketErrorHandler extends StompSubProtocolErrorHandler {

    @Override
    public Message<byte[]> handleClientMessageProcessingError(@Nullable Message<byte[]> clientMessage, @Nullable Throwable ex) {
        final Throwable rootCause = getRootCause(ex != null ? ex : new Exception("Unknown error"));
        final StompHeaderAccessor accessor = StompHeaderAccessor.create(StompCommand.ERROR);

        // Add WebSocket error headers
        accessor.setMessage(rootCause.getMessage());
        accessor.setLeaveMutable(true);

        // Log the error with client details if available
        String destination = "unknown";
        String sessionId = "unknown";
        if (clientMessage != null) {
            StompHeaderAccessor clientHeaderAccessor = StompHeaderAccessor.wrap(clientMessage);
            destination = clientHeaderAccessor.getDestination() != null ? clientHeaderAccessor.getDestination() : destination;
            sessionId = clientHeaderAccessor.getSessionId() != null ? clientHeaderAccessor.getSessionId() : sessionId;
        }
        
        log.error("WebSocket error for session {} on destination {}: {}", 
                sessionId, destination, rootCause.getMessage(), rootCause);

        // Build error message payload with more details
        String errorPayload = String.format(
                "{'type': 'ERROR', 'message': '%s', 'details': '%s'}",
                rootCause.getMessage(),
                rootCause.getClass().getSimpleName());

        // Return formatted error message
        return MessageBuilder.createMessage(
                errorPayload.getBytes(StandardCharsets.UTF_8),
                accessor.getMessageHeaders());
    }

    /**
     * Get the root cause of an exception
     * @param throwable The throwable to examine
     * @return The root cause throwable
     */
    private Throwable getRootCause(Throwable throwable) {
        Throwable cause = throwable;
        Throwable rootCause = cause;
        while (cause != null) {
            rootCause = cause;
            cause = cause.getCause();
        }
        return rootCause;
    }

    @Override
    protected @NonNull Message<byte[]> handleInternal(@NonNull StompHeaderAccessor errorHeaderAccessor, @NonNull byte[] errorPayload, @Nullable Throwable cause, @Nullable StompHeaderAccessor clientHeaderAccessor) {
        return super.handleInternal(errorHeaderAccessor, errorPayload, cause, clientHeaderAccessor);
    }
} 