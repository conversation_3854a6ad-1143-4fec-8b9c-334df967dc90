2025-05-21 09:28:25.392 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-21 09:28:53.818 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:53.840 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:54.019 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:54.557 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:56.290 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-05-21 09:28:57.475 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.475 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.489 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:28:57.489 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.489 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:28:57.489 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.492 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.494 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.501 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:28:57.502 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:28:57.506 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.507 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:28:57.507 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.508 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:28:57.512 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:29:01.072 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:29:01.073 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:29:01.073 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:29:01.074 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:29:01.077 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:29:01.077 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:29:01.077 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:29:01.083 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:29:01.084 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:29:01.088 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:29:01.091 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:32:15.106 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:32:15.110 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:32:15.110 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:32:15.112 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:32:15.117 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:32:15.117 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:32:15.119 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:32:15.120 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:32:15.121 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:32:15.121 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:32:15.121 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:34:09.940 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:09.944 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:34:09.944 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:09.945 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:09.948 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:09.948 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:09.951 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:34:09.951 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:34:09.951 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:34:09.952 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:34:09.954 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:34:21.815 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:21.817 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:34:21.817 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:21.819 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:21.822 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:21.822 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:34:21.826 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:34:21.826 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:34:21.826 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:34:21.826 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:34:21.829 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:36:13.390 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:36:13.393 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:36:13.393 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:36:13.395 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:36:13.398 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:36:13.399 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:36:13.400 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:36:13.400 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:36:13.400 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:36:13.400 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:36:13.402 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:37:03.921 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:37:03.937 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:43:38.438 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:43:38.442 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 09:43:38.442 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:43:38.443 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 09:43:38.448 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:43:38.448 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:43:38.449 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 09:43:38.450 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:43:38.450 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:43:38.450 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:43:38.451 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:44:01.185 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:01.203 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:06.523 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:06.539 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:07.537 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:09.171 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:25.280 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:29.763 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:29.766 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:44:29.766 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:44:29.766 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:44:29.766 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:44:31.719 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:31.753 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.753 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.753 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.754 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.755 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.759 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.770 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:31.771 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:35.547 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.548 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.549 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:35.550 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.100 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.101 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.102 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:44:36.103 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:19.311 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:46:24.515 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:46:24.518 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 09:46:24.519 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 09:46:24.519 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 09:46:24.519 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 09:46:25.882 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.901 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.902 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.903 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.904 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.909 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.920 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.921 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.923 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.931 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:46:25.932 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.835 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.852 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.853 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.854 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.855 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.858 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.872 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.873 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.875 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.885 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:07.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.442 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.443 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.444 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.444 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.444 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.444 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.444 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.926 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.926 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.926 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.926 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.927 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.928 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 09:48:10.929 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 09:48:10.929 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:24:52.664 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-21 11:25:19.693 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-21 11:25:31.851 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:25:31.870 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:25:31.870 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:25:31.875 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 11:25:31.875 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:25:31.875 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 11:25:31.877 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 11:25:31.884 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 11:25:31.886 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 11:25:31.888 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 11:25:31.899 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 11:26:25.703 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:26:25.723 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:26:27.252 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:26:33.140 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:26:33.158 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:26:36.597 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:27:20.579 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:27:25.212 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:27:25.216 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 11:27:25.217 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 11:27:25.217 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 11:27:25.217 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 11:27:26.332 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.369 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.370 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.371 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.372 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.373 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.378 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.391 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:26.392 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.414 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.415 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.416 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.417 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.936 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.937 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.938 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 11:27:34.939 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 11:28:32.050 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:32:55.396 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:32:55.414 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:31.720 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:31.724 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 11:38:31.724 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:31.728 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:31.730 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:31.730 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:31.735 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 11:38:31.735 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 11:38:31.735 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 11:38:31.735 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 11:38:31.739 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 11:38:34.264 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:56.801 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:38:56.817 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:39:20.046 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:45:49.219 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-21 11:45:57.733 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:45:57.747 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:45:57.748 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:45:57.782 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 11:45:57.782 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:45:57.783 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 11:45:57.785 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 11:45:57.793 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 11:45:57.796 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 11:45:57.797 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 11:45:57.806 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 11:46:00.426 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:49:23.090 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-21 11:49:34.215 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:49:34.230 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:49:34.231 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:49:34.272 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 11:49:34.272 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:49:34.274 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 11:49:34.275 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 11:49:34.283 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 11:49:34.286 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 11:49:34.288 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 11:49:34.300 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 11:49:37.281 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:17.053 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:23.008 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:50.203 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:50.221 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:51.669 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:52.199 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:50:54.331 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:51:37.123 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:51:37.142 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:51:39.740 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 11:52:23.872 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:21.938 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:21.943 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 12:06:21.944 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:21.946 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:21.952 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:21.952 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:21.958 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 12:06:21.961 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 12:06:21.961 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 12:06:21.961 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 12:06:21.962 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 12:06:24.508 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:33.687 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:06:33.706 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:07:04.980 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:07:04.997 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:07:07.056 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:08:11.747 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:24.080 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:24.084 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...e7fA
2025-05-21 12:17:24.085 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:24.086 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:24.090 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:24.092 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:24.095 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 12:17:24.095 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 12:17:24.096 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 12:17:24.096 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 12:17:24.098 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-21 12:17:50.493 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:17:50.510 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:02.790 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:02.807 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:04.048 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:19.877 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:37.880 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:37.883 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-21 12:18:37.883 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-21 12:18:37.883 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-21 12:18:37.883 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-21 12:18:39.276 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:39.276 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-21 12:18:39.301 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:18:39.301 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:18:39.301 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:18:39.301 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:18:39.301 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.301 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:18:39.302 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:03.485 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:03.486 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-21 12:19:04.214 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-21 12:19:04.215 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:04.215 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-21 12:19:04.215 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-21 12:19:04.215 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
