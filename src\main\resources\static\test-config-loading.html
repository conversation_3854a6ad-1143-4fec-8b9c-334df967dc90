<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置加载测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-cog mr-2"></i>
                            配置加载测试
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            此页面用于测试配置加载功能，验证加载模态框是否能正确关闭，知识点配置是否能正确显示。
                        </p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5>测试场景 1：模拟配置加载</h5>
                                <button class="btn btn-primary btn-block" id="testConfigLoading">
                                    <i class="fas fa-download mr-1"></i>
                                    测试配置加载
                                </button>
                                <small class="text-muted">模拟从API加载配置的完整流程</small>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>测试场景 2：知识点配置应用</h5>
                                <button class="btn btn-success btn-block" id="testKnowledgeConfig">
                                    <i class="fas fa-brain mr-1"></i>
                                    测试知识点配置
                                </button>
                                <small class="text-muted">测试知识点配置的应用和显示</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5>测试场景 3：模态框状态清理</h5>
                                <button class="btn btn-warning btn-block" id="testModalCleanup">
                                    <i class="fas fa-broom mr-1"></i>
                                    测试模态框清理
                                </button>
                                <small class="text-muted">测试模态框状态的强制清理</small>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>测试场景 4：错误处理</h5>
                                <button class="btn btn-danger btn-block" id="testErrorHandling">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    测试错误处理
                                </button>
                                <small class="text-muted">测试加载失败时的错误处理</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-1"></i>测试说明：</h6>
                            <ul class="mb-0">
                                <li>点击测试按钮后，观察加载模态框是否能正确显示和关闭</li>
                                <li>检查知识点配置是否能正确应用到页面</li>
                                <li>验证页面是否能正常滚动，没有残留的backdrop</li>
                                <li>确认成功/错误提示是否正常显示</li>
                            </ul>
                        </div>
                        
                        <!-- 知识点配置显示区域 -->
                        <div class="mt-4">
                            <h5>知识点配置显示区域</h5>
                            <div id="knowledgeConfigDisplay" class="border rounded p-3 bg-light">
                                <p class="text-muted mb-0">暂无知识点配置</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="spinner-border text-primary mb-4" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <h5 id="loadingTitle" class="mb-3">加载配置</h5>
                    <p class="text-muted" id="loadingMessage">正在加载配置...</p>
                    <div class="progress mt-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery, Bootstrap JS, SweetAlert2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // 模拟修复后的函数
        function showLoading(message = '加载中...', title = '正在处理') {
            $('#loadingTitle').text(title);
            $('#loadingMessage').text(message);
            $('#loadingModal').modal('show');
        }

        function hideLoading() {
            try {
                console.log('🔧 开始关闭加载模态框...');

                const $loadingModal = $('#loadingModal');
                if ($loadingModal.length === 0) {
                    console.warn('⚠️ 加载模态框不存在');
                    return;
                }

                if (!$loadingModal.hasClass('show') && !$loadingModal.is(':visible')) {
                    console.log('✅ 加载模态框已经隐藏');
                    return;
                }

                // 立即隐藏模态框，不等待动画
                $loadingModal.removeClass('show').hide();
                console.log('🔧 已强制隐藏加载模态框');

                // 立即清理backdrop和body状态
                setTimeout(function() {
                    // 移除所有可能的backdrop
                    $('.modal-backdrop').each(function() {
                        const $backdrop = $(this);
                        console.log('🔧 移除backdrop:', $backdrop);
                        $backdrop.remove();
                    });

                    // 恢复body状态
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });

                    // 确保加载模态框完全隐藏
                    $loadingModal.css('display', 'none');

                    console.log('✅ 加载模态框关闭完成');
                }, 50);

            } catch (error) {
                console.error('❌ 关闭加载模态框失败:', error);

                // 强制清理
                try {
                    $('#loadingModal').hide().removeClass('show').css('display', 'none');
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });
                    console.log('✅ 强制清理完成');
                } catch (cleanupError) {
                    console.error('❌ 强制清理也失败:', cleanupError);
                }
            }
        }

        // 模拟配置加载成功的流程
        function simulateConfigLoading() {
            showLoading('正在加载配置...', '加载配置');
            
            setTimeout(function() {
                console.log('🔧 配置加载成功，开始清理模态框状态...');
                
                // 立即隐藏加载模态框
                hideLoading();
                
                // 强制清理所有模态框状态
                setTimeout(function() {
                    console.log('🔧 强制清理模态框状态...');
                    
                    // 移除所有可能的backdrop
                    $('.modal-backdrop').each(function() {
                        $(this).remove();
                    });
                    
                    // 恢复body状态
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });
                    
                    // 确保所有模态框都被隐藏
                    $('.modal').removeClass('show').hide();
                    
                    console.log('✅ 模态框状态清理完成');
                    
                    // 延迟显示成功提示
                    setTimeout(function() {
                        console.log('🔧 显示配置加载成功提示...');
                        
                        Swal.fire({
                            icon: 'success',
                            title: '配置加载成功',
                            text: '配置已成功加载，知识点配置已应用！',
                            timer: 3000,
                            showConfirmButton: true,
                            confirmButtonText: '确定'
                        });
                        
                        // 模拟知识点配置应用
                        applyMockKnowledgeConfig();
                    }, 300);
                }, 200);
            }, 2000); // 模拟2秒的加载时间
        }

        // 模拟知识点配置应用
        function applyMockKnowledgeConfig() {
            const mockConfig = [
                { knowledgeId: 190, knowledgeName: '数据结构基础', questionCount: 5 },
                { knowledgeId: 191, knowledgeName: '算法设计', questionCount: 3 },
                { knowledgeId: 192, knowledgeName: '数据库原理', questionCount: 4 }
            ];
            
            let html = '<h6>已加载的知识点配置：</h6><ul class="list-group">';
            mockConfig.forEach(config => {
                html += `
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><strong>${config.knowledgeName}</strong> (ID: ${config.knowledgeId})</span>
                        <span class="badge badge-primary badge-pill">${config.questionCount} 题</span>
                    </li>
                `;
            });
            html += '</ul>';
            
            $('#knowledgeConfigDisplay').html(html);
            console.log('✅ 知识点配置显示更新完成');
        }

        // 测试知识点配置
        function testKnowledgeConfig() {
            applyMockKnowledgeConfig();
            
            Swal.fire({
                icon: 'info',
                title: '知识点配置测试',
                text: '知识点配置已应用到显示区域',
                timer: 2000,
                showConfirmButton: false
            });
        }

        // 测试模态框清理
        function testModalCleanup() {
            showLoading('测试模态框清理...', '清理测试');
            
            setTimeout(function() {
                hideLoading();
                
                setTimeout(function() {
                    Swal.fire({
                        icon: 'success',
                        title: '清理完成',
                        text: '模态框状态已清理',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }, 100);
            }, 1000);
        }

        // 测试错误处理
        function testErrorHandling() {
            showLoading('正在加载配置...', '加载配置');
            
            setTimeout(function() {
                hideLoading();
                
                setTimeout(function() {
                    Swal.fire({
                        icon: 'error',
                        title: '加载失败',
                        text: '配置加载失败，请稍后重试'
                    });
                }, 100);
            }, 1500);
        }

        // 绑定测试按钮事件
        $(document).ready(function() {
            $('#testConfigLoading').on('click', simulateConfigLoading);
            $('#testKnowledgeConfig').on('click', testKnowledgeConfig);
            $('#testModalCleanup').on('click', testModalCleanup);
            $('#testErrorHandling').on('click', testErrorHandling);
            
            console.log('✅ 配置加载测试页面初始化完成');
        });
    </script>
</body>
</html>
