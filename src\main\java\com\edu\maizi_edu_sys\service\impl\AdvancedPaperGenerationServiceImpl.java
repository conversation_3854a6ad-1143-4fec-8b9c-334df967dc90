package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.service.PaperGenerationService;
import com.edu.maizi_edu_sys.util.AdvancedPdfGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 先进的试卷生成服务实现
 * 使用新的PDF生成技术栈
 */
@Slf4j
@Service("advancedPaperGenerationService")
public class AdvancedPaperGenerationServiceImpl {

    @Autowired
    private PaperGenerationService paperGenerationService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 生成PDF格式的试卷
     */
    public Resource generatePdfPaper(Paper paper, String paperType, boolean showQuestions, boolean showAnswers) {
        log.info("开始使用先进方案生成PDF试卷 - 试卷ID: {}, 类型: {}, 显示题目: {}, 显示答案: {}", 
            paper.getId(), paperType, showQuestions, showAnswers);

        try {
            // 1. 获取题目列表
            List<Topic> topics = getTopicsForPaper(paper);
            log.info("获取到 {} 道题目", topics.size());

            if (topics.isEmpty()) {
                log.warn("试卷 {} 没有题目", paper.getId());
                return AdvancedPdfGenerator.convertHtmlToPdf(
                    "<div style='text-align: center; margin-top: 50px;'>" +
                    "<h2>" + (paper.getTitle() != null ? paper.getTitle() : "试卷") + "</h2>" +
                    "<p style='color: red;'>该试卷暂无题目内容</p>" +
                    "</div>"
                );
            }

            // 2. 生成HTML内容
            String htmlContent = generateAdvancedHtml(paper, topics, paperType, showQuestions, showAnswers);
            log.debug("HTML内容生成完成，长度: {}", htmlContent.length());

            // 3. 转换为PDF
            Resource pdfResource = AdvancedPdfGenerator.convertHtmlToPdf(htmlContent);
            log.info("PDF生成成功");

            return pdfResource;

        } catch (Exception e) {
            log.error("生成PDF试卷失败: {}", e.getMessage(), e);
            return AdvancedPdfGenerator.convertHtmlToPdf(
                "<div style='text-align: center; color: red; margin-top: 50px;'>" +
                "<h2>试卷生成失败</h2>" +
                "<p>" + e.getMessage() + "</p>" +
                "</div>"
            );
        }
    }

    /**
     * 生成先进的HTML内容
     */
    private String generateAdvancedHtml(Paper paper, List<Topic> topics, String paperType, 
                                       boolean showQuestions, boolean showAnswers) {
        StringBuilder html = new StringBuilder();

        // 1. 试卷标题
        String title = paper.getTitle() != null ? paper.getTitle() : "试卷";
        html.append("<h1>").append(escapeHtml(title)).append("</h1>");

        // 2. 试卷基本信息
        String paperInfo = String.format("总分: %s | 难度: %.2f | %s试卷",
                paper.getTotalScore() != null ? paper.getTotalScore() : "未设置",
                paper.getDifficulty() != null ? paper.getDifficulty() : 0.0,
                getPaperTypeDisplayName(paperType));
        html.append("<div style='text-align: center; margin-bottom: 30px; font-size: 16px; color: #666;'>")
            .append(escapeHtml(paperInfo)).append("</div>");

        // 3. 生成题目内容
        if (showQuestions && !topics.isEmpty()) {
            html.append(generateQuestionsHtml(topics, paper, showAnswers));
        }

        return html.toString();
    }

    /**
     * 生成题目HTML
     */
    private String generateQuestionsHtml(List<Topic> topics, Paper paper, boolean showAnswers) {
        StringBuilder html = new StringBuilder();

        // 按题型分组
        Map<String, List<Topic>> topicsByType = topics.stream()
                .collect(Collectors.groupingBy(topic -> mapTopicType(topic.getType())));

        // 获取题型分值配置
        Map<String, Integer> typeScoresFromConfig = parseTypeScoreMapFromConfig(paper.getConfig());

        // 题型顺序
        String[] orderedTypes = {"singleChoice", "multipleChoice", "judgment", "fillBlank", "shortAnswer", "subjective", "groupQuestion"};

        int questionNumber = 1;

        for (String type : orderedTypes) {
            List<Topic> topicsOfType = topicsByType.getOrDefault(type, Collections.emptyList());
            if (topicsOfType.isEmpty()) {
                continue;
            }

            // 题型标题
            String typeChineseName = getChineseTopicTypeName(type);
            Integer scorePerQuestion = typeScoresFromConfig.getOrDefault(type, 0);
            Integer totalScore = scorePerQuestion * topicsOfType.size();

            html.append("<h2>").append(escapeHtml(typeChineseName))
                .append(" (每题").append(scorePerQuestion).append("分，共").append(totalScore).append("分)")
                .append("</h2>");

            // 生成题目
            for (Topic topic : topicsOfType) {
                html.append(generateQuestionHtml(topic, questionNumber++, type, showAnswers));
            }
        }

        return html.toString();
    }

    /**
     * 生成单个题目的HTML
     */
    private String generateQuestionHtml(Topic topic, int questionNumber, String type, boolean showAnswers) {
        StringBuilder html = new StringBuilder();
        
        html.append("<div class='question'>");
        
        // 题目标题
        String title = topic.getTitle() != null ? topic.getTitle() : "[题目标题缺失]";
        html.append("<div class='question-title'>").append(questionNumber).append(". ")
            .append(escapeHtml(title)).append("</div>");

        // 根据题型生成内容
        switch (type) {
            case "singleChoice":
            case "multipleChoice":
                html.append(generateChoiceOptions(topic));
                break;
            case "judgment":
                html.append("<div class='options'>");
                html.append("<div class='option-item'>A. 正确</div>");
                html.append("<div class='option-item'>B. 错误</div>");
                html.append("</div>");
                break;
            case "fillBlank":
                html.append("<div class='answer-line'>答案: __________________</div>");
                break;
            case "shortAnswer":
                html.append("<div class='answer-area'>答案:</div>");
                html.append("<div style='height: 100px; border: 1px solid #ddd; margin: 10px 0;'></div>");
                break;
            case "subjective":
                html.append("<div class='subjective-area'>答案:</div>");
                html.append("<div style='height: 120px; border: 1px solid #ddd; margin: 10px 0;'></div>");
                break;
            case "groupQuestion":
                html.append("<div class='group-question'>");
                html.append("<div class='group-note'>注：本题为组合题，请按要求作答。</div>");
                html.append("</div>");
                break;
            default:
                html.append("<div style='color: red;'>[未知题型: ").append(type).append("]</div>");
        }

        // 显示答案（如果需要）
        if (showAnswers && topic.getAnswer() != null && !topic.getAnswer().trim().isEmpty()) {
            html.append("<div style='margin-top: 15px; padding: 10px; background-color: #f0f8ff; border-left: 3px solid #007cba;'>");
            html.append("<strong>答案：</strong>").append(escapeHtml(topic.getAnswer()));
            
            if (topic.getParse() != null && !topic.getParse().trim().isEmpty()) {
                html.append("<br><strong>解析：</strong>").append(escapeHtml(topic.getParse()));
            }
            html.append("</div>");
        }

        html.append("</div>");
        return html.toString();
    }

    /**
     * 生成选择题选项
     */
    private String generateChoiceOptions(Topic topic) {
        StringBuilder html = new StringBuilder();
        
        try {
            List<Map<String, String>> options = parseOptions(topic.getOptions());
            if (!options.isEmpty()) {
                html.append("<div class='options'>");
                for (Map<String, String> option : options) {
                    String key = option.getOrDefault("key", "");
                    String name = option.getOrDefault("name", "");
                    html.append("<div class='option-item'>")
                        .append(escapeHtml(key)).append(". ")
                        .append(escapeHtml(name))
                        .append("</div>");
                }
                html.append("</div>");
            } else {
                html.append("<div class='options'>[选项数据为空]</div>");
            }
        } catch (Exception e) {
            log.error("解析题目 {} 选项失败: {}", topic.getId(), e.getMessage());
            html.append("<div class='options'>(选项解析失败)</div>");
        }
        
        return html.toString();
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }

    /**
     * 解析选项JSON
     */
    private List<Map<String, String>> parseOptions(String optionsJson) {
        try {
            if (optionsJson == null || optionsJson.isEmpty()) {
                return Collections.emptyList();
            }
            return objectMapper.readValue(optionsJson, new TypeReference<List<Map<String, String>>>() {});
        } catch (Exception e) {
            log.warn("解析选项JSON失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 解析题型分值配置
     */
    private Map<String, Integer> parseTypeScoreMapFromConfig(String configJson) {
        try {
            if (configJson == null || configJson.isEmpty()) {
                return Collections.emptyMap();
            }
            Map<String, Object> config = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});

            // 尝试从 globalTypeScoreMap 获取分值映射（新格式）
            Object rawTypeScoreMap = config.get("globalTypeScoreMap");
            if (rawTypeScoreMap == null) {
                // 回退到旧格式
                rawTypeScoreMap = config.get("typeScoreMap");
            }

            if (rawTypeScoreMap instanceof Map) {
                Map<?, ?> rawMap = (Map<?, ?>) rawTypeScoreMap;
                Map<String, Integer> typeScores = new HashMap<>();

                for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
                    if (entry.getKey() instanceof String && entry.getValue() != null) {
                        try {
                            String frontendKey = (String) entry.getKey();
                            Integer score = Integer.parseInt(entry.getValue().toString());

                            // 将前端格式的键名转换为数据库格式
                            String dbKey = mapTopicType(frontendKey);
                            typeScores.put(dbKey, score);

                            log.debug("映射题型分值: {} ({}) -> {} 分", frontendKey, dbKey, score);
                        } catch (NumberFormatException nfe) {
                            log.warn("Skipping invalid score value for key '{}': {}", entry.getKey(), entry.getValue());
                        }
                    }
                }

                log.info("解析配置得到的分值映射（数据库格式）: {}", typeScores);
                return typeScores;
            }
            return Collections.emptyMap();
        } catch (Exception e) {
            log.warn("解析题型分值配置失败: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 获取试卷的题目列表
     */
    private List<Topic> getTopicsForPaper(Paper paper) {
        try {
            PaperDetailDTO paperDetail = paperGenerationService.getPaperDetail(paper.getId());
            if (paperDetail != null && paperDetail.getTopics() != null) {
                return paperDetail.getTopics();
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("获取试卷 {} 的题目时出错: {}", paper.getId(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 映射题目类型 - 使用统一的TopicTypeMapper工具类
     */
    private String mapTopicType(String type) {
        if (type == null) return "";

        // 使用统一的TopicTypeMapper工具类，转换为数据库标准格式
        return com.edu.maizi_edu_sys.util.TopicTypeMapper.toDbFormat(type);
    }

    /**
     * 获取题型中文名称 - 使用统一的TopicTypeMapper工具类
     */
    private String getChineseTopicTypeName(String type) {
        if (type == null) return "未知题型";

        // 先转换为数据库格式，再获取中文名称
        String dbFormat = com.edu.maizi_edu_sys.util.TopicTypeMapper.toDbFormat(type);
        return com.edu.maizi_edu_sys.util.TopicTypeMapper.getChineseName(dbFormat);
    }

    /**
     * 获取试卷类型显示名称
     */
    private String getPaperTypeDisplayName(String paperType) {
        if (paperType == null) return "普通";
        
        switch (paperType) {
            case "regular": return "学生";
            case "teacher": return "教师";
            case "standard": return "标准";
            default: return paperType;
        }
    }
}
