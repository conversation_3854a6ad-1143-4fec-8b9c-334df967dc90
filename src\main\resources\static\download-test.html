<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-download me-2"></i>下载功能测试
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>测试说明：</strong>点击下面的按钮测试不同的下载方式，观察浏览器的下载行为。
                        </div>
                        
                        <div class="row g-3">
                            <!-- 测试文件下载 -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-file-alt text-primary me-2"></i>测试文件下载
                                        </h5>
                                        <p class="card-text">下载一个简单的测试文本文件</p>
                                        <button class="btn btn-primary" onclick="testSimpleDownload()">
                                            <i class="fas fa-download me-1"></i>下载测试文件
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 试卷PDF下载 -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-file-pdf text-danger me-2"></i>试卷PDF下载
                                        </h5>
                                        <p class="card-text">下载试卷PDF文件（试卷ID: 123）</p>
                                        <button class="btn btn-danger" onclick="testPaperDownload()">
                                            <i class="fas fa-file-pdf me-1"></i>下载试卷PDF
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- window.location.href 方式 -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-link text-success me-2"></i>location.href 方式
                                        </h5>
                                        <p class="card-text">使用 window.location.href 下载</p>
                                        <button class="btn btn-success" onclick="testLocationDownload()">
                                            <i class="fas fa-external-link-alt me-1"></i>location.href
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- window.open 方式 -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-external-link-alt text-warning me-2"></i>window.open 方式
                                        </h5>
                                        <p class="card-text">使用 window.open 下载</p>
                                        <button class="btn btn-warning" onclick="testWindowOpenDownload()">
                                            <i class="fas fa-external-link-alt me-1"></i>window.open
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 创建链接方式 -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-link text-info me-2"></i>创建链接方式
                                        </h5>
                                        <p class="card-text">创建隐藏链接并点击</p>
                                        <button class="btn btn-info" onclick="testCreateLinkDownload()">
                                            <i class="fas fa-link me-1"></i>创建链接下载
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Fetch API 方式 -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-cloud-download-alt text-secondary me-2"></i>Fetch API 方式
                                        </h5>
                                        <p class="card-text">使用 Fetch API 下载</p>
                                        <button class="btn btn-secondary" onclick="testFetchDownload()">
                                            <i class="fas fa-cloud-download-alt me-1"></i>Fetch 下载
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5>测试结果记录</h5>
                            <div id="testResults" class="border rounded p-3 bg-light" style="min-height: 100px; max-height: 300px; overflow-y: auto;">
                                <p class="text-muted mb-0">点击上面的按钮开始测试...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 记录测试结果
        function logResult(method, success, message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>';
            const statusClass = success ? 'text-success' : 'text-danger';
            
            const resultHtml = `
                <div class="mb-2 p-2 border-bottom">
                    <strong>[${timestamp}]</strong> ${statusIcon} 
                    <span class="${statusClass}">${method}</span>: ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('点击上面的按钮开始测试')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
            }
        }

        // 测试简单文件下载
        function testSimpleDownload() {
            try {
                const url = '/debug/download-test';
                console.log(' 测试简单文件下载:', url);
                
                window.location.href = url;
                logResult('测试文件下载', true, '下载请求已发送');
                
                setTimeout(() => {
                    Swal.fire({
                        title: '下载测试',
                        text: '测试文件下载是否成功？',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonText: '成功',
                        cancelButtonText: '失败'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            logResult('测试文件下载', true, '用户确认下载成功');
                        } else {
                            logResult('测试文件下载', false, '用户反馈下载失败');
                        }
                    });
                }, 2000);
                
            } catch (error) {
                console.error('❌ 测试文件下载失败:', error);
                logResult('测试文件下载', false, '发生错误: ' + error.message);
            }
        }

        // 测试试卷PDF下载
        function testPaperDownload() {
            try {
                const url = '/api/papers/download/123?format=pdf&paperType=regular';
                console.log(' 测试试卷PDF下载:', url);
                
                window.location.href = url;
                logResult('试卷PDF下载', true, '下载请求已发送');
                
            } catch (error) {
                console.error('❌ 试卷PDF下载失败:', error);
                logResult('试卷PDF下载', false, '发生错误: ' + error.message);
            }
        }

        // 测试 location.href 方式
        function testLocationDownload() {
            try {
                const url = '/debug/download-test';
                console.log(' 测试 location.href 下载:', url);
                
                window.location.href = url;
                logResult('location.href 方式', true, '下载请求已发送');
                
            } catch (error) {
                console.error('❌ location.href 下载失败:', error);
                logResult('location.href 方式', false, '发生错误: ' + error.message);
            }
        }

        // 测试 window.open 方式
        function testWindowOpenDownload() {
            try {
                const url = '/debug/download-test';
                console.log(' 测试 window.open 下载:', url);
                
                const newWindow = window.open(url, '_blank');
                if (newWindow) {
                    logResult('window.open 方式', true, '新窗口已打开');
                } else {
                    logResult('window.open 方式', false, '弹窗被阻止');
                }
                
            } catch (error) {
                console.error('❌ window.open 下载失败:', error);
                logResult('window.open 方式', false, '发生错误: ' + error.message);
            }
        }

        // 测试创建链接方式
        function testCreateLinkDownload() {
            try {
                const url = '/debug/download-test';
                console.log(' 测试创建链接下载:', url);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = 'test-download.txt';
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                logResult('创建链接方式', true, '隐藏链接已点击');
                
            } catch (error) {
                console.error('❌ 创建链接下载失败:', error);
                logResult('创建链接方式', false, '发生错误: ' + error.message);
            }
        }

        // 测试 Fetch API 方式
        function testFetchDownload() {
            try {
                const url = '/debug/download-test';
                console.log(' 测试 Fetch API 下载:', url);
                
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应不正常');
                        }
                        return response.blob();
                    })
                    .then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'fetch-download.txt';
                        link.style.display = 'none';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                        
                        logResult('Fetch API 方式', true, 'Blob下载成功');
                    })
                    .catch(error => {
                        console.error('❌ Fetch API 下载失败:', error);
                        logResult('Fetch API 方式', false, '发生错误: ' + error.message);
                    });
                
            } catch (error) {
                console.error('❌ Fetch API 下载失败:', error);
                logResult('Fetch API 方式', false, '发生错误: ' + error.message);
            }
        }
    </script>
</body>
</html>
