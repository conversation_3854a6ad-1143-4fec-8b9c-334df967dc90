package com.edu.maizi_edu_sys.config;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.lang.NonNull;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
public class RateLimitInterceptor implements HandlerInterceptor {
    
    private final RedisTemplate<String, Integer> redisTemplate;
    private static final int MAX_REQUESTS = 100; // 每分钟最大请求数
    private static final int EXPIRE_TIME = 60; // 时间窗口（秒）

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, 
                           @NonNull HttpServletResponse response, 
                           @NonNull Object handler) {
        String ip = request.getRemoteAddr();
        String uri = request.getRequestURI();
        String key = "ratelimit:" + ip + ":" + uri;
        
        Integer count = redisTemplate.opsForValue().get(key);
        if (count == null) {
            redisTemplate.opsForValue().set(key, 1, EXPIRE_TIME, TimeUnit.SECONDS);
        } else if (count >= MAX_REQUESTS) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            return false;
        } else {
            redisTemplate.opsForValue().increment(key);
        }
        
        return true;
    }
} 