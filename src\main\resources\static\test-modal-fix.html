<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框修复测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-bug mr-2"></i>
                            模态框修复测试
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            此页面用于测试保存配置成功后加载模态框不消失的问题修复。
                        </p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5>测试场景 1：正常保存</h5>
                                <button class="btn btn-success btn-block" id="testNormalSave">
                                    <i class="fas fa-save mr-1"></i>
                                    测试正常保存
                                </button>
                                <small class="text-muted">模拟正常的配置保存流程</small>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>测试场景 2：保存失败</h5>
                                <button class="btn btn-warning btn-block" id="testFailedSave">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    测试保存失败
                                </button>
                                <small class="text-muted">模拟保存失败的情况</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5>测试场景 3：加载配置</h5>
                                <button class="btn btn-info btn-block" id="testLoadConfig">
                                    <i class="fas fa-folder-open mr-1"></i>
                                    测试加载配置
                                </button>
                                <small class="text-muted">模拟加载配置的流程</small>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>测试场景 4：强制清理</h5>
                                <button class="btn btn-secondary btn-block" id="testForceCleanup">
                                    <i class="fas fa-broom mr-1"></i>
                                    强制清理模态框
                                </button>
                                <small class="text-muted">强制清理所有模态框状态</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-1"></i>测试说明：</h6>
                            <ul class="mb-0">
                                <li>点击测试按钮后，观察加载模态框是否能正确关闭</li>
                                <li>检查页面是否能正常滚动</li>
                                <li>确认没有残留的backdrop遮罩</li>
                                <li>验证SweetAlert提示是否正常显示</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="spinner-border text-primary mb-4" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <h5 id="loadingTitle" class="mb-3">正在处理</h5>
                    <p class="text-muted" id="loadingMessage">请稍候...</p>
                    <div class="progress mt-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery, Bootstrap JS, SweetAlert2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // 模拟修复后的函数
        function showLoading(message = '加载中...', title = '正在处理') {
            $('#loadingTitle').text(title);
            $('#loadingMessage').text(message);
            $('#loadingModal').modal('show');
        }

        function hideLoading() {
            try {
                console.log('🔧 开始关闭加载模态框...');

                const $loadingModal = $('#loadingModal');
                if ($loadingModal.length === 0) {
                    console.warn('⚠️ 加载模态框不存在');
                    return;
                }

                if (!$loadingModal.hasClass('show') && !$loadingModal.is(':visible')) {
                    console.log('✅ 加载模态框已经隐藏');
                    return;
                }

                // 立即隐藏模态框，不等待动画
                $loadingModal.removeClass('show').hide();
                console.log('🔧 已强制隐藏加载模态框');

                // 立即清理backdrop和body状态
                setTimeout(function() {
                    // 移除所有可能的backdrop
                    $('.modal-backdrop').each(function() {
                        const $backdrop = $(this);
                        console.log('🔧 移除backdrop:', $backdrop);
                        $backdrop.remove();
                    });

                    // 恢复body状态
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });

                    // 确保加载模态框完全隐藏
                    $loadingModal.css('display', 'none');

                    console.log('✅ 加载模态框关闭完成');
                }, 50);

            } catch (error) {
                console.error('❌ 关闭加载模态框失败:', error);

                // 强制清理
                try {
                    $('#loadingModal').hide().removeClass('show').css('display', 'none');
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });
                    console.log('✅ 强制清理完成');
                } catch (cleanupError) {
                    console.error('❌ 强制清理也失败:', cleanupError);
                }
            }
        }

        // 模拟保存配置成功的流程
        function simulateSaveSuccess() {
            showLoading('正在保存配置...', '保存配置');
            
            setTimeout(function() {
                console.log('🔧 配置保存成功，开始清理模态框状态...');
                
                // 立即隐藏加载模态框
                hideLoading();
                
                // 强制清理所有模态框状态，增加延迟确保完全清理
                setTimeout(function() {
                    console.log('🔧 强制清理模态框状态...');
                    
                    // 移除所有可能的backdrop
                    $('.modal-backdrop').each(function() {
                        $(this).remove();
                    });
                    
                    // 恢复body状态
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });
                    
                    // 确保所有模态框都被隐藏
                    $('.modal').removeClass('show').hide();
                    
                    console.log('✅ 模态框状态清理完成');
                    
                    // 延迟显示成功提示，确保模态框完全关闭
                    setTimeout(function() {
                        console.log('🔧 显示保存成功提示...');
                        
                        Swal.fire({
                            icon: 'success',
                            title: '保存成功',
                            text: '配置已成功保存！',
                            timer: 3000,
                            showConfirmButton: true,
                            confirmButtonText: '确定'
                        });
                    }, 300);
                }, 200);
            }, 2000); // 模拟2秒的保存时间
        }

        // 模拟保存失败的流程
        function simulateSaveFailure() {
            showLoading('正在保存配置...', '保存配置');
            
            setTimeout(function() {
                hideLoading();
                setTimeout(function() {
                    Swal.fire({
                        icon: 'error',
                        title: '保存失败',
                        text: '配置保存失败，请稍后重试'
                    });
                }, 100);
            }, 2000);
        }

        // 模拟加载配置的流程
        function simulateLoadConfig() {
            showLoading('正在加载配置...', '加载配置');
            
            setTimeout(function() {
                hideLoading();
                setTimeout(function() {
                    Swal.fire({
                        icon: 'success',
                        title: '加载成功',
                        text: '配置已成功加载！'
                    });
                }, 100);
            }, 1500);
        }

        // 强制清理所有模态框状态
        function forceCleanupModals() {
            console.log('🔧 强制清理所有模态框状态...');
            
            try {
                // 隐藏所有模态框
                $('.modal').removeClass('show').hide().css('display', 'none');
                
                // 移除所有backdrop
                $('.modal-backdrop').remove();
                
                // 恢复body状态
                $('body').removeClass('modal-open').css({
                    'overflow': '',
                    'padding-right': '',
                    'position': '',
                    'top': ''
                });
                
                console.log('✅ 强制清理完成');
                
                Swal.fire({
                    icon: 'info',
                    title: '清理完成',
                    text: '所有模态框状态已清理',
                    timer: 2000,
                    showConfirmButton: false
                });
            } catch (error) {
                console.error('❌ 强制清理失败:', error);
            }
        }

        // 绑定测试按钮事件
        $(document).ready(function() {
            $('#testNormalSave').on('click', simulateSaveSuccess);
            $('#testFailedSave').on('click', simulateSaveFailure);
            $('#testLoadConfig').on('click', simulateLoadConfig);
            $('#testForceCleanup').on('click', forceCleanupModals);
            
            console.log('✅ 模态框修复测试页面初始化完成');
        });
    </script>
</body>
</html>
