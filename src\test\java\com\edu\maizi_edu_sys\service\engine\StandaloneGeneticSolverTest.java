package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class StandaloneGeneticSolverTest {

    @InjectMocks
    private GeneticSolver geneticSolver;

    @BeforeEach
    public void setup() {
        // Set essential private fields using reflection
        ReflectionTestUtils.setField(geneticSolver, "POPULATION_SIZE", 20);
        ReflectionTestUtils.setField(geneticSolver, "MAX_GENERATIONS", 10);
        ReflectionTestUtils.setField(geneticSolver, "MIN_GENERATIONS", 5);
        ReflectionTestUtils.setField(geneticSolver, "CROSSOVER_RATE", 0.8);
        ReflectionTestUtils.setField(geneticSolver, "MUTATION_RATE", 0.1);
        ReflectionTestUtils.setField(geneticSolver, "TOURNAMENT_SIZE", 3);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_SCORE", 0.4);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_QUALITY", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_DIFFICULTY_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_COGNITIVE_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "EARLY_TERMINATE_THRESHOLD", 0.95);
    }

    @Test
    @DisplayName("测试遗传算法难度分类映射")
    public void testDifficultyNameMapping() {
        // Test the difficulty mapping using reflection to access the private method
        Double[] difficultyValues = {0.1, 0.2, 0.3, 0.4, 0.5};
        String[] expectedNames = {"easy", "easy", "medium", "medium", "hard"};
        
        for (int i = 0; i < difficultyValues.length; i++) {
            String result = (String) ReflectionTestUtils.invokeMethod(geneticSolver, 
                    "getDifficultyName", difficultyValues[i]);
            assertEquals(expectedNames[i], result, 
                    "Difficulty " + difficultyValues[i] + " should map to " + expectedNames[i]);
        }
    }
    
    @Test
    @DisplayName("测试遗传算法基本功能")
    public void testBasicGeneticSolverFunctionality() {
        // 创建模拟数据
        List<Topic> testTopics = createTestTopics();
        
        // 设置目标分数和分布
        int targetScore = 30;
        Map<String, Integer> typeScores = new HashMap<>();
        typeScores.put("单选题", 20);
        typeScores.put("多选题", 10);
        
        Map<String, Double> difficultyDistribution = new HashMap<>();
        difficultyDistribution.put("easy", 0.3);
        difficultyDistribution.put("medium", 0.5);
        difficultyDistribution.put("hard", 0.2);
        
        // 执行遗传算法
        List<Topic> result = geneticSolver.solve(
                testTopics,
                targetScore,
                typeScores,
                difficultyDistribution,
                Collections.emptyMap(),
                Collections.emptyMap(),
                Collections.emptyList()
        );
        
        // 验证结果
        assertNotNull(result, "Result should not be null");
        assertFalse(result.isEmpty(), "Result should not be empty");
        
        // 分析结果的分数
        int totalScore = result.stream().mapToInt(Topic::getScore).sum();
        System.out.println("Total score: " + totalScore + " (target: " + targetScore + ")");
        
        // 分析结果的难度分布
        Map<String, List<Topic>> resultByDifficulty = new HashMap<>();
        resultByDifficulty.put("easy", new ArrayList<>());
        resultByDifficulty.put("medium", new ArrayList<>());
        resultByDifficulty.put("hard", new ArrayList<>());
        
        for (Topic topic : result) {
            double difficulty = topic.getDifficulty();
            String category = getDifficultyCategory(difficulty);
            resultByDifficulty.get(category).add(topic);
        }
        
        System.out.println("\n难度分布结果:");
        difficultyDistribution.forEach((difficulty, targetPercent) -> {
            List<Topic> topicsInCategory = resultByDifficulty.get(difficulty);
            double actualPercent = result.isEmpty() ? 0 : (double) topicsInCategory.size() / result.size();
            System.out.printf("%s难度: 目标 %.1f%%, 实际 %.1f%% (%d题)\n", 
                    difficulty, targetPercent * 100, actualPercent * 100, topicsInCategory.size());
        });
    }
    
    private List<Topic> createTestTopics() {
        List<Topic> topics = new ArrayList<>();
        
        // Create topics with different difficulties and scores
        for (int i = 1; i <= 50; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setType(i % 3 == 0 ? "多选题" : "单选题");
            
            // Distribute difficulties evenly
            if (i % 5 == 0) {
                topic.setDifficulty(0.5); // hard
                topic.setScore(5);
            } else if (i % 5 == 1 || i % 5 == 2) {
                topic.setDifficulty(0.1); // easy
                topic.setScore(2);
            } else {
                topic.setDifficulty(0.3); // medium
                topic.setScore(3); 
            }
            
            topics.add(topic);
        }
        
        return topics;
    }
    
    // Helper method for difficulty categorization - must match GeneticSolver implementation
    private String getDifficultyCategory(double difficulty) {
        if (difficulty <= 0.2) return "easy";
        if (difficulty <= 0.4) return "medium";
        return "hard";
    }
} 