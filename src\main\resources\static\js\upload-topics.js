let editor;
let previewTopics = [];
let currentPreviewIndex = 0;

document.addEventListener('DOMContentLoaded', function() {
    // 初始化 CodeMirror 编辑器
    editor = CodeMirror.fromTextArea(document.getElementById('jsonEditor'), {
        mode: {name: "javascript", json: true},
        theme: "material-darker",
        lineNumbers: true,
        matchBrackets: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        lineWrapping: true,
        extraKeys: {
            "Ctrl-Space": "autocomplete"
        }
    });

    // 其他初始化代码...

    // 获取DOM元素
    const insertExampleBtn = document.getElementById('insertExampleBtn');
    const validateAndPreviewBtn = document.getElementById('validateAndPreviewBtn');
    const submitTopicsBtn = document.getElementById('submitTopicsBtn');
    const previewContainer = document.getElementById('previewContainer');
    const errorMessageDiv = document.getElementById('errorMessage');
    const successMessageDiv = document.getElementById('successMessage');

    const previewQuestionCountSpan = document.getElementById('previewQuestionCount');
    const previewFirstBtn = document.getElementById('previewFirstBtn');
    const previewPrevBtn = document.getElementById('previewPrevBtn');
    const previewNextBtn = document.getElementById('previewNextBtn');
    const previewLastBtn = document.getElementById('previewLastBtn');

    // 插入示例JSON
    insertExampleBtn.addEventListener('click', function () {
        const exampleJson = JSON.parse(JSON.stringify(exampleTopicData)); // Deep copy
        // JSON字符串中的反斜杠需要转义
        exampleJson.forEach(topic => {
            if (topic.title) topic.title = topic.title.replace(/\\/g, '\\\\');
            if (topic.parse) topic.parse = topic.parse.replace(/\\/g, '\\\\');
            if (topic.options) {
                topic.options.forEach(opt => {
                    if (opt.name) opt.name = opt.name.replace(/\\/g, '\\\\');
                });
            }
        });
        editor.setValue(JSON.stringify(exampleJson, null, 2));
        showToast('示例JSON已插入编辑器', 'info');
        validateAndPreview(); // 自动预览示例
    });

    // 验证并预览
    validateAndPreviewBtn.addEventListener('click', validateAndPreview);

    function validateAndPreview() {
        const jsonString = editor.getValue();
        if (!jsonString.trim()) {
            showError('JSON数据不能为空。');
            previewContainer.innerHTML = '<p class="text-muted text-center p-5"><i class="bi bi-card-text fs-1"></i><br>编辑器内容为空。</p>';
            previewContainer.classList.add('preview-container-empty');
            updatePreviewControls();
            return;
        }

        try {
            previewTopics = JSON.parse(jsonString);
            if (!Array.isArray(previewTopics)) {
                throw new Error('JSON数据必须是一个数组。');
            }
            if (previewTopics.length === 0) {
                showError('JSON数组不能为空。');
                 previewContainer.innerHTML = '<p class="text-muted text-center p-5"><i class="bi bi-card-text fs-1"></i><br>JSON数组为空。</p>';
                previewContainer.classList.add('preview-container-empty');
                updatePreviewControls();
                return;
            }

            // 验证每个题目的结构 (可以根据需要添加更详细的验证规则)
            for (const topic of previewTopics) {
                if (!topic.know_id || !topic.type || !topic.title || !topic.difficulty) {
                    throw new Error('题目缺少必需字段 (know_id, type, title, difficulty)。');
                }
                if ((topic.type === 'choice' || topic.type === 'multiple') && (!topic.options || !Array.isArray(topic.options) || topic.options.length === 0)) {
                    throw new Error(`类型为 '${topic.type}' 的题目缺少有效的 'options' 数组。`);
                }
                if ((topic.type === 'choice' || topic.type === 'multiple' || topic.type === 'judge') && typeof topic.answer === 'undefined') {
                     throw new Error(`类型为 '${topic.type}' 的题目缺少 'answer' 字段。`);
                }
            }

            showSuccess('JSON数据验证通过！');
            submitTopicsBtn.disabled = false;
            currentPreviewIndex = 0;
            renderPreview();
            previewContainer.classList.remove('preview-container-empty');
        } catch (error) {
            showError('JSON解析或验证失败: ' + error.message);
            submitTopicsBtn.disabled = true;
            previewTopics = [];
            previewContainer.innerHTML = `<p class="text-danger text-center p-5"><i class="bi bi-exclamation-triangle-fill fs-1"></i><br>JSON数据格式错误或不符合要求。<br><small>${error.message}</small></p>`;
            previewContainer.classList.add('preview-container-empty');
        }
        updatePreviewControls();
    }

    // 渲染预览
    function renderPreview() {
        if (previewTopics.length === 0 || currentPreviewIndex < 0 || currentPreviewIndex >= previewTopics.length) {
            previewContainer.innerHTML = '<p class="text-muted text-center p-5">没有可预览的题目。</p>';
            previewContainer.classList.add('preview-container-empty');
            updatePreviewControls();
            return;
        }
        previewContainer.classList.remove('preview-container-empty');

        const topic = previewTopics[currentPreviewIndex];
        let html = `<div class="p-2"><h5>${renderMarkdown(topic.title)}</h5>`;

        if (topic.options && Array.isArray(topic.options)) {
            html += '<ul class="options-list">';
            topic.options.forEach(opt => {
                html += `<li><strong>${opt.key}.</strong> ${renderMarkdown(opt.name)}</li>`;
            });
            html += '</ul>';
        }

        html += '<div class="topic-meta mt-3">';
        html += `<p><strong>答案:</strong> ${topic.answer}</p>`;
        if (topic.parse) {
            html += `<div class="topic-parse"><strong>解析:</strong> ${renderMarkdown(topic.parse)}</div>`;
        }
        html += `<p><strong>知识点ID:</strong> ${topic.know_id} | <strong>类型:</strong> ${topic.type} | <strong>难度:</strong> ${topic.difficulty}</p>`;
        if (topic.source) {
            html += `<p><strong>来源:</strong> ${renderMarkdown(topic.source)}</p>`;
        }
        html += '</div></div>';

        previewContainer.innerHTML = html;

        // 使用KaTeX渲染公式
        if (window.renderKaTeX) {
            renderKaTeX(previewContainer);
        }
        updatePreviewControls();
    }

    function renderMarkdown(text) {
        if (!text) return '';
        // 先处理LaTeX转义，确保JSON中的 \\frac 变成 \frac 给Marked和KaTeX
        const unescapedText = text.replace(/\\\\/g, '\\');
        return marked.parse(unescapedText, { breaks: true, gfm: true });
    }

    // 更新预览控制按钮状态
    function updatePreviewControls() {
        const total = previewTopics.length;
        previewQuestionCountSpan.textContent = total > 0 ? `${currentPreviewIndex + 1} / ${total}` : '0 / 0';

        previewFirstBtn.disabled = currentPreviewIndex <= 0;
        previewPrevBtn.disabled = currentPreviewIndex <= 0;
        previewNextBtn.disabled = currentPreviewIndex >= total - 1;
        previewLastBtn.disabled = currentPreviewIndex >= total - 1 || total === 0;
    }

    // 预览导航
    previewFirstBtn.addEventListener('click', () => { currentPreviewIndex = 0; renderPreview(); });
    previewPrevBtn.addEventListener('click', () => { if (currentPreviewIndex > 0) currentPreviewIndex--; renderPreview(); });
    previewNextBtn.addEventListener('click', () => { if (currentPreviewIndex < previewTopics.length - 1) currentPreviewIndex++; renderPreview(); });
    previewLastBtn.addEventListener('click', () => { currentPreviewIndex = previewTopics.length - 1; renderPreview(); });

    // 提交题目
    submitTopicsBtn.addEventListener('click', async function () {
        if (previewTopics.length === 0) {
            showError('没有可提交的题目。');
            return;
        }
        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...';

        try {
            const token = getAuthHeader(); // 从 common.js 获取 token
            if (!token) {
                showError('用户未登录或会话已过期，请重新登录。');
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-cloud-upload"></i> 提交题目';
                // 可以选择跳转到登录页
                // window.location.href = '/auth/login';
                return;
            }

            const response = await fetch('/api/topics/upload', { // <--- 修改了API端点
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}` // 确保common.js中的getAuthHeader()返回的是纯token
                },
                body: JSON.stringify(previewTopics)
            });

            const result = await response.json();

            if (response.ok && result.code === 200) {
                // 后端直接返回 "题目上传成功" 字符串，或者一个包含计数信息的对象
                // 根据 TopicController 的 /upload 端点，它返回 ApiResponse.success("题目上传成功")
                // 因此 result.data 可能是 null 或者就是那个成功消息字符串。
                // 如果需要计数，后端需要调整返回结构。
                // 暂时我们只显示后端返回的通用成功消息。
                showSuccess(result.message || `成功提交 ${previewTopics.length} 道题目！`);
                editor.setValue(''); // 清空编辑器
                previewTopics = [];
                currentPreviewIndex = 0;
                renderPreview(); // 清空预览
                submitTopicsBtn.disabled = true;
            } else {
                throw new Error(result.message || `服务器错误: ${response.status}`);
            }
        } catch (error) {
            showError('提交失败: ' + error.message);
        } finally {
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-cloud-upload"></i> 提交题目';
        }
    });

    // 辅助函数显示错误/成功消息
    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.classList.remove('d-none');
        successMessageDiv.classList.add('d-none');
        showToast(message, 'error');
    }

    function showSuccess(message) {
        successMessageDiv.textContent = message;
        successMessageDiv.classList.remove('d-none');
        errorMessageDiv.classList.add('d-none');
        showToast(message, 'success');
    }

    // 示例题目数据 (与 template.json 内容一致)
    const exampleTopicData = [
        {
            "know_id": 218,
            "type": "choice",
            "title": "新民主主义革命的开端是（    ）",
            "options": [
                {"key":"A","name":"中国共产党成立（1921年）"},
                {"key":"B","name":"五四运动（1919年）"},
                {"key":"C","name":"辛亥革命（1911年）"},
                {"key":"D","name":"南昌起义（1927年）"}
            ],
            "answer": "B",
            "parse": "五四运动中无产阶级首次以独立姿态登上政治舞台，标志着中国革命从旧民主主义转向新民主主义。中共成立（A）是革命领导核心形成，辛亥革命（C）是旧民主主义高潮，南昌起义（D）是武装革命开端，均非开端事件。",
            "source": "《思想政治必修1中国特色社会主义》第二章节'只有社会主义才能救中国'中：'五四运动是中国旧民主主义革命走向新民主主义革命的转折点。'",
            "difficulty": 0.3
        },
        {
            "know_id": 218,
            "type": "choice",
            "title": "我国社会主义基本制度确立的标志是（    ）",
            "options": [
                {"key":"A","name":"新中国成立（1949年）"},
                {"key":"B","name":"三大改造基本完成（1956年）"},
                {"key":"C","name":"中共八大召开（1956年）"},
                {"key":"D","name":"'一五'计划完成（1957年）"}
            ],
            "answer": "B",
            "parse": "1956年底三大改造基本完成，实现了生产资料私有制向公有制的转变，标志着社会主义基本制度确立（B正确）。新中国成立（A）是新民主主义革命胜利，中共八大（C）是探索建设道路，'一五'计划（D）是工业化起步，均非制度确立标志。",
            "source": "《思想政治必修1中国特色社会主义》第二章节'只有社会主义才能救中国'中：'到1956年底，我国基本完成了对生产资料私有制的社会主义改造，在中国建立起社会主义制度。'",
            "difficulty": 0.3
        },
         {
            "know_id": 218,
            "type": "judge",
            "title": "新中国成立标志着我国进入社会主义社会（    ）",
            "answer": "否",
            "parse": "新中国成立（1949年）标志着新民主主义革命胜利，我国进入新民主主义社会；1956年底三大改造基本完成，才标志着社会主义基本制度确立，进入社会主义社会。",
            "source": "《思想政治必修1中国特色社会主义》第二章节'只有社会主义才能救中国'中：'1956年，生产资料私有制的社会主义改造取得了决定性的胜利，标志着我国实现了从新民主主义到社会主义的转变，进入了社会主义社会。'",
            "difficulty": 0.3
        }
        // 您可以从 template.json 中复制更多示例数据
    ];

    // 初始化页面状态
    updatePreviewControls();
    // 可以在这里自动加载一个空示例或上次未提交的内容（如果需要）
    // editor.setValue(JSON.stringify(exampleTopicData.slice(0,1), null, 2)); // 加载一个示例
    // validateAndPreview();
});

// 插入数学公式示例函数
function insertMathExample() {
    const exampleJson = [
        {
            "know_id": 1,
            "type": "choice",
            "title": "以下哪个是正确的平方和公式？$\\sum_{i=1}^{n} i^2 = ?$",
            "options": [
                {"key": "A", "name": "$\\frac{n(n+1)}{2}$"},
                {"key": "B", "name": "$\\frac{n(n+1)(2n+1)}{6}$"},
                {"key": "C", "name": "$\\frac{n^2(n+1)^2}{4}$"},
                {"key": "D", "name": "$n^3$"}
            ],
            "answer": "B",
            "source": "数学公式例题",
            "parse": "正确的平方和公式是：$$\\sum_{i=1}^{n} i^2 = \\frac{n(n+1)(2n+1)}{6}$$",
            "difficulty": 0.6
        }
    ];
    editor.setValue(JSON.stringify(exampleJson, null, 2));
    showToast('已插入数学公式示例', 'info');
}

// 添加其他函数...