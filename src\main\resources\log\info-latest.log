2025-05-19 00:12:26.913 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 00:12:26.914 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6d4f266]]
2025-05-19 00:12:26.914 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 00:12:27.937 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 00:12:27.938 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 00:12:27.943 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 00:12:31.544 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9150 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 00:12:31.545 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 00:12:32.012 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 00:12:32.012 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 00:12:32.049 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 3 JPA repository interfaces.
2025-05-19 00:12:32.055 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 00:12:32.055 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 00:12:32.061 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 00:12:32.062 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 00:12:32.062 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 00:12:32.062 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 00:12:32.350 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 00:12:32.354 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 00:12:32.354 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 00:12:32.354 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 00:12:32.391 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 00:12:32.392 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 827 ms
2025-05-19 00:12:32.582 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 00:12:32.699 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 00:12:33.003 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 00:12:33.008 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 00:12:33.008 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 00:12:33.008 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 00:12:33.184 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 00:12:33.222 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 00:12:33.486 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 00:12:33.675 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 00:12:33.684 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-05-19 00:12:33.861 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 00:12:33.889 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 00:12:33.900 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 00:12:33.901 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 00:12:33.902 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6b756a62]]
2025-05-19 00:12:33.902 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 00:12:33.908 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 2.552 seconds (JVM running for 2.785)
2025-05-19 00:12:33.915 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 00:12:33.916 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 00:12:33.917 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 00:12:33.918 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 00:12:33.939 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 28
2025-05-19 00:12:34.426 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 00:12:34.426 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 00:12:34.427 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-19 00:12:40.295 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzUzNDQwOSwiZXhwIjoxNzQ3NjIwODA5fQ.-WoVuZFUUt4B-UNEZlUJn5o2jztA10NweECpckdv3wfHldAoXZkZKLX8fH0_IhF_QAK-hK-4QdkZz0iW43h3zQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 00:12:40.305 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 00:12:44.472 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 00:13:08.812 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 00:13:08.886 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 集合考点  专项练习
2025-05-19 00:13:10.445 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/29, Token: exists
2025-05-19 00:13:10.448 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 29
2025-05-19 00:13:10.588 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:13:10.589 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:13:10.936 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/29, Token: exists
2025-05-19 00:13:10.937 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 29
2025-05-19 00:19:22.544 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 00:19:24.915 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 00:55:50.848 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 00:55:50.848 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6b756a62]]
2025-05-19 00:55:50.848 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 00:55:51.889 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 00:55:51.891 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 00:55:51.895 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 00:55:55.815 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 11534 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 00:55:55.816 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 00:55:56.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 00:55:56.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 00:55:56.344 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 3 JPA repository interfaces.
2025-05-19 00:55:56.351 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 00:55:56.351 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 00:55:56.358 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 00:55:56.359 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 00:55:56.360 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 00:55:56.360 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-05-19 00:55:56.668 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 00:55:56.672 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 00:55:56.672 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 00:55:56.672 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 00:55:56.712 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 00:55:56.712 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 875 ms
2025-05-19 00:55:56.929 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 00:55:57.041 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 00:55:57.415 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 00:55:57.421 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 00:55:57.421 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 00:55:57.421 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 00:55:57.522 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 00:55:57.556 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 00:55:57.814 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 00:55:58.020 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 00:55:58.031 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-05-19 00:55:58.203 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 00:55:58.230 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 00:55:58.241 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 00:55:58.242 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 00:55:58.242 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6b756a62]]
2025-05-19 00:55:58.242 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 00:55:58.249 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 2.633 seconds (JVM running for 2.874)
2025-05-19 00:55:58.254 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 00:55:58.256 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 00:55:58.257 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 00:55:58.279 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 29
2025-05-19 00:55:58.694 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 00:55:58.695 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 00:55:58.695 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-05-19 00:56:12.394 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzUzNDQwOSwiZXhwIjoxNzQ3NjIwODA5fQ.-WoVuZFUUt4B-UNEZlUJn5o2jztA10NweECpckdv3wfHldAoXZkZKLX8fH0_IhF_QAK-hK-4QdkZz0iW43h3zQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 00:56:12.654 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 00:56:12.674 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:56:12.674 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 00:56:12.680 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:56:12.693 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 00:56:12.695 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 00:56:12.707 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 00:56:12.709 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 00:56:12.739 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:56:12.740 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:56:12.831 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 00:56:14.497 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 00:56:32.621 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 00:56:32.698 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 识记类  专项练习
2025-05-19 00:56:34.623 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:56:34.628 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:56:35.763 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/30, Token: exists
2025-05-19 00:56:35.766 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 30
2025-05-19 00:56:36.080 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/30, Token: exists
2025-05-19 00:56:36.081 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 30
2025-05-19 00:56:53.632 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 00:56:55.253 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 00:57:13.269 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 00:57:13.275 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 识记类  专项练习
2025-05-19 00:57:15.167 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:57:15.170 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:57:39.599 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/31, Token: exists
2025-05-19 00:57:39.600 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 31
2025-05-19 00:57:40.924 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/31, Token: exists
2025-05-19 00:57:40.925 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 31
2025-05-19 00:58:34.377 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 00:58:34.382 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 00:58:34.388 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 00:58:34.388 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:58:34.388 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 00:58:34.388 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 00:58:34.388 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 00:58:34.390 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:58:34.390 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 00:58:34.390 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 00:58:34.732 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 00:58:34.765 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 00:58:36.802 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 01:13:32.825 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 01:13:32.828 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 01:13:32.830 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 01:13:32.832 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 01:13:32.834 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:13:32.834 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:13:32.834 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:13:32.834 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:13:32.836 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:13:32.836 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:13:33.229 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:13:33.269 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:13:36.921 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/31, Token: exists
2025-05-19 01:14:12.416 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 01:14:17.361 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/29, Token: exists
2025-05-19 01:14:17.850 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/29, Token: exists
2025-05-19 01:16:16.529 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:16:19.889 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/31, Token: exists
2025-05-19 01:16:19.908 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/31, Token: exists
2025-05-19 01:16:22.513 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/31, Token: exists
2025-05-19 01:16:22.530 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/31, Token: exists
2025-05-19 01:16:24.752 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 01:16:24.754 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 01:16:24.756 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 01:16:24.759 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 01:16:24.767 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:16:24.767 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:16:24.770 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:16:24.776 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:16:24.779 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:16:24.781 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:16:24.835 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:16:25.947 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/31, Token: exists
2025-05-19 01:16:25.959 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/31, Token: exists
2025-05-19 01:16:36.776 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/30, Token: exists
2025-05-19 01:16:36.790 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/30, Token: exists
2025-05-19 01:16:52.473 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 01:16:52.475 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 01:16:52.478 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 01:16:52.481 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 01:16:52.491 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:16:52.491 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:16:52.493 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:16:52.497 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:16:52.499 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:16:52.500 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:16:52.752 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:16:54.219 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 01:16:56.639 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 01:16:56.644 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 集合考点  专项练习
2025-05-19 01:16:58.069 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:16:58.069 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:16:58.382 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:16:58.382 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:00.498 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:00.499 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:00.924 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:00.924 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:02.909 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:02.910 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:03.241 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:03.241 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:03.757 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:03.758 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:04.079 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:04.079 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:04.565 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:04.566 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:04.874 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:04.875 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:05.003 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:05.003 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:05.334 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:05.335 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:07.905 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:07.906 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:08.246 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/32, Token: exists
2025-05-19 01:17:08.247 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 32
2025-05-19 01:17:09.287 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:17:13.496 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:17:13.499 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:17:14.876 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:14.888 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:14.897 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:18.320 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 01:17:19.333 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.345 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.354 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.783 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.796 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.806 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.962 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.972 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:17:19.980 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/32, Token: exists
2025-05-19 01:18:23.475 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/32, Token: exists
2025-05-19 01:18:24.360 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/32, Token: exists
2025-05-19 01:25:50.339 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 01:25:50.339 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6b756a62]]
2025-05-19 01:25:50.339 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 01:25:51.357 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:25:51.359 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 01:25:51.363 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 01:25:58.378 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 13235 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 01:25:58.380 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 01:25:58.817 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:25:58.818 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 01:25:58.855 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 3 JPA repository interfaces.
2025-05-19 01:25:58.859 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:25:58.860 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 01:25:58.866 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:25:58.867 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:25:58.867 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:25:58.868 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 01:25:59.126 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 01:25:59.130 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 01:25:59.130 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 01:25:59.130 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 01:25:59.168 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 01:25:59.168 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 768 ms
2025-05-19 01:25:59.347 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 01:25:59.455 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 01:25:59.554 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 01:25:59.555 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 01:25:59.556 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-19 01:25:59.563 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-19 01:27:26.437 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 13349 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 01:27:26.438 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 01:27:26.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:27:26.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 01:27:26.917 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 3 JPA repository interfaces.
2025-05-19 01:27:26.923 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:27:26.923 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 01:27:26.929 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:27:26.930 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:27:26.930 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:27:26.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 01:27:27.189 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 01:27:27.193 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 01:27:27.193 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 01:27:27.193 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 01:27:27.232 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 01:27:27.232 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 772 ms
2025-05-19 01:27:27.449 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 01:27:27.575 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 01:27:27.672 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 01:27:27.674 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 01:27:27.675 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-19 01:27:27.683 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-19 01:29:28.253 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 13464 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 01:29:28.254 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 01:29:28.730 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:29:28.730 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 01:29:28.766 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 3 JPA repository interfaces.
2025-05-19 01:29:28.772 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:29:28.773 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 01:29:28.779 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:29:28.780 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:29:28.780 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:29:28.780 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 01:29:29.055 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 01:29:29.059 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 01:29:29.059 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 01:29:29.059 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 01:29:29.100 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 01:29:29.100 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 821 ms
2025-05-19 01:29:29.320 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 01:29:29.442 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 01:29:29.748 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:29:29.754 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 01:29:29.754 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 01:29:29.754 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 01:29:29.932 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 01:29:29.976 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 01:29:30.254 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 01:29:30.423 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:29:30.423 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 01:29:30.427 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 01:29:30.428 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-19 01:29:30.436 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-19 01:30:47.969 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 13557 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 01:30:47.971 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 01:30:48.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:30:48.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 01:30:48.478 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 3 JPA repository interfaces.
2025-05-19 01:30:48.484 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:30:48.484 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 01:30:48.491 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:30:48.491 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:30:48.492 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:30:48.492 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 01:30:48.756 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 01:30:48.760 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 01:30:48.760 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 01:30:48.760 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 01:30:48.800 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 01:30:48.800 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 807 ms
2025-05-19 01:30:49.006 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 01:30:49.118 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 01:30:49.425 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:30:49.431 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 01:30:49.431 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 01:30:49.431 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 01:30:49.598 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 01:30:49.632 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 01:30:49.898 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 01:30:50.031 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:30:50.032 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 01:30:50.039 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 01:30:50.040 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-19 01:30:50.047 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-19 01:36:10.537 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 13921 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 01:36:10.538 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 01:36:10.975 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:36:10.976 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 01:36:11.011 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 3 JPA repository interfaces.
2025-05-19 01:36:11.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:36:11.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 01:36:11.025 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:36:11.026 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:36:11.026 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:36:11.026 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 01:36:11.279 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 01:36:11.282 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 01:36:11.282 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 01:36:11.282 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 01:36:11.323 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 01:36:11.323 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 765 ms
2025-05-19 01:36:11.511 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 01:36:11.619 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 01:36:11.911 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:36:11.916 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 01:36:11.916 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 01:36:11.916 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 01:36:12.078 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 01:36:12.113 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 01:36:12.373 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 01:36:12.574 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 01:36:12.584 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-05-19 01:36:12.751 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 01:36:12.776 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 01:36:12.786 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 01:36:12.787 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 01:36:12.787 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@523f3c29]]
2025-05-19 01:36:12.787 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 01:36:12.796 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 2.448 seconds (JVM running for 2.694)
2025-05-19 01:36:12.802 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 01:36:12.804 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 01:36:12.805 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 01:36:12.805 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 01:36:12.805 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 01:36:12.805 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 01:36:12.805 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 01:36:12.805 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 01:36:12.806 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 01:36:12.826 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 32
2025-05-19 01:36:12.931 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 01:36:12.931 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 01:36:12.932 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-19 01:37:40.713 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzUzNDQwOSwiZXhwIjoxNzQ3NjIwODA5fQ.-WoVuZFUUt4B-UNEZlUJn5o2jztA10NweECpckdv3wfHldAoXZkZKLX8fH0_IhF_QAK-hK-4QdkZz0iW43h3zQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 01:37:41.712 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 01:37:41.723 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:37:41.723 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:37:41.723 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:37:41.723 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:37:41.752 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 01:37:41.752 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:37:41.752 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:37:41.755 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 01:37:41.782 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 01:37:42.075 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:37:42.105 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:37:44.922 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/32, Token: exists
2025-05-19 01:37:45.453 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/32, Token: exists
2025-05-19 01:41:54.778 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 01:41:54.779 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@523f3c29]]
2025-05-19 01:41:54.779 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 01:41:55.800 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:41:55.803 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 01:41:55.808 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 01:44:46.351 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 15734 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 01:44:46.354 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 01:44:46.814 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:44:46.814 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 01:44:46.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 3 JPA repository interfaces.
2025-05-19 01:44:46.859 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 01:44:46.860 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 01:44:46.866 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:44:46.867 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:44:46.867 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 01:44:46.867 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-19 01:44:47.140 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 01:44:47.144 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 01:44:47.144 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 01:44:47.144 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 01:44:47.186 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 01:44:47.187 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 810 ms
2025-05-19 01:44:47.371 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 01:44:47.478 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 01:44:47.759 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 01:44:47.765 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 01:44:47.765 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 01:44:47.765 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 01:44:47.962 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 01:44:48.006 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 01:44:48.287 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 01:44:48.500 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 01:44:48.511 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-05-19 01:44:48.703 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 01:44:48.734 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 01:44:48.746 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 01:44:48.747 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 01:44:48.748 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@28fef9a2]]
2025-05-19 01:44:48.748 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 01:44:48.755 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 2.592 seconds (JVM running for 2.843)
2025-05-19 01:44:48.761 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 01:44:48.762 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 01:44:48.763 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 01:44:48.763 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 01:44:48.764 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 01:44:48.787 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 32
2025-05-19 01:44:49.102 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 01:44:49.102 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 01:44:49.103 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-19 01:44:52.951 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzUzNDQwOSwiZXhwIjoxNzQ3NjIwODA5fQ.-WoVuZFUUt4B-UNEZlUJn5o2jztA10NweECpckdv3wfHldAoXZkZKLX8fH0_IhF_QAK-hK-4QdkZz0iW43h3zQ] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 01:44:53.299 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:44:54.026 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 01:44:54.030 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:44:54.032 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:44:54.063 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 01:44:54.063 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:44:54.065 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 01:44:54.079 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 01:44:54.086 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 01:44:54.118 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 01:44:54.120 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 01:44:54.120 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 01:44:56.508 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/32, Token: exists
2025-05-19 01:44:56.914 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/32, Token: exists
2025-05-19 02:36:17.650 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-05-19 02:36:17.651 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@28fef9a2]]
2025-05-19 02:36:17.651 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-05-19 02:36:18.669 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 02:36:18.671 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-19 02:36:18.676 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-19 11:50:27.067 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 5357 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-05-19 11:50:27.069 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-19 11:50:27.584 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 11:50:27.584 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-19 11:50:27.628 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 3 JPA repository interfaces.
2025-05-19 11:50:27.637 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-19 11:50:27.638 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-19 11:50:27.646 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 11:50:27.647 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 11:50:27.647 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-19 11:50:27.647 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-05-19 11:50:27.955 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-05-19 11:50:27.959 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-19 11:50:27.959 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-19 11:50:27.959 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-19 11:50:28.006 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-19 11:50:28.006 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 914 ms
2025-05-19 11:50:28.233 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-19 11:50:28.370 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-19 11:50:28.699 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-19 11:50:28.704 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-19 11:50:28.704 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-19 11:50:28.705 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-19 11:50:28.905 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-05-19 11:50:28.957 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-19 11:50:29.279 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-19 11:50:29.547 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-05-19 11:50:29.559 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-05-19 11:50:29.759 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-19 11:50:29.793 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-19 11:50:29.807 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-05-19 11:50:29.808 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-05-19 11:50:29.808 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7a14ab66]]
2025-05-19 11:50:29.808 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-05-19 11:50:29.816 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 2.958 seconds (JVM running for 3.245)
2025-05-19 11:50:29.822 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-19 11:50:29.823 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: bit
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - type: int
2025-05-19 11:50:29.824 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-19 11:50:29.827 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.461, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 1, total_score=100, type=1, update_time=2025-05-17T20:24:28.461}, {id=2, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.481, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 2, total_score=100, type=2, update_time=2025-05-17T20:24:28.481}, {id=3, actual_total_score=null, config=null, content=1,2,3,4,5, create_time=2025-05-17T20:24:28.491, difficulty=3.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title=示例试卷 3, total_score=100, type=0, update_time=2025-05-17T20:24:28.491}, {id=4, actual_total_score=null, config={"topicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"typeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10}}, content=173688,173690,173694,173695,173696,173697,173698,173702,173703,173704,173736,173740,174375,174376,174380,174390,174396,174398,174401,174537, create_time=2025-05-17T20:25:05.117, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 函数考点 试卷, total_score=50, type=null, update_time=2025-05-17T20:25:05.117}, {id=5, actual_total_score=null, config={"globalTypeScoreMap":{"singleChoice":3,"multipleChoice":4,"judgment":2,"fill":3,"shortAnswer":10,"subjective":10,"group":10},"difficultyDistribution":{"easy":0.3,"medium":0.5,"hard":0.2},"globalTopicTypeCounts":{"singleChoice":10,"multipleChoice":5,"judgment":10,"fill":0,"shortAnswer":3,"subjective":0,"group":0},"knowledgePointConfigs":[{"knowledgeId":190,"questionCount":1,"includeShortAnswer":true}]}, content=175939, create_time=2025-05-17T20:58:39.766, difficulty=0.5, is_deleted=false, knowledge_id=null, knowledge_name=null, title= 识记类  专项练习, total_score=3, type=null, update_time=2025-05-17T20:58:39.766}]
2025-05-19 11:50:29.854 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 32
2025-05-19 11:50:29.971 [RMI TCP Connection(1)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 11:50:29.971 [RMI TCP Connection(1)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-19 11:50:29.972 [RMI TCP Connection(1)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-19 11:50:37.275 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 11:50:37.296 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 11:50:42.264 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Login attempt from IP: 0:0:0:0:0:0:0:1, username: admin
2025-05-19 11:50:42.366 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: admin with secret (first 5): 'F9A8C...'
2025-05-19 11:50:43.389 [http-nio-8081-exec-7] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0NzYyNjY0MiwiZXhwIjoxNzQ3NzEzMDQyfQ.YpEkps53UPgJUDTylMudC7EiJCKmPFAKZ3CjKqETSScL8OeQX0zyw5XwiHkhLGOLCuWZ9QuzEEqXzXdt7GTGnw] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-19 11:50:43.457 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/info, Token: exists
2025-05-19 11:50:43.457 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 11:50:43.468 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 11:50:43.468 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/info called (redirecting to /current)
2025-05-19 11:50:43.468 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 11:50:43.470 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 11:50:43.470 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 11:50:43.473 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 11:50:43.473 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 11:50:43.476 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 11:50:43.477 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 11:50:43.478 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 11:50:43.481 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 11:50:43.532 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 11:50:43.543 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 11:50:46.167 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-19 11:50:46.169 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-19 11:50:46.170 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-19 11:50:46.173 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-19 11:50:46.176 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 11:50:46.176 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 11:50:46.181 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 11:50:46.183 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 11:50:46.230 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-19 11:50:46.247 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-19 11:50:46.261 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 11:50:46.267 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-19 11:50:46.268 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='', type=null, sort='time-desc', page=0, size=10
2025-05-19 11:50:46.268 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='', type=null, sortField='create_time', isAsc=false, page=0, size=10
2025-05-19 11:50:46.277 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Query results: total=32, pages=4, current=1, size=10, records=10
2025-05-19 11:50:47.112 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 11:50:49.556 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge, Token: exists
2025-05-19 11:51:28.951 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate, Token: exists
2025-05-19 11:51:28.968 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 函数考点  专项练习
2025-05-19 11:51:28.974 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='函数考点  专项练习', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=196, questionCount=null, includeShortAnswer=true)], GlobalTypeCounts={SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, DifficultyCriteria={easy=30.0, medium=50.0, hard=20.0}
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 33. Requested global counts (for warning reference): {SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=10, MULTIPLE_CHOICE=10, JUDGMENT=10, FILL_IN_BLANKS=3, SHORT_ANSWER=2}
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='singleChoice'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='multipleChoice'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGMENT' → Mapped key='judgment'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL_IN_BLANKS' → Mapped key='FILL_IN_BLANKS'
2025-05-19 11:51:29.152 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT_ANSWER' → Mapped key='shortAnswer'
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {singleChoice=17, judgment=16}
2025-05-19 11:51:29.154 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {singleChoice=10, judgment=10, multipleChoice=10, FILL_IN_BLANKS=3, shortAnswer=2}
2025-05-19 11:51:29.157 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- 单选题: 请求10/实际17
- 判断题: 请求10/实际16
- 多选题: 请求10/实际0 [警告: 此题型在题库中完全不存在!]
- FILL_IN_BLANKS: 请求3/实际0 [警告: 此题型在题库中完全不存在!]
- 简答题: 请求2/实际0 [警告: 此题型在题库中完全不存在!]
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 10 topics of type 'singleChoice' (requested 10)
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 10 topics of type 'judgment' (requested 10)
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 33. Final result size: 20. Actual counts per type after enforcement: {singleChoice=10, judgment=10}
2025-05-19 11:51:29.158 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 99) after enforcing type counts...
2025-05-19 11:51:29.159 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - After DP adjustment: 20 topics, actual score: 60 (target score: 99)
2025-05-19 11:51:29.176 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 33. Title: 函数考点  专项练习
2025-05-19 11:51:29.204 [http-nio-8081-exec-10] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 20 topics.
2025-05-19 11:51:30.637 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/33, Token: exists
2025-05-19 11:51:30.640 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 33
2025-05-19 11:51:30.643 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 20 topics for paper id: 33
2025-05-19 11:51:30.646 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 20 topics in database (from 20 requested IDs) for paper id: 33
2025-05-19 11:51:30.652 [http-nio-8081-exec-9] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 20 ordered topics for paper id: 33
2025-05-19 11:51:31.072 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/33, Token: exists
2025-05-19 11:51:31.072 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 33
2025-05-19 11:51:31.074 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 20 topics for paper id: 33
2025-05-19 11:51:31.076 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 20 topics in database (from 20 requested IDs) for paper id: 33
2025-05-19 11:51:31.076 [http-nio-8081-exec-1] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 20 ordered topics for paper id: 33
2025-05-19 11:51:32.938 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/33, Token: exists
2025-05-19 11:51:32.938 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 33
2025-05-19 11:51:32.941 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 20 topics for paper id: 33
2025-05-19 11:51:32.945 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 20 topics in database (from 20 requested IDs) for paper id: 33
2025-05-19 11:51:32.946 [http-nio-8081-exec-4] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 20 ordered topics for paper id: 33
2025-05-19 11:51:33.347 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/preview/33, Token: exists
2025-05-19 11:51:33.347 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Request to preview paper with id: 33
2025-05-19 11:51:33.349 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 20 topics for paper id: 33
2025-05-19 11:51:33.351 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 20 topics in database (from 20 requested IDs) for paper id: 33
2025-05-19 11:51:33.352 [http-nio-8081-exec-2] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 20 ordered topics for paper id: 33
2025-05-19 11:51:51.649 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/download/33, Token: exists
2025-05-19 11:51:51.703 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimHei字体，路径: /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simhei.ttf
2025-05-19 11:51:51.736 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 正在加载SimSun字体，路径: /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/fonts/simsun.ttf
2025-05-19 11:51:51.737 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - 试卷字体加载完成，已优化字体排版
2025-05-19 11:51:51.743 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Loading 20 topics for paper id: 33
2025-05-19 11:51:51.745 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Found 20 topics in database (from 20 requested IDs) for paper id: 33
2025-05-19 11:51:51.746 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Returning 20 ordered topics for paper id: 33
2025-05-19 11:51:51.791 [http-nio-8081-exec-3] INFO  c.e.m.service.impl.PaperGenerationServiceImpl - Successfully generated PDF for paper id: 33
