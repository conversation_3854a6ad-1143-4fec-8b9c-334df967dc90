<!-- 自由组卷模态框 -->
<div class="modal fade" id="customPaperModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-puzzle-piece mr-2"></i>自由组卷
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="customPaperForm">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customPaperTitle">试卷标题</label>
                                <input type="text" class="form-control" id="customPaperTitle" required placeholder="例如：高一数学期末测试">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customPaperType">试卷类型</label>
                                <select class="form-control" id="customPaperType">
                                    <option value="0">普通试卷</option>
                                    <option value="1">教师专用</option>
                                    <option value="2">标准考试</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customPaperFormat">输出格式</label>
                                <select class="form-control" id="customPaperFormat">
                                    <option value="pdf">PDF文档</option>
                                    <option value="word">Word文档</option>
                                    <option value="html">网页版</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 难度分布设置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie mr-2"></i>难度分布
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="easyPercentage">简单题比例 (%)</label>
                                        <input type="number" class="form-control difficulty-input" id="easyPercentage" min="0" max="100" value="30">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="mediumPercentage">中等题比例 (%)</label>
                                        <input type="number" class="form-control difficulty-input" id="mediumPercentage" min="0" max="100" value="50">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="hardPercentage">困难题比例 (%)</label>
                                        <input type="number" class="form-control difficulty-input" id="hardPercentage" min="0" max="100" value="20">
                                    </div>
                                </div>
                            </div>
                            <div class="progress mt-2" style="height: 30px;">
                                <div class="progress-bar bg-success" id="easyProgressBar" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">简单 30%</div>
                                <div class="progress-bar bg-warning" id="mediumProgressBar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">中等 50%</div>
                                <div class="progress-bar bg-danger" id="hardProgressBar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">困难 20%</div>
                            </div>
                            <div id="difficultyError" class="text-danger mt-2" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- 题型配置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-list-ol mr-2"></i>题型配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>题型</th>
                                            <th width="150">数量</th>
                                            <th width="150">每题分值</th>
                                            <th width="150">总分</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>单选题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customSingleChoiceCount" min="0" value="10">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customSingleChoiceScore" min="0" value="3">
                                            </td>
                                            <td class="type-total-score">30</td>
                                        </tr>
                                        <tr>
                                            <td>多选题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customMultipleChoiceCount" min="0" value="5">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customMultipleChoiceScore" min="0" value="4">
                                            </td>
                                            <td class="type-total-score">20</td>
                                        </tr>
                                        <tr>
                                            <td>判断题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customJudgmentCount" min="0" value="5">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customJudgmentScore" min="0" value="2">
                                            </td>
                                            <td class="type-total-score">10</td>
                                        </tr>
                                        <tr>
                                            <td>填空题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customFillCount" min="0" value="3">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customFillScore" min="0" value="3">
                                            </td>
                                            <td class="type-total-score">9</td>
                                        </tr>
                                        <tr>
                                            <td>简答题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customShortAnswerCount" min="0" value="2">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customShortAnswerScore" min="0" value="10">
                                            </td>
                                            <td class="type-total-score">20</td>
                                        </tr>
                                        <tr>
                                            <td>主观题</td>
                                            <td>
                                                <input type="number" class="form-control question-count" id="customSubjectiveCount" min="0" value="1">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control question-score" id="customSubjectiveScore" min="0" value="15">
                                            </td>
                                            <td class="type-total-score">15</td>
                                        </tr>
                                    </tbody>
                                    <tfoot class="bg-light">
                                        <tr>
                                            <th colspan="3" class="text-right">总分：</th>
                                            <th id="customTotalScore">104</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 知识点配置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-brain mr-2"></i>知识点配置
                            </h5>
                            <button type="button" class="btn btn-primary btn-sm" id="addKnowledgePointBtn">
                                <i class="fas fa-plus mr-1"></i>添加知识点
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>请为每个知识点设置题目数量，并选择是否包含简答题。
                            </div>
                            
                            <!-- 知识点列表容器 -->
                            <div id="knowledgePointsContainer" class="mb-3">
                                <!-- 知识点项将通过JavaScript动态添加 -->
                                <div class="text-center text-muted py-5" id="noKnowledgePointsMessage">
                                    <i class="fas fa-brain fa-3x mb-3"></i>
                                    <p>暂无知识点，请点击"添加知识点"按钮添加</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="generateCustomPaperBtn">
                    <i class="fas fa-magic mr-1"></i>生成试卷
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 知识点选择模态框 -->
<div class="modal fade" id="knowledgePointSelectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-brain mr-2"></i>选择知识点
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="text" class="form-control" id="knowledgePointSearchInput" placeholder="搜索知识点...">
                </div>
                <div class="knowledge-point-list">
                    <div class="text-center py-4" id="loadingKnowledgePoints">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载知识点...</p>
                    </div>
                    <div id="knowledgePointsList" class="list-group">
                        <!-- 知识点将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="confirmKnowledgePointSelection">
                    <i class="fas fa-check mr-1"></i>确认选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 知识点项模板 -->
<template id="knowledgePointItemTemplate">
    <div class="card mb-3 knowledge-point-item" data-id="{id}">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="card-title mb-0">{name}</h5>
                <button type="button" class="btn btn-sm btn-danger remove-knowledge-point">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>题目数量</label>
                        <input type="number" class="form-control knowledge-question-count" min="0" value="5">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>包含简答题</label>
                        <div class="custom-control custom-switch mt-2">
                            <input type="checkbox" class="custom-control-input include-short-answer" id="includeShortAnswer{id}">
                            <label class="custom-control-label" for="includeShortAnswer{id}">包含简答题</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
