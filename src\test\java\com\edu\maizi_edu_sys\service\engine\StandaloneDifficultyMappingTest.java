package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class StandaloneDifficultyMappingTest {

    @Test
    @DisplayName("测试难度值映射到难度类别")
    public void testDifficultyMapping() {
        // Test mapping between difficulty values and categories
        testDifficultyMapping(0.1, "easy");
        testDifficultyMapping(0.2, "easy");
        testDifficultyMapping(0.3, "medium");
        testDifficultyMapping(0.4, "medium");
        testDifficultyMapping(0.5, "hard");
    }
    
    @Test
    @DisplayName("测试难度分布百分比计算")
    public void testDifficultyDistributionCalculation() {
        // 创建一个难度分布的测试数据集
        Map<Double, Integer> difficultyCountMap = new HashMap<>();
        difficultyCountMap.put(0.1, 20);  // 20个0.1难度的题
        difficultyCountMap.put(0.3, 50);  // 50个0.3难度的题
        difficultyCountMap.put(0.5, 30);  // 30个0.5难度的题
        
        int totalTopics = 100;
        
        // 根据难度值分类计算分布
        Map<String, Integer> categoryCountMap = new HashMap<>();
        categoryCountMap.put("easy", 0);
        categoryCountMap.put("medium", 0);
        categoryCountMap.put("hard", 0);
        
        for (Map.Entry<Double, Integer> entry : difficultyCountMap.entrySet()) {
            String category = getDifficultyCategory(entry.getKey());
            categoryCountMap.put(category, categoryCountMap.get(category) + entry.getValue());
        }
        
        // 验证分类后的计数
        assertEquals(20, categoryCountMap.get("easy"));
        assertEquals(50, categoryCountMap.get("medium"));
        assertEquals(30, categoryCountMap.get("hard"));
        
        // 验证百分比计算
        Map<String, Double> percentageMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : categoryCountMap.entrySet()) {
            double percentage = (double) entry.getValue() / totalTopics;
            percentageMap.put(entry.getKey(), percentage);
        }
        
        assertEquals(0.2, percentageMap.get("easy"));
        assertEquals(0.5, percentageMap.get("medium"));
        assertEquals(0.3, percentageMap.get("hard"));
        
        // 输出分析结果
        System.out.println("难度分布分析:");
        for (Map.Entry<String, Double> entry : percentageMap.entrySet()) {
            System.out.printf("%s难度: %.1f%% (%d题)\n", 
                    entry.getKey(), entry.getValue() * 100, categoryCountMap.get(entry.getKey()));
        }
    }
    
    private void testDifficultyMapping(double difficultyValue, String expectedCategory) {
        String actualCategory = getDifficultyCategory(difficultyValue);
        System.out.printf("难度值 %.1f 映射到 %s 类别\n", difficultyValue, actualCategory);
        assertEquals(expectedCategory, actualCategory, 
                String.format("难度值 %.1f 应该映射到 %s 类别", difficultyValue, expectedCategory));
    }
    
    // Helper method - should match implementation in GeneticSolver
    private String getDifficultyCategory(double difficulty) {
        if (difficulty <= 0.2) return "easy";
        if (difficulty <= 0.4) return "medium";
        return "hard";
    }
    
    @Test
    @DisplayName("验证难度映射一致性与0.1-0.5范围的覆盖")
    public void verifyDifficultyRangeConsistency() {
        // 验证我们的映射覆盖了0.1-0.5的整个范围
        System.out.println("\n难度值映射覆盖分析:");
        double[] difficultyValues = {0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5};
        
        for (double difficulty : difficultyValues) {
            String category = getDifficultyCategory(difficulty);
            System.out.printf("难度值 %.2f -> %s\n", difficulty, category);
        }
        
        // 确认范围边界值的映射是否正确
        assertEquals("easy", getDifficultyCategory(0.1));
        assertEquals("easy", getDifficultyCategory(0.2));
        assertEquals("medium", getDifficultyCategory(0.21)); // 略高于边界
        assertEquals("medium", getDifficultyCategory(0.4));
        assertEquals("hard", getDifficultyCategory(0.41)); // 略高于边界
        assertEquals("hard", getDifficultyCategory(0.5));
    }
} 