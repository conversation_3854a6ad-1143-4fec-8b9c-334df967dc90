package com.edu.maizi_edu_sys.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import lombok.extern.slf4j.Slf4j;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LaTeX渲染功能测试
 * 测试PDF生成中的LaTeX公式渲染是否正确
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class LatexRenderingTest {

    private PaperGenerationServiceImpl paperGenerationService;

    @BeforeEach
    void setUp() {
        // 创建一个简单的实例用于测试LaTeX渲染功能
        // 由于我们只测试LaTeX渲染，所以其他依赖可以为null
        paperGenerationService = new PaperGenerationServiceImpl(
                null, null, null, null, null, null, null, null, null
        );
    }

    /**
     * 手动测试LaTeX渲染功能
     * 可以在IDE中直接运行此方法来测试
     */
    public static void main(String[] args) {
        LatexRenderingTest test = new LatexRenderingTest();
        test.setUp();

        // 测试集合符号
        String testInput = "设全集$U = \\{1, 2, 3, 4, 5, 6, 7, 8, 9, 10\\}$，集合$A = \\{2, 4, 6, 8\\}$，集合$B = \\{1, 3, 5, 7, 9\\}$，则集合$A \\cup B$ 的补集 $(A \\cup B)'$ 是？";

        System.out.println("=== LaTeX渲染测试 ===");
        System.out.println("输入: " + testInput);

        String result = test.paperGenerationService.testLatexRendering(testInput);

        System.out.println("输出: " + result);
        System.out.println("===================");

        // 验证结果
        boolean hasCorrectBraces = result.contains("{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}");
        boolean hasUnionSymbol = result.contains("∪");
        boolean noLatexSyntax = !result.contains("\\{") && !result.contains("\\cup") && !result.contains("$");

        System.out.println("花括号保留: " + hasCorrectBraces);
        System.out.println("并集符号转换: " + hasUnionSymbol);
        System.out.println("LaTeX语法清理: " + noLatexSyntax);
        System.out.println("测试" + (hasCorrectBraces && hasUnionSymbol && noLatexSyntax ? "通过" : "失败"));
    }

    @Test
    @DisplayName("测试集合符号LaTeX渲染")
    void testSetNotationLatexRendering() {
        // 测试用例：集合表示法
        String input = "设全集$U = \\{1, 2, 3, 4, 5, 6, 7, 8, 9, 10\\}$，集合$A = \\{2, 4, 6, 8\\}$，集合$B = \\{1, 3, 5, 7, 9\\}$，则集合$A \\cup B$ 的补集 $(A \\cup B)'$ 是？";
        String expected = "设全集U = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}，集合A = {2, 4, 6, 8}，集合B = {1, 3, 5, 7, 9}，则集合A ∪ B 的补集 (A ∪ B)' 是？";

        String result = paperGenerationService.testLatexRendering(input);

        log.info("输入: {}", input);
        log.info("期望: {}", expected);
        log.info("实际: {}", result);

        // 验证花括号被正确保留
        assertTrue(result.contains("{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}"), "全集U的花括号应该被保留");
        assertTrue(result.contains("{2, 4, 6, 8}"), "集合A的花括号应该被保留");
        assertTrue(result.contains("{1, 3, 5, 7, 9}"), "集合B的花括号应该被保留");

        // 验证并集符号被正确转换
        assertTrue(result.contains("∪"), "并集符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\{"), "不应该包含LaTeX花括号语法");
        assertFalse(result.contains("\\}"), "不应该包含LaTeX花括号语法");
        assertFalse(result.contains("\\cup"), "不应该包含LaTeX并集语法");
        assertFalse(result.contains("$"), "不应该包含LaTeX公式标记");
    }

    @Test
    @DisplayName("测试其他数学符号LaTeX渲染")
    void testMathSymbolsLatexRendering() {
        String input = "$\\alpha + \\beta = \\gamma$, $x \\in A$, $A \\subset B$, $A \\cap B = \\emptyset$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("数学符号测试 - 输入: {}", input);
        log.info("数学符号测试 - 输出: {}", result);

        // 验证希腊字母
        assertTrue(result.contains("α"), "alpha符号应该被正确转换");
        assertTrue(result.contains("β"), "beta符号应该被正确转换");
        assertTrue(result.contains("γ"), "gamma符号应该被正确转换");

        // 验证集合符号
        assertTrue(result.contains("∈"), "属于符号应该被正确转换");
        assertTrue(result.contains("⊂"), "子集符号应该被正确转换");
        assertTrue(result.contains("∩"), "交集符号应该被正确转换");
        assertTrue(result.contains("∅"), "空集符号应该被正确转换");
    }

    @Test
    @DisplayName("测试分数和根号LaTeX渲染")
    void testFractionAndSqrtLatexRendering() {
        String input = "$\\frac{a}{b} + \\sqrt{x} = \\sqrt[3]{y}$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("分数根号测试 - 输入: {}", input);
        log.info("分数根号测试 - 输出: {}", result);

        // 验证分数转换
        assertTrue(result.contains("(a)/(b)"), "分数应该被转换为(a)/(b)格式");

        // 验证根号转换
        assertTrue(result.contains("√(x)"), "平方根应该被转换");
        assertTrue(result.contains("3√(y)"), "立方根应该被转换");
    }

    @Test
    @DisplayName("测试上下标LaTeX渲染")
    void testSuperscriptSubscriptLatexRendering() {
        String input = "$x^{2} + y_{1} = z^n + w_i$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("上下标测试 - 输入: {}", input);
        log.info("上下标测试 - 输出: {}", result);

        // 验证上下标转换
        assertTrue(result.contains("x^(2)"), "花括号上标应该被转换");
        assertTrue(result.contains("y_(1)"), "花括号下标应该被转换");
        assertTrue(result.contains("z^n"), "单字符上标应该被转换");
        assertTrue(result.contains("w_i"), "单字符下标应该被转换");
    }

    @Test
    @DisplayName("测试混合LaTeX表达式渲染")
    void testMixedLatexRendering() {
        String input = "若$A = \\{x | x^2 \\leq 4\\}$，$B = \\{x | x \\geq 0\\}$，则$A \\cap B = \\{x | 0 \\leq x \\leq 2\\}$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("混合表达式测试 - 输入: {}", input);
        log.info("混合表达式测试 - 输出: {}", result);

        // 验证集合花括号保留
        assertTrue(result.contains("{x | x^2 ≤ 4}"), "集合定义的花括号应该被保留");
        assertTrue(result.contains("{x | x ≥ 0}"), "集合定义的花括号应该被保留");
        assertTrue(result.contains("{x | 0 ≤ x ≤ 2}"), "集合定义的花括号应该被保留");

        // 验证不等号转换
        assertTrue(result.contains("≤"), "小于等于符号应该被正确转换");
        assertTrue(result.contains("≥"), "大于等于符号应该被正确转换");

        // 验证交集符号
        assertTrue(result.contains("∩"), "交集符号应该被正确转换");
    }

    @Test
    @DisplayName("测试逻辑电路LaTeX渲染")
    void testLogicCircuitLatexRendering() {
        String input = "与门的逻辑表达式是$Y = A\\cdot B$，异或门是$Y = A\\oplus B$，非门是$Y=\\overline{A}$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("逻辑电路测试 - 输入: {}", input);
        log.info("逻辑电路测试 - 输出: {}", result);

        // 验证逻辑电路符号
        assertTrue(result.contains("A·B"), "点乘符号应该被正确转换");
        assertTrue(result.contains("A⊕B"), "异或符号应该被正确转换");
        assertTrue(result.contains("A̅"), "上划线（取反）符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\cdot"), "不应该包含LaTeX点乘语法");
        assertFalse(result.contains("\\oplus"), "不应该包含LaTeX异或语法");
        assertFalse(result.contains("\\overline"), "不应该包含LaTeX上划线语法");
        assertFalse(result.contains("$"), "不应该包含LaTeX公式标记");
    }

    @Test
    @DisplayName("测试逻辑运算符LaTeX渲染")
    void testLogicOperatorLatexRendering() {
        String input = "$A \\land B$（逻辑与），$A \\lor B$（逻辑或），$\\neg A$（逻辑非），$A \\rightarrow B$（蕴含）";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("逻辑运算符测试 - 输入: {}", input);
        log.info("逻辑运算符测试 - 输出: {}", result);

        // 验证逻辑运算符
        assertTrue(result.contains("A ∧ B"), "逻辑与符号应该被正确转换");
        assertTrue(result.contains("A ∨ B"), "逻辑或符号应该被正确转换");
        assertTrue(result.contains("¬ A"), "逻辑非符号应该被正确转换");
        assertTrue(result.contains("A → B"), "蕴含符号应该被正确转换");
    }

    @Test
    @DisplayName("测试特殊运算符LaTeX渲染")
    void testSpecialOperatorLatexRendering() {
        String input = "$a \\ast b$，$x \\circ y$，$p \\bullet q$，$m \\diamond n$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("特殊运算符测试 - 输入: {}", input);
        log.info("特殊运算符测试 - 输出: {}", result);

        // 验证特殊运算符
        assertTrue(result.contains("a ∗ b"), "星号运算符应该被正确转换");
        assertTrue(result.contains("x ∘ y"), "圆圈运算符应该被正确转换");
        assertTrue(result.contains("p • q"), "实心圆点运算符应该被正确转换");
        assertTrue(result.contains("m ⋄ n"), "菱形运算符应该被正确转换");
    }

    @Test
    @DisplayName("测试复杂数学函数LaTeX渲染")
    void testComplexMathFunctionLatexRendering() {
        String input = "$k(x)=\\sin(x^2)$，$f(x)=\\cos(2x+1)$，$g(x)=\\log(x^3)$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("复杂数学函数测试 - 输入: {}", input);
        log.info("复杂数学函数测试 - 输出: {}", result);

        // 验证数学函数转换
        assertTrue(result.contains("k(x)=sin(x²)"), "sin函数和平方应该被正确转换");
        assertTrue(result.contains("f(x)=cos(2x+1)"), "cos函数应该被正确转换");
        assertTrue(result.contains("g(x)=log(x³)"), "log函数和立方应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\sin"), "不应该包含LaTeX sin语法");
        assertFalse(result.contains("\\cos"), "不应该包含LaTeX cos语法");
        assertFalse(result.contains("\\log"), "不应该包含LaTeX log语法");
        assertFalse(result.contains("$"), "不应该包含LaTeX公式标记");
    }

    @Test
    @DisplayName("测试复杂分式和根号LaTeX渲染")
    void testComplexFractionAndSqrtLatexRendering() {
        String input = "$\\frac{4\\sqrt{2}}{15}$，$\\frac{a^2+b^2}{c^3}$，$\\sqrt{x^2+y^2}$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("复杂分式根号测试 - 输入: {}", input);
        log.info("复杂分式根号测试 - 输出: {}", result);

        // 验证复杂分式转换
        assertTrue(result.contains("(4√(2))/(15)"), "带根号的分式应该被正确转换");
        assertTrue(result.contains("(a²+b²)/(c³)"), "带平方立方的分式应该被正确转换");
        assertTrue(result.contains("√(x²+y²)"), "复杂根号表达式应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\frac"), "不应该包含LaTeX分式语法");
        assertFalse(result.contains("\\sqrt"), "不应该包含LaTeX根号语法");
    }

    @Test
    @DisplayName("测试平方立方LaTeX渲染")
    void testSquareAndCubeLatexRendering() {
        String input = "$x^2 + y^2 = z^2$，$(a+b)^3 = a^3 + 3a^2b + 3ab^2 + b^3$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("平方立方测试 - 输入: {}", input);
        log.info("平方立方测试 - 输出: {}", result);

        // 验证平方立方转换
        assertTrue(result.contains("x² + y² = z²"), "平方应该被转换为上标符号");
        assertTrue(result.contains("(a+b)³"), "括号表达式的立方应该被正确转换");
        assertTrue(result.contains("a³"), "立方应该被转换为上标符号");
        assertTrue(result.contains("a²b"), "平方应该在复杂表达式中正确转换");
    }

    @Test
    @DisplayName("测试复杂上标下标LaTeX渲染")
    void testComplexSuperscriptSubscriptLatexRendering() {
        String input = "$x^{2n+1}$，$a_{i,j}^{(k)}$，$\\sum_{i=1}^{n} x_i^2$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("复杂上下标测试 - 输入: {}", input);
        log.info("复杂上下标测试 - 输出: {}", result);

        // 验证复杂上下标转换
        assertTrue(result.contains("x^(2n+1)"), "复杂上标表达式应该被正确转换");
        assertTrue(result.contains("a_(i,j)^((k))"), "复杂上下标组合应该被正确转换");
        assertTrue(result.contains("Σ_(i=1)^(n) x_i²"), "求和符号和复杂上下标应该被正确转换");
    }

    @Test
    @DisplayName("测试综合复杂LaTeX表达式渲染")
    void testComprehensiveComplexLatexRendering() {
        String input = "函数$k(x)=\\sin(x^2)$的导数是多少？分式$\\frac{4\\sqrt{2}}{15}$的值约等于多少？集合$A = \\{x | x^2 \\leq 4\\}$与$B = \\{x | x \\geq 0\\}$的交集$A \\cap B$是什么？";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("综合复杂表达式测试 - 输入: {}", input);
        log.info("综合复杂表达式测试 - 输出: {}", result);

        // 验证各种复杂表达式的综合转换
        assertTrue(result.contains("k(x)=sin(x²)"), "数学函数和平方应该被正确转换");
        assertTrue(result.contains("(4√(2))/(15)"), "复杂分式应该被正确转换");
        assertTrue(result.contains("{x | x² ≤ 4}"), "集合定义中的平方和不等号应该被正确转换");
        assertTrue(result.contains("{x | x ≥ 0}"), "集合定义中的不等号应该被正确转换");
        assertTrue(result.contains("A ∩ B"), "交集符号应该被正确转换");

        // 验证花括号保留
        assertTrue(result.contains("{x | x² ≤ 4}"), "集合花括号应该被保留");
        assertTrue(result.contains("{x | x ≥ 0}"), "集合花括号应该被保留");

        // 验证LaTeX语法清理
        assertFalse(result.contains("\\sin"), "不应该包含LaTeX函数语法");
        assertFalse(result.contains("\\frac"), "不应该包含LaTeX分式语法");
        assertFalse(result.contains("\\sqrt"), "不应该包含LaTeX根号语法");
        assertFalse(result.contains("$"), "不应该包含LaTeX公式标记");
    }

    @Test
    @DisplayName("测试几何符号LaTeX渲染")
    void testGeometricSymbolsLatexRendering() {
        String input = "$\\angle ABC$，$\\triangle DEF$，$\\square ABCD$，$\\bigcirc$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("几何符号测试 - 输入: {}", input);
        log.info("几何符号测试 - 输出: {}", result);

        // 验证几何符号转换
        assertTrue(result.contains("∠ ABC"), "角符号应该被正确转换");
        assertTrue(result.contains("△ DEF"), "三角形符号应该被正确转换");
        assertTrue(result.contains("□ ABCD"), "正方形符号应该被正确转换");
        assertTrue(result.contains("○"), "大圆符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\angle"), "不应该包含LaTeX角符号语法");
        assertFalse(result.contains("\\triangle"), "不应该包含LaTeX三角形语法");
        assertFalse(result.contains("\\square"), "不应该包含LaTeX正方形语法");
        assertFalse(result.contains("\\bigcirc"), "不应该包含LaTeX大圆语法");
    }

    @Test
    @DisplayName("测试特殊符号LaTeX渲染")
    void testSpecialSymbolsLatexRendering() {
        String input = "$\\infty$，$\\partial f$，$\\nabla \\cdot \\vec{F}$，$\\clubsuit \\diamondsuit \\heartsuit \\spadesuit$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("特殊符号测试 - 输入: {}", input);
        log.info("特殊符号测试 - 输出: {}", result);

        // 验证特殊符号转换
        assertTrue(result.contains("∞"), "无穷符号应该被正确转换");
        assertTrue(result.contains("∂ f"), "偏微分符号应该被正确转换");
        assertTrue(result.contains("∇ · F"), "梯度符号应该被正确转换");
        assertTrue(result.contains("♣ ♦ ♥ ♠"), "扑克符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\infty"), "不应该包含LaTeX无穷语法");
        assertFalse(result.contains("\\partial"), "不应该包含LaTeX偏微分语法");
        assertFalse(result.contains("\\nabla"), "不应该包含LaTeX梯度语法");
        assertFalse(result.contains("\\clubsuit"), "不应该包含LaTeX扑克语法");
    }

    @Test
    @DisplayName("测试大型运算符LaTeX渲染")
    void testLargeOperatorsLatexRendering() {
        String input = "$\\sum_{i=1}^{n} a_i$，$\\prod_{k=1}^{m} b_k$，$\\bigcup_{i} A_i$，$\\bigcap_{j} B_j$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("大型运算符测试 - 输入: {}", input);
        log.info("大型运算符测试 - 输出: {}", result);

        // 验证大型运算符转换
        assertTrue(result.contains("Σ_(i=1)^(n) a_i"), "求和符号应该被正确转换");
        assertTrue(result.contains("Π_(k=1)^(m) b_k"), "求积符号应该被正确转换");
        assertTrue(result.contains("⋃_i A_i"), "大并集符号应该被正确转换");
        assertTrue(result.contains("⋂_j B_j"), "大交集符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\sum"), "不应该包含LaTeX求和语法");
        assertFalse(result.contains("\\prod"), "不应该包含LaTeX求积语法");
        assertFalse(result.contains("\\bigcup"), "不应该包含LaTeX大并集语法");
        assertFalse(result.contains("\\bigcap"), "不应该包含LaTeX大交集语法");
    }

    @Test
    @DisplayName("测试积分符号LaTeX渲染")
    void testIntegralSymbolsLatexRendering() {
        String input = "$\\int_{a}^{b} f(x)dx$，$\\iint_D f(x,y)dxdy$，$\\oint_C \\vec{F} \\cdot d\\vec{r}$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("积分符号测试 - 输入: {}", input);
        log.info("积分符号测试 - 输出: {}", result);

        // 验证积分符号转换
        assertTrue(result.contains("∫_(a)^(b) f(x)dx"), "定积分符号应该被正确转换");
        assertTrue(result.contains("∬_D f(x,y)dxdy"), "二重积分符号应该被正确转换");
        assertTrue(result.contains("∮_C F · dr"), "环积分符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\int"), "不应该包含LaTeX积分语法");
        assertFalse(result.contains("\\iint"), "不应该包含LaTeX二重积分语法");
        assertFalse(result.contains("\\oint"), "不应该包含LaTeX环积分语法");
    }

    @Test
    @DisplayName("测试数值函数LaTeX渲染")
    void testNumericalFunctionsLatexRendering() {
        String input = "$\\arcsin(x)$，$\\sinh(x)$，$\\max\\{a,b\\}$，$\\det(A)$，$\\gcd(m,n)$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("数值函数测试 - 输入: {}", input);
        log.info("数值函数测试 - 输出: {}", result);

        // 验证数值函数转换
        assertTrue(result.contains("arcsin(x)"), "反正弦函数应该被正确转换");
        assertTrue(result.contains("sinh(x)"), "双曲正弦函数应该被正确转换");
        assertTrue(result.contains("max{a,b}"), "最大值函数应该被正确转换");
        assertTrue(result.contains("det(A)"), "行列式函数应该被正确转换");
        assertTrue(result.contains("gcd(m,n)"), "最大公约数函数应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\arcsin"), "不应该包含LaTeX反三角函数语法");
        assertFalse(result.contains("\\sinh"), "不应该包含LaTeX双曲函数语法");
        assertFalse(result.contains("\\max"), "不应该包含LaTeX最大值语法");
        assertFalse(result.contains("\\det"), "不应该包含LaTeX行列式语法");
        assertFalse(result.contains("\\gcd"), "不应该包含LaTeX最大公约数语法");
    }

    @Test
    @DisplayName("测试二项式系数LaTeX渲染")
    void testBinomialCoefficientsLatexRendering() {
        String input = "$\\binom{n}{k}$，${n \\choose k}$，$C(n,k)$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("二项式系数测试 - 输入: {}", input);
        log.info("二项式系数测试 - 输出: {}", result);

        // 验证二项式系数转换
        assertTrue(result.contains("C(n,k)"), "二项式系数应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\binom"), "不应该包含LaTeX二项式语法");
        assertFalse(result.contains("\\choose"), "不应该包含LaTeX选择语法");
    }

    @Test
    @DisplayName("测试极限表达式LaTeX渲染")
    void testLimitExpressionsLatexRendering() {
        String input = "$\\lim_{x \\to 0} \\frac{\\sin x}{x}$，$\\lim_{n \\to \\infty} a_n$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("极限表达式测试 - 输入: {}", input);
        log.info("极限表达式测试 - 输出: {}", result);

        // 验证极限表达式转换
        assertTrue(result.contains("lim_(x→0)"), "极限符号应该被正确转换");
        assertTrue(result.contains("lim_(n→∞)"), "无穷极限应该被正确转换");
        assertTrue(result.contains("(sin(x))/(x)"), "极限中的分式应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\lim"), "不应该包含LaTeX极限语法");
        assertFalse(result.contains("\\to"), "不应该包含LaTeX箭头语法");
    }

    @Test
    @DisplayName("测试括号符号LaTeX渲染")
    void testBracketSymbolsLatexRendering() {
        String input = "$\\langle a,b \\rangle$，$\\lceil x \\rceil$，$\\lfloor y \\rfloor$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("括号符号测试 - 输入: {}", input);
        log.info("括号符号测试 - 输出: {}", result);

        // 验证括号符号转换
        assertTrue(result.contains("⟨a,b⟩"), "尖括号应该被正确转换");
        assertTrue(result.contains("⌈x⌉"), "上取整符号应该被正确转换");
        assertTrue(result.contains("⌊y⌋"), "下取整符号应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\langle"), "不应该包含LaTeX左尖括号语法");
        assertFalse(result.contains("\\rangle"), "不应该包含LaTeX右尖括号语法");
        assertFalse(result.contains("\\lceil"), "不应该包含LaTeX上取整语法");
        assertFalse(result.contains("\\rceil"), "不应该包含LaTeX上取整语法");
        assertFalse(result.contains("\\lfloor"), "不应该包含LaTeX下取整语法");
        assertFalse(result.contains("\\rfloor"), "不应该包含LaTeX下取整语法");
    }

    @Test
    @DisplayName("测试保留字符LaTeX渲染")
    void testReservedCharactersLatexRendering() {
        String input = "$\\%$，$\\lt$，$\\gt$，$\\&$，$\\#$，$\\_$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("保留字符测试 - 输入: {}", input);
        log.info("保留字符测试 - 输出: {}", result);

        // 验证保留字符转换
        assertTrue(result.contains("%"), "百分号应该被正确转换");
        assertTrue(result.contains("<"), "小于号应该被正确转换");
        assertTrue(result.contains(">"), "大于号应该被正确转换");
        assertTrue(result.contains("&"), "和号应该被正确转换");
        assertTrue(result.contains("#"), "井号应该被正确转换");
        assertTrue(result.contains("_"), "下划线应该被正确转换");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\%"), "不应该包含LaTeX百分号语法");
        assertFalse(result.contains("\\lt"), "不应该包含LaTeX小于语法");
        assertFalse(result.contains("\\gt"), "不应该包含LaTeX大于语法");
        assertFalse(result.contains("\\&"), "不应该包含LaTeX和号语法");
        assertFalse(result.contains("\\#"), "不应该包含LaTeX井号语法");
        assertFalse(result.contains("\\_"), "不应该包含LaTeX下划线语法");
    }

    @Test
    @DisplayName("测试根号修复LaTeX渲染")
    void testSqrtFixLatexRendering() {
        String input = "$4\\sqrt{2}$，$\\sqrt{3}$，$\\frac{4\\sqrt{2}}{15}$，$\\sqrt{x^2+y^2}$";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("根号修复测试 - 输入: {}", input);
        log.info("根号修复测试 - 输出: {}", result);

        // 验证根号修复
        assertTrue(result.contains("4√(2)"), "数字根号应该有完整括号");
        assertTrue(result.contains("√(3)"), "简单根号应该有完整括号");
        assertTrue(result.contains("(4√(2))/(15)"), "分式中的根号应该正确显示");
        assertTrue(result.contains("√(x²+y²)"), "复杂根号表达式应该正确显示");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\sqrt"), "不应该包含LaTeX根号语法");
        assertFalse(result.contains("\\frac"), "不应该包含LaTeX分式语法");
    }

    @Test
    @DisplayName("测试综合修复LaTeX渲染")
    void testComprehensiveFixLatexRendering() {
        String input = "计算$\\frac{4\\sqrt{2}}{15} \\times 100\\%$的值，其中$x \\lt 5$且$y \\gt 3$。";
        String result = paperGenerationService.testLatexRendering(input);

        log.info("综合修复测试 - 输入: {}", input);
        log.info("综合修复测试 - 输出: {}", result);

        // 验证综合修复
        assertTrue(result.contains("(4√(2))/(15) × 100%"), "复杂表达式应该正确渲染");
        assertTrue(result.contains("x < 5"), "小于号应该正确显示");
        assertTrue(result.contains("y > 3"), "大于号应该正确显示");

        // 验证根号括号完整
        assertTrue(result.contains("4√(2)"), "根号应该有完整括号");

        // 验证不包含LaTeX语法
        assertFalse(result.contains("\\frac"), "不应该包含LaTeX分式语法");
        assertFalse(result.contains("\\sqrt"), "不应该包含LaTeX根号语法");
        assertFalse(result.contains("\\%"), "不应该包含LaTeX百分号语法");
        assertFalse(result.contains("\\lt"), "不应该包含LaTeX小于语法");
        assertFalse(result.contains("\\gt"), "不应该包含LaTeX大于语法");
        assertFalse(result.contains("$"), "不应该包含LaTeX公式标记");
    }
}
