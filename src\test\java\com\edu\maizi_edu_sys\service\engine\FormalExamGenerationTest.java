package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FormalExamGenerationTest {

    @InjectMocks
    private GeneticSolver geneticSolver;

    @Mock
    private TopicMapper topicMapper;
    
    @Mock
    private TopicEnhancementDataMapper enhancementDataMapper;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        // 使用反射设置遗传算法参数
        ReflectionTestUtils.setField(geneticSolver, "POPULATION_SIZE", 50);
        ReflectionTestUtils.setField(geneticSolver, "MAX_GENERATIONS", 100);
        ReflectionTestUtils.setField(geneticSolver, "MIN_GENERATIONS", 20);
        ReflectionTestUtils.setField(geneticSolver, "CROSSOVER_RATE", 0.8);
        ReflectionTestUtils.setField(geneticSolver, "MUTATION_RATE", 0.1);
        ReflectionTestUtils.setField(geneticSolver, "TOURNAMENT_SIZE", 5);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_SCORE", 0.4);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_QUALITY", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_DIFFICULTY_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_COGNITIVE_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "EARLY_TERMINATE_THRESHOLD", 0.95);
        
        // 设置模拟返回数据
        setupMockData();
    }
    
    private void setupMockData() {
        // 模拟数据库中有300道题目
        when(topicMapper.countAllTopics()).thenReturn(300);
        
        // 创建一些模拟题目
        List<Topic> mockTopics = createRealisticTestTopics(300);
        
        // 模拟从数据库获取题目
        Topic anyTopic = mockTopics.get(0);
        when(topicMapper.findAnyTopic()).thenReturn(anyTopic);
        
        // 模拟根据知识点ID获取题目
        when(topicMapper.selectFromBakByKnowId(anyInt())).thenReturn(mockTopics);
        
        // 模拟根据ID列表获取题目
        when(topicMapper.selectBatchIds(anyList())).thenReturn(mockTopics);
        
        // 模拟知识点ID列表
        List<Integer> topicIds = mockTopics.stream().map(Topic::getId).collect(Collectors.toList());
        when(topicMapper.findAnyTopicsByKnowledgeId(anyInt())).thenReturn(topicIds);
        
        // 模拟增强数据
        List<TopicEnhancementData> enhancementDataList = new ArrayList<>();
        for (Topic topic : mockTopics) {
            TopicEnhancementData data = new TopicEnhancementData();
            data.setTopicId(topic.getId());
            
            // 根据难度分配认知层次
            String cognitiveLevel;
            double difficulty = topic.getDifficulty();
            if (difficulty <= 0.2) {
                cognitiveLevel = "理解";
            } else if (difficulty <= 0.3) {
                cognitiveLevel = "应用";
            } else if (difficulty <= 0.4) {
                cognitiveLevel = "应用";
            } else if (difficulty <= 0.45) {
                cognitiveLevel = "分析";
            } else {
                cognitiveLevel = "评价";
            }
            data.setCognitiveLevel(cognitiveLevel);
            enhancementDataList.add(data);
        }
        
        when(enhancementDataMapper.selectBatchIds(anyList())).thenReturn(enhancementDataList);
    }

    @Test
    @DisplayName("使用模拟数据库题目生成标准试卷")
    public void generateFormalExamWithMockedData() throws IOException {
        // 从模拟数据库获取题目数据
        List<Topic> allTopics = loadMockedTopicsFromDatabase();
        
        if (allTopics.isEmpty()) {
            System.out.println("模拟数据库中没有题目数据，将使用硬编码模拟数据...");
            allTopics = createRealisticTestTopics(500);
        }
        
        // 打印题库统计信息
        printTopicsInformation(allTopics);
        
        // 设置试卷生成参数
        int targetScore = 100; // 总分100分
        Map<String, Integer> typeScores = new HashMap<>();
        typeScores.put("单选题", 40); // 40个单选题，每题1分，共40分
        typeScores.put("多选题", 30); // 15个多选题，每题2分，共30分
        typeScores.put("判断题", 30); // 30个判断题，每题1分，共30分
        
        Map<String, Double> difficultyDistribution = new HashMap<>();
        difficultyDistribution.put("easy", 0.3);    // 30%简单题
        difficultyDistribution.put("medium", 0.5);  // 50%中等题
        difficultyDistribution.put("hard", 0.2);    // 20%困难题
        
        // 从模拟数据库获取认知层次数据
        Map<Integer, TopicEnhancementData> enhancementDataMap = loadMockedEnhancementDataFromDatabase(allTopics);
        
        // 模拟认知层次分布
        Map<String, Double> cognitiveLevelDistribution = new HashMap<>();
        cognitiveLevelDistribution.put("理解", 0.4); // 40%理解层次题目
        cognitiveLevelDistribution.put("应用", 0.3); // 30%应用层次题目
        cognitiveLevelDistribution.put("分析", 0.2); // 20%分析层次题目
        cognitiveLevelDistribution.put("评价", 0.1); // 10%评价层次题目
        
        // 提取所有知识点ID作为目标覆盖
        List<Integer> targetKnowledgeIds = allTopics.stream()
                                                  .map(Topic::getKnowId)
                                                  .filter(Objects::nonNull)
                                                  .distinct()
                                                  .collect(Collectors.toList());
        if (targetKnowledgeIds.isEmpty() && !allTopics.isEmpty()) {
            // 如果模拟数据中没有有效的knowId，可以添加一个默认的，或确保createRealisticTestTopics总是生成有效的knowId
            System.out.println("Warning: No distinct knowledge IDs found in mock topics for FormalExamGenerationTest. Add default or ensure mock data has knowIds.");
            // targetKnowledgeIds.add(1); // Example if your test logic needs at least one
        }

        // 执行遗传算法
        List<Topic> selectedTopics = geneticSolver.solve(
                allTopics,
                targetScore,
                typeScores,
                difficultyDistribution,
                cognitiveLevelDistribution,
                enhancementDataMap,
                targetKnowledgeIds // Pass the collected/mocked knowledge IDs
        );
        
        // 验证结果
        assertNotNull(selectedTopics, "生成的试卷不能为空");
        assertFalse(selectedTopics.isEmpty(), "生成的试卷题目不能为空");
        
        // 分析总分
        int totalScore = selectedTopics.stream().mapToInt(Topic::getScore).sum();
        System.out.printf("试卷总分: %d (目标: %d)\n", totalScore, targetScore);
        
        // 分析每种题型的数量
        Map<String, Long> typeCount = selectedTopics.stream()
                .collect(Collectors.groupingBy(Topic::getType, Collectors.counting()));
        
        // 分析每种题型的总分
        Map<String, Integer> actualTypeScores = selectedTopics.stream()
                .collect(Collectors.groupingBy(Topic::getType, 
                        Collectors.summingInt(Topic::getScore)));
        
        System.out.println("\n题型分布:");
        typeScores.forEach((type, targetTypeScore) -> {
            long count = typeCount.getOrDefault(type, 0L);
            int actualScore = actualTypeScores.getOrDefault(type, 0);
            System.out.printf("%s: %d题, 目标分数 %d, 实际分数 %d\n", 
                    type, count, targetTypeScore, actualScore);
        });
        
        // 对selectedTopics按题型分组并排序
        Map<String, List<Topic>> topicsByType = selectedTopics.stream()
                .collect(Collectors.groupingBy(Topic::getType));
        
        // 保存到文件
        saveExamToFile(topicsByType, totalScore);
    }
    
    private List<Topic> loadMockedTopicsFromDatabase() {
        List<Topic> allTopics = new ArrayList<>();
        try {
            // 尝试获取题目总数
            int totalTopics = topicMapper.countAllTopics();
            System.out.println("模拟数据库中共有 " + totalTopics + " 道题目");
            
            if (totalTopics > 0) {
                // 先尝试获取任意一道题
                Topic anyTopic = topicMapper.findAnyTopic();
                if (anyTopic != null) {
                    // 得到这道题的知识点ID
                    Integer knowId = anyTopic.getKnowId();
                    if (knowId != null) {
                        // 获取该知识点下的所有题目
                        List<Topic> topics = topicMapper.selectFromBakByKnowId(knowId);
                        if (topics != null && !topics.isEmpty()) {
                            allTopics.addAll(topics);
                            System.out.println("成功从知识点 " + knowId + " 加载了 " + topics.size() + " 道题目");
                        }
                    }
                }
                
                if (allTopics.isEmpty()) {
                    // 如果还是空，尝试从前10个知识点ID中加载题目
                    for (int i = 1; i <= 10; i++) {
                        List<Integer> topicIds = topicMapper.findAnyTopicsByKnowledgeId(i);
                        if (topicIds != null && !topicIds.isEmpty()) {
                            List<Topic> topics = topicMapper.selectBatchIds(topicIds);
                            if (topics != null && !topics.isEmpty()) {
                                allTopics.addAll(topics);
                                System.out.println("成功从知识点 " + i + " 加载了 " + topics.size() + " 道题目");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("从模拟数据库加载题目失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return allTopics;
    }
    
    private Map<Integer, TopicEnhancementData> loadMockedEnhancementDataFromDatabase(List<Topic> topics) {
        Map<Integer, TopicEnhancementData> dataMap = new HashMap<>();
        
        try {
            if (!topics.isEmpty()) {
                // 获取所有题目ID
                List<Integer> topicIds = topics.stream()
                        .map(Topic::getId)
                        .collect(Collectors.toList());
                
                // 批量查询增强数据
                List<TopicEnhancementData> enhancementDataList = enhancementDataMapper.selectBatchIds(topicIds);
                
                if (enhancementDataList != null && !enhancementDataList.isEmpty()) {
                    // 转换为map
                    dataMap = enhancementDataList.stream()
                            .collect(Collectors.toMap(
                                    TopicEnhancementData::getTopicId,
                                    data -> data,
                                    (existing, replacement) -> existing));
                    
                    System.out.println("成功加载了 " + dataMap.size() + " 条题目增强数据");
                }
            }
        } catch (Exception e) {
            System.err.println("从模拟数据库加载题目增强数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 如果数据库中没有足够的认知层次数据，为每个题目创建模拟数据
        if (dataMap.size() < topics.size()) {
            System.out.println("模拟数据库中的增强数据不足，将为剩余题目创建模拟数据...");
            
            for (Topic topic : topics) {
                if (!dataMap.containsKey(topic.getId())) {
                    TopicEnhancementData data = new TopicEnhancementData();
                    data.setTopicId(topic.getId());
                    
                    // 根据难度分配认知层次
                    String cognitiveLevel;
                    double difficulty = topic.getDifficulty();
                    if (difficulty <= 0.2) {
                        cognitiveLevel = "理解";
                    } else if (difficulty <= 0.3) {
                        cognitiveLevel = "应用";
                    } else if (difficulty <= 0.4) {
                        cognitiveLevel = "应用";
                    } else if (difficulty <= 0.45) {
                        cognitiveLevel = "分析";
                    } else {
                        cognitiveLevel = "评价";
                    }
                    data.setCognitiveLevel(cognitiveLevel);
                    dataMap.put(topic.getId(), data);
                }
            }
        }
        
        return dataMap;
    }
    
    private void saveExamToFile(Map<String, List<Topic>> topicsByType, int totalScore) throws IOException {
        // 创建resources/paper目录(如果不存在)
        File paperDir = new File("src/main/resources/paper");
        if (!paperDir.exists()) {
            paperDir.mkdirs();
        }
        
        // 生成文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        File examFile = new File(paperDir, "exam_paper_" + timestamp + ".html");
        
        try (FileWriter writer = new FileWriter(examFile)) {
            // HTML头部
            writer.write("<!DOCTYPE html>\n");
            writer.write("<html lang=\"zh-CN\">\n");
            writer.write("<head>\n");
            writer.write("    <meta charset=\"UTF-8\">\n");
            writer.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
            writer.write("    <title>标准试卷 - " + timestamp + "</title>\n");
            writer.write("    <style>\n");
            writer.write("        body { font-family: SimSun, sans-serif; line-height: 1.5; }\n");
            writer.write("        .exam-header { text-align: center; margin-bottom: 20px; }\n");
            writer.write("        .section { margin-bottom: 20px; }\n");
            writer.write("        .section-title { font-weight: bold; margin-bottom: 10px; }\n");
            writer.write("        .question { margin-bottom: 15px; }\n");
            writer.write("        .question-title { font-weight: normal; }\n");
            writer.write("        .options { margin-left: 20px; }\n");
            writer.write("        .answer-key { border-top: 1px solid #ccc; margin-top: 30px; padding-top: 20px; }\n");
            writer.write("        .hidden { display: none; }\n");
            writer.write("        button { margin: 20px 0; padding: 8px 16px; }\n");
            writer.write("    </style>\n");
            writer.write("</head>\n");
            writer.write("<body>\n");
            
            // 试卷头部
            writer.write("    <div class=\"exam-header\">\n");
            writer.write("        <h1>综合能力测试试卷</h1>\n");
            writer.write("        <p>总分：" + totalScore + "分 &nbsp;&nbsp; 考试时间：120分钟</p>\n");
            writer.write("        <p>日期：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "</p>\n");
            writer.write("    </div>\n");
            
            // 按题型组织试卷内容
            int questionNumber = 1;
            
            // 1. 单选题
            if (topicsByType.containsKey("单选题")) {
                List<Topic> singleChoiceQuestions = topicsByType.get("单选题");
                writer.write("    <div class=\"section\">\n");
                writer.write("        <div class=\"section-title\">一、单项选择题（每题1分，共" + singleChoiceQuestions.size() + "题，共" + 
                        singleChoiceQuestions.stream().mapToInt(Topic::getScore).sum() + "分）</div>\n");
                
                for (Topic topic : singleChoiceQuestions) {
                    writeQuestionToFile(writer, topic, questionNumber++);
                }
                
                writer.write("    </div>\n");
            }
            
            // 2. 多选题
            if (topicsByType.containsKey("多选题")) {
                List<Topic> multipleChoiceQuestions = topicsByType.get("多选题");
                writer.write("    <div class=\"section\">\n");
                writer.write("        <div class=\"section-title\">二、多项选择题（每题2分，共" + multipleChoiceQuestions.size() + "题，共" + 
                        multipleChoiceQuestions.stream().mapToInt(Topic::getScore).sum() + "分）</div>\n");
                
                for (Topic topic : multipleChoiceQuestions) {
                    writeQuestionToFile(writer, topic, questionNumber++);
                }
                
                writer.write("    </div>\n");
            }
            
            // 3. 判断题
            if (topicsByType.containsKey("判断题")) {
                List<Topic> judgmentQuestions = topicsByType.get("判断题");
                writer.write("    <div class=\"section\">\n");
                writer.write("        <div class=\"section-title\">三、判断题（每题1分，共" + judgmentQuestions.size() + "题，共" + 
                        judgmentQuestions.stream().mapToInt(Topic::getScore).sum() + "分）</div>\n");
                
                for (Topic topic : judgmentQuestions) {
                    writeQuestionToFile(writer, topic, questionNumber++);
                }
                
                writer.write("    </div>\n");
            }
            
            // 答案部分
            writer.write("    <button onclick=\"toggleAnswers()\">显示/隐藏答案</button>\n");
            writer.write("    <div id=\"answer-key\" class=\"answer-key hidden\">\n");
            writer.write("        <h2>参考答案与解析</h2>\n");
            
            // 重置问题编号
            questionNumber = 1;
            
            // 添加单选题答案
            if (topicsByType.containsKey("单选题")) {
                writer.write("        <div class=\"section\">\n");
                writer.write("            <div class=\"section-title\">一、单项选择题</div>\n");
                
                for (Topic topic : topicsByType.get("单选题")) {
                    writeAnswerToFile(writer, topic, questionNumber++);
                }
                
                writer.write("        </div>\n");
            }
            
            // 添加多选题答案
            if (topicsByType.containsKey("多选题")) {
                writer.write("        <div class=\"section\">\n");
                writer.write("            <div class=\"section-title\">二、多项选择题</div>\n");
                
                for (Topic topic : topicsByType.get("多选题")) {
                    writeAnswerToFile(writer, topic, questionNumber++);
                }
                
                writer.write("        </div>\n");
            }
            
            // 添加判断题答案
            if (topicsByType.containsKey("判断题")) {
                writer.write("        <div class=\"section\">\n");
                writer.write("            <div class=\"section-title\">三、判断题</div>\n");
                
                for (Topic topic : topicsByType.get("判断题")) {
                    writeAnswerToFile(writer, topic, questionNumber++);
                }
                
                writer.write("        </div>\n");
            }
            
            writer.write("    </div>\n");
            
            // JavaScript用于显示/隐藏答案
            writer.write("    <script>\n");
            writer.write("        function toggleAnswers() {\n");
            writer.write("            var answerKey = document.getElementById('answer-key');\n");
            writer.write("            if (answerKey.classList.contains('hidden')) {\n");
            writer.write("                answerKey.classList.remove('hidden');\n");
            writer.write("            } else {\n");
            writer.write("                answerKey.classList.add('hidden');\n");
            writer.write("            }\n");
            writer.write("        }\n");
            writer.write("    </script>\n");
            
            // HTML尾部
            writer.write("</body>\n");
            writer.write("</html>");
        }
        
        System.out.println("\n试卷已保存至: " + examFile.getAbsolutePath());
    }
    
    private void writeQuestionToFile(FileWriter writer, Topic topic, int questionNumber) throws IOException {
        writer.write("        <div class=\"question\">\n");
        writer.write("            <div class=\"question-title\">" + questionNumber + ". " + topic.getTitle() + "（" + topic.getScore() + "分）</div>\n");
        
        // 写入选项
        if (topic.getType().equals("单选题") || topic.getType().equals("多选题")) {
            writer.write("            <div class=\"options\">\n");
            
            try {
                // 解析选项JSON字符串
                JsonNode optionsNode = objectMapper.readTree(topic.getOptions());
                if (optionsNode.isArray()) {
                    for (JsonNode option : optionsNode) {
                        String key = option.get("key").asText();
                        String name = option.get("name").asText();
                        writer.write("                <div>" + key + ". " + name + "</div>\n");
                    }
                }
            } catch (JsonProcessingException e) {
                // 如果解析失败，创建模拟选项
                writer.write("                <div>A. 选项A内容</div>\n");
                writer.write("                <div>B. 选项B内容</div>\n");
                writer.write("                <div>C. 选项C内容</div>\n");
                writer.write("                <div>D. 选项D内容</div>\n");
            }
            
            writer.write("            </div>\n");
        } else if (topic.getType().equals("判断题")) {
            writer.write("            <div class=\"options\">\n");
            writer.write("                <div>A. 正确</div>\n");
            writer.write("                <div>B. 错误</div>\n");
            writer.write("            </div>\n");
        }
        
        writer.write("        </div>\n");
    }
    
    private void writeAnswerToFile(FileWriter writer, Topic topic, int questionNumber) throws IOException {
        writer.write("            <div class=\"question\">\n");
        writer.write("                <div class=\"question-title\">" + questionNumber + ". 答案：" + topic.getAnswer() + "</div>\n");
        
        if (topic.getParse() != null && !topic.getParse().trim().isEmpty()) {
            writer.write("                <div class=\"explanation\">解析：" + topic.getParse() + "</div>\n");
        } else {
            writer.write("                <div class=\"explanation\">解析：该题暂无解析。</div>\n");
        }
        
        writer.write("            </div>\n");
    }
    
    private void printTopicsInformation(List<Topic> topics) {
        System.out.println("\n============ 测试题目集信息 ============");
        System.out.println("总题目数量: " + topics.size());
        
        // 按难度统计
        Map<Double, Integer> difficultyCountMap = new HashMap<>();
        for (Topic topic : topics) {
            difficultyCountMap.merge(topic.getDifficulty(), 1, Integer::sum);
        }
        
        System.out.println("\n按难度值分布:");
        difficultyCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    double percent = (double) entry.getValue() / topics.size() * 100;
                    System.out.printf("难度 %.1f: %d题 (%.1f%%)\n", 
                            entry.getKey(), entry.getValue(), percent);
                });
        
        // 按难度类别统计
        Map<String, List<Topic>> topicsByCategory = new HashMap<>();
        topicsByCategory.put("easy", new ArrayList<>());
        topicsByCategory.put("medium", new ArrayList<>());
        topicsByCategory.put("hard", new ArrayList<>());
        
        for (Topic topic : topics) {
            String category = getDifficultyCategory(topic.getDifficulty());
            topicsByCategory.get(category).add(topic);
        }
        
        System.out.println("\n按难度类别分布:");
        topicsByCategory.forEach((category, topicsInCategory) -> {
            double percent = (double) topicsInCategory.size() / topics.size() * 100;
            System.out.printf("%s难度: %d题 (%.1f%%)\n", 
                    category, topicsInCategory.size(), percent);
        });
        
        // 按题型统计
        Map<String, Long> typeCountMap = topics.stream()
                .collect(Collectors.groupingBy(Topic::getType, Collectors.counting()));
        
        System.out.println("\n按题型分布:");
        typeCountMap.forEach((type, count) -> {
            double percent = (double) count / topics.size() * 100;
            System.out.printf("%s: %d题 (%.1f%%)\n", type, count, percent);
        });
    }
    
    private List<Topic> createRealisticTestTopics(int count) {
        List<Topic> topics = new ArrayList<>();
        String[] types = {"单选题", "多选题", "判断题"};
        double[] difficulties = {0.1, 0.2, 0.3, 0.4, 0.5};
        
        Random random = new Random(42); // 使用固定种子以保证可重复性
        
        for (int i = 1; i <= count; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            
            // 设置知识点ID (1-10之间的随机值)
            topic.setKnowId(1 + random.nextInt(10));
            
            // 设置随机题型，但保持一定分布
            int typeIndex;
            double typeDist = random.nextDouble();
            if (typeDist < 0.4) {
                typeIndex = 0; // 40%单选题
            } else if (typeDist < 0.7) {
                typeIndex = 1; // 30%多选题
            } else {
                typeIndex = 2; // 30%判断题
            }
            String type = types[typeIndex];
            topic.setType(type);
            
            // 设置随机难度，但遵循一定分布
            double difficultyDist = random.nextDouble();
            double difficulty;
            if (difficultyDist < 0.3) {
                difficulty = difficulties[0]; // 30% 0.1难度
            } else if (difficultyDist < 0.6) {
                difficulty = difficulties[1]; // 30% 0.2难度
            } else if (difficultyDist < 0.8) {
                difficulty = difficulties[2]; // 20% 0.3难度
            } else if (difficultyDist < 0.9) {
                difficulty = difficulties[3]; // 10% 0.4难度
            } else {
                difficulty = difficulties[4]; // 10% 0.5难度
            }
            topic.setDifficulty(difficulty);
            
            // 设置分值
            if (type.equals("单选题")) {
                topic.setScore(1); // 单选题1分
            } else if (type.equals("多选题")) {
                topic.setScore(2); // 多选题2分
            } else { // 判断题
                topic.setScore(1); // 判断题1分
            }
            
            // 设置标题
            topic.setTitle("这是第" + i + "道" + type + "，难度为" + difficulty + "。考察知识点" + topic.getKnowId() + "的内容。");
            
            // 设置选项
            if (type.equals("单选题")) {
                topic.setOptions(createSingleChoiceOptions());
                topic.setAnswer(getSingleChoiceAnswer());
            } else if (type.equals("多选题")) {
                topic.setOptions(createMultipleChoiceOptions());
                topic.setAnswer(getMultipleChoiceAnswer());
            } else { // 判断题
                topic.setOptions("[]"); // 判断题没有选项
                topic.setAnswer(random.nextBoolean() ? "A" : "B"); // A表示正确，B表示错误
            }
            
            // 设置解析
            topic.setParse("这道题的难度是" + difficulty + "。" + 
                    "这道题主要考察了知识点" + topic.getKnowId() + "，" + 
                    "属于" + getDifficultyCategory(difficulty) + "难度题目。" +
                    "答案是" + topic.getAnswer() + "，解析...");
            
            // 设置来源
            topic.setSource("模拟测试题库");
            
            topics.add(topic);
        }
        
        return topics;
    }
    
    private String createSingleChoiceOptions() {
        return "[{\"key\":\"A\",\"name\":\"选项A内容\"},{\"key\":\"B\",\"name\":\"选项B内容\"}," +
                "{\"key\":\"C\",\"name\":\"选项C内容\"},{\"key\":\"D\",\"name\":\"选项D内容\"}]";
    }
    
    private String getSingleChoiceAnswer() {
        String[] possibleAnswers = {"A", "B", "C", "D"};
        return possibleAnswers[new Random().nextInt(possibleAnswers.length)];
    }
    
    private String createMultipleChoiceOptions() {
        return "[{\"key\":\"A\",\"name\":\"选项A内容\"},{\"key\":\"B\",\"name\":\"选项B内容\"}," +
                "{\"key\":\"C\",\"name\":\"选项C内容\"},{\"key\":\"D\",\"name\":\"选项D内容\"}," +
                "{\"key\":\"E\",\"name\":\"选项E内容\"}]";
    }
    
    private String getMultipleChoiceAnswer() {
        String[] possibleAnswers = {"AB", "ABC", "ABCD", "ABCDE", "AC", "ACD", "ACE", "AD", "AE", "BC", "BCE", "BD", "BE", "CD", "CE", "DE"};
        return possibleAnswers[new Random().nextInt(possibleAnswers.length)];
    }
    
    // Helper method for difficulty categorization - must match GeneticSolver implementation
    private String getDifficultyCategory(double difficulty) {
        if (difficulty <= 0.2) return "easy";
        if (difficulty <= 0.4) return "medium";
        return "hard";
    }
} 