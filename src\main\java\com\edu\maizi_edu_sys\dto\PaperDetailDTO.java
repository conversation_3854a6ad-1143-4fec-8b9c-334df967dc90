package com.edu.maizi_edu_sys.dto;

import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 试卷详情DTO
 */
@Data
public class PaperDetailDTO {

    /**
     * 试卷ID
     */
    private Long id;

    /**
     * 试卷标题
     */
    private String title;

    /**
     * 知识点ID
     */
    private Integer knowledgeId;

    /**
     * 知识点名称
     */
    private String knowledgeName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 试卷总分
     */
    private Integer totalScore;

    /**
     * 实际总分
     */
    private Integer actualTotalScore;

    /**
     * 试卷难度
     */
    private Double difficulty;

    /**
     * 题目列表
     */
    private List<Topic> topics;

    /**
     * 按题型分组的题目
     */
    private Map<String, List<Topic>> topicsByType;

    /**
     * 各题型题目数量
     */
    private Map<String, Integer> typeCountMap;

    /**
     * 各题型分数统计
     */
    private Map<String, Integer> typeScoreMap;

    /**
     * 试卷类型 (0-普通试卷, 1-教师试卷, 2-标准试卷)
     */
    private Integer type;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 试卷难度分布 (e.g., {"1": 0.6, "2": 0.3, "3": 0.1})
     */
    private Map<String, Double> difficultyDistribution;

    /**
     * 构造函数，从Paper实体转换
     */
    public static PaperDetailDTO fromEntity(Paper paper, List<Topic> topics) {
        PaperDetailDTO dto = new PaperDetailDTO();
        dto.setId(paper.getId());
        dto.setTitle(paper.getTitle());
        dto.setKnowledgeId(paper.getKnowledgeId());
        dto.setKnowledgeName(paper.getKnowledgeName());
        dto.setCreateTime(paper.getCreateTime());
        dto.setTotalScore(paper.getTotalScore());
        dto.setActualTotalScore(paper.getActualTotalScore());
        dto.setDifficulty(paper.getDifficulty());
        dto.setTopics(topics);
        dto.setType(paper.getType()); // Uses Paper.getType() which handles conversion
        dto.setUpdatedAt(paper.getUpdateTime());
        // difficultyDistribution will be set in the service layer
        
        return dto;
    }
} 