package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 自由组卷请求DTO
 * 与PaperGenerationRequest相似但更强调知识点粒度控制
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomPaperRequest {
    /**
     * 试卷标题
     */
    @NotBlank(message = "试卷标题不能为空")
    private String title;

    /**
     * 试卷类型:
     * 0 - 普通试卷
     * 1 - 教师专用
     * 2 - 标准考试
     */
    @NotNull(message = "试卷类型不能为空")
    private Integer type;

    /**
     * 题型-分值映射
     * 例如：{"SINGLE_CHOICE": 3, "MULTIPLE_CHOICE": 4, "JUDGMENT": 2, "SHORT_ANSWER": 10}
     */
    @NotNull(message = "题型分值映射不能为空")
    private Map<String, Integer> typeScoreMap;

    /**
     * 难度分布（百分比）
     * 例如：{"easy": 0.3, "medium": 0.5, "hard": 0.2}
     * 注意：百分比之和需为1.0
     */
    @NotNull(message = "难度分布不能为空")
    private Map<String, Double> difficultyDistribution;

    /**
     * 知识点配置列表
     * 每个知识点单独指定题目数量和是否包含简答题
     */
    @NotEmpty(message = "知识点配置不能为空")
    @Valid
    private List<KnowledgePointConfigRequest> knowledgePointConfigs;
} 