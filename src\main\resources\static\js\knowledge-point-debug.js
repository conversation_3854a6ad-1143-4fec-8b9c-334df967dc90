/**
 * 知识点调试工具
 * 用于诊断知识点名称显示问题
 */

// 调试函数：检查知识点数据完整性
function debugKnowledgePointData() {
    console.log('=== 知识点数据完整性检查 ===');

    console.log('1. 全局选择状态:');
    if (selectedKnowledgePoints.size === 0) {
        console.log('   ❌ 没有选择的知识点');
        return;
    }

    selectedKnowledgePoints.forEach((pointData, pointId) => {
        console.log(`   知识点 ${pointId}:`);
        console.log(`     - 名称: "${pointData.name}"`);
        console.log(`     - ID: ${pointData.id}`);
        console.log(`     - knowId: ${pointData.knowId}`);
        console.log(`     - 题目数量: ${pointData.topicCount}`);
        console.log(`     - 是否免费: ${pointData.isFree}`);

        // 检查名称是否为默认格式
        if (pointData.name && pointData.name.includes('#')) {
            console.warn(`     ⚠️ 名称可能不完整: ${pointData.name}`);
        }

        // 检查knowId是否存在
        if (!pointData.knowId) {
            console.warn(`     ⚠️ 缺少knowId`);
        }
    });

    console.log('\n2. 页面上的知识点信息:');
    $('.knowledge-checkbox:checked').each(function() {
        const checkbox = $(this);
        const id = checkbox.val();
        const card = checkbox.closest('.card');
        const name = card.find('.card-title').text().trim();
        const badges = card.find('.badge').map(function() {
            return $(this).text().trim();
        }).get();

        console.log(`   页面知识点 ${id}:`);
        console.log(`     - 名称: "${name}"`);
        console.log(`     - 标签: [${badges.join(', ')}]`);

        // 检查是否在全局状态中
        if (selectedKnowledgePoints.has(id)) {
            const globalData = selectedKnowledgePoints.get(id);
            if (globalData.name !== name) {
                console.warn(`     ⚠️ 名称不匹配: 全局="${globalData.name}", 页面="${name}"`);
            }
        } else {
            console.warn(`     ⚠️ 在全局状态中未找到`);
        }
    });
}

// 调试函数：检查试卷配置生成过程
function debugPaperConfigGeneration() {
    console.log('\n=== 试卷配置生成过程检查 ===');

    if (selectedKnowledgePoints.size === 0) {
        console.log('❌ 没有选择的知识点，无法生成配置');
        return;
    }

    console.log('1. 生成知识点配置:');
    const knowledgeConfigs = [];
    const processedIds = new Set();

    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        const knowledgeId = parseInt(pointId);

        if (processedIds.has(knowledgeId)) {
            console.warn(`   ⚠️ 跳过重复的知识点ID: ${knowledgeId}`);
            return;
        }
        processedIds.add(knowledgeId);

        const knowId = pointData.knowId || knowledgeId;

        console.log(`   配置知识点: ${pointData.name}`);
        console.log(`     - pointId: ${pointId}`);
        console.log(`     - knowledgeId: ${knowledgeId}`);
        console.log(`     - knowId: ${knowId}`);

        knowledgeConfigs.push({
            knowledgeId: knowId,
            questionCount: 5,
            includeShortAnswer: false // 默认关闭简答题开关
        });
    });

    console.log(`\n2. 生成的配置数量: ${knowledgeConfigs.length}`);
    console.log('3. 配置详情:', knowledgeConfigs);

    return knowledgeConfigs;
}

// 调试函数：模拟知识点配置渲染
function debugKnowledgePointConfigRendering(knowledgeConfigs) {
    console.log('\n=== 知识点配置渲染过程检查 ===');

    if (!knowledgeConfigs) {
        knowledgeConfigs = debugPaperConfigGeneration();
    }

    if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
        console.log('❌ 没有知识点配置可渲染');
        return;
    }

    console.log('1. 建立知识点信息映射:');
    const knowledgeInfo = {};

    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        const info = {
            name: pointData.name,
            topicCount: 0,
            originalId: pointId,
            knowId: pointData.knowId || pointId
        };

        // 解析题目数量
        const topicCountText = pointData.topicCount || '';
        if (typeof topicCountText === 'string') {
            if (topicCountText.includes('题目:')) {
                info.topicCount = parseInt(topicCountText.replace('题目:', '').trim()) || 0;
            } else if (topicCountText.includes('题')) {
                info.topicCount = parseInt(topicCountText.replace('题', '').trim()) || 0;
            } else {
                info.topicCount = parseInt(topicCountText) || 0;
            }
        }

        knowledgeInfo[pointId] = info;
        knowledgeInfo[pointData.knowId || pointId] = info;

        console.log(`   映射: ${pointData.name}`);
        console.log(`     - pointId映射: ${pointId} -> ${info.name}`);
        console.log(`     - knowId映射: ${pointData.knowId || pointId} -> ${info.name}`);
    });

    console.log('\n2. 渲染每个知识点配置:');
    knowledgeConfigs.forEach(function(config, index) {
        const knowledgeId = config.knowledgeId;

        console.log(`   渲染配置 ${index + 1}: knowledgeId=${knowledgeId}`);

        // 查找知识点信息
        let info = knowledgeInfo[knowledgeId];

        if (!info) {
            console.log(`     ❌ 直接查找失败，尝试遍历查找`);
            Object.keys(knowledgeInfo).forEach(key => {
                const keyInfo = knowledgeInfo[key];
                if (!info && (
                    key == knowledgeId ||
                    keyInfo.knowId == knowledgeId ||
                    keyInfo.originalId == knowledgeId
                )) {
                    info = keyInfo;
                    console.log(`     ✅ 通过遍历找到: ${keyInfo.name} (key: ${key})`);
                }
            });
        } else {
            console.log(`     ✅ 直接找到: ${info.name}`);
        }

        if (!info) {
            console.log(`     ❌ 无法找到知识点信息，将显示默认名称`);
            info = {
                name: `知识点 #${knowledgeId}`,
                topicCount: 0
            };
        }

        console.log(`     最终显示: "${info.name}" (题库: ${info.topicCount}题)`);
    });
}

// 调试函数：检查知识点选择过程
function debugKnowledgePointSelection() {
    console.log('\n=== 知识点选择过程检查 ===');

    const checkboxes = $('.knowledge-checkbox');
    console.log(`页面上共有 ${checkboxes.length} 个知识点复选框`);

    checkboxes.each(function(index) {
        const checkbox = $(this);
        const id = checkbox.val();
        const isChecked = checkbox.prop('checked');
        const card = checkbox.closest('.card');
        const name = card.find('.card-title').text().trim();
        const badges = card.find('.badge').map(function() {
            return $(this).text().trim();
        }).get();

        console.log(`   知识点 ${index + 1}: ID=${id}, 选中=${isChecked}`);
        console.log(`     - 名称: "${name}"`);
        console.log(`     - 标签: [${badges.join(', ')}]`);

        if (isChecked) {
            const globalData = selectedKnowledgePoints.get(id);
            if (globalData) {
                console.log(`     - 全局数据: ${JSON.stringify(globalData)}`);
            } else {
                console.warn(`     ⚠️ 已选中但在全局状态中未找到`);
            }
        }
    });
}

// 主调试函数
function runKnowledgePointDiagnostics() {
    console.log('🔍 开始知识点诊断...');

    debugKnowledgePointSelection();
    debugKnowledgePointData();

    const configs = debugPaperConfigGeneration();
    debugKnowledgePointConfigRendering(configs);

    console.log('\n📋 诊断建议:');

    if (selectedKnowledgePoints.size === 0) {
        console.log('1. 请先选择一些知识点');
        return;
    }

    let hasNameIssues = false;
    selectedKnowledgePoints.forEach((pointData, pointId) => {
        if (!pointData.name || pointData.name.includes('#')) {
            hasNameIssues = true;
        }
    });

    if (hasNameIssues) {
        console.log('1. ❌ 发现知识点名称问题:');
        console.log('   - 检查知识点选择时是否正确获取了名称');
        console.log('   - 检查页面HTML结构是否正确');
        console.log('   - 检查.card-title元素是否包含正确的知识点名称');
    } else {
        console.log('1. ✅ 知识点名称正常');
    }

    const hasKnowIdIssues = Array.from(selectedKnowledgePoints.values()).some(data => !data.knowId);
    if (hasKnowIdIssues) {
        console.log('2. ❌ 发现knowId问题:');
        console.log('   - 检查知识点选择时是否正确获取了knowId');
        console.log('   - 检查.badge-secondary元素是否包含"知识点ID:"信息');
    } else {
        console.log('2. ✅ knowId正常');
    }

    console.log('\n🎯 诊断完成');
}

// 修复函数：尝试修复知识点名称
function fixKnowledgePointNames() {
    console.log('🔧 尝试修复知识点名称...');

    let fixedCount = 0;

    selectedKnowledgePoints.forEach((pointData, pointId) => {
        if (!pointData.name || pointData.name.includes('#')) {
            // 尝试从页面获取正确的名称
            const checkbox = $(`#kp-${pointId}`);
            if (checkbox.length > 0) {
                const card = checkbox.closest('.card');
                const name = card.find('.card-title').text().trim();

                if (name && !name.includes('#')) {
                    pointData.name = name;
                    selectedKnowledgePoints.set(pointId, pointData);
                    fixedCount++;
                    console.log(`✅ 修复知识点名称: ${pointId} -> "${name}"`);
                } else {
                    console.log(`❌ 无法修复知识点名称: ${pointId} (页面名称: "${name}")`);
                }
            } else {
                console.log(`❌ 无法修复知识点名称: ${pointId} (页面上未找到)`);
            }
        }
    });

    if (fixedCount > 0) {
        console.log(`🎉 成功修复 ${fixedCount} 个知识点名称`);
        // 更新显示
        if (typeof updateSelectedKnowledgePointsDisplay === 'function') {
            updateSelectedKnowledgePointsDisplay();
        }
    } else {
        console.log('ℹ️ 没有需要修复的知识点名称');
    }
}

// 导出调试函数
window.debugKnowledgePointData = debugKnowledgePointData;
window.debugPaperConfigGeneration = debugPaperConfigGeneration;
window.debugKnowledgePointConfigRendering = debugKnowledgePointConfigRendering;
window.debugKnowledgePointSelection = debugKnowledgePointSelection;
window.runKnowledgePointDiagnostics = runKnowledgePointDiagnostics;
window.fixKnowledgePointNames = fixKnowledgePointNames;

// 知识点调试工具已加载 - 静默模式
// 可用的调试函数: runKnowledgePointDiagnostics(), fixKnowledgePointNames(), debugKnowledgePointData(), debugKnowledgePointSelection()
// 使用方法: 在控制台运行相应的调试函数
